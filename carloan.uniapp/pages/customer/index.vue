<template>
	<!-- 客户管理 -->
	<view class="customer-management">
		<!-- 分页列表 -->
		<z-paging 
			ref="paging" 
			v-model="dataList" 
			@query="queryList"
			:refresher-enabled="true"
			:loading-more-enabled="true"
			:empty-view-text="emptyText"
			:empty-view-img="emptyImg"
		>
			<!-- 顶部固定内容 -->
			<template #top>
				<navbar title="客户管理" />
				<view class="filter">
					<!-- 搜索区域 -->
					<view class="search-section">
						<view class="search-box">
							<image
								class="search-icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-search.png"
							/>
							<input
								class="search-input"
								v-model="searchKeyword"
								placeholder="搜索客户姓名或手机号"
								@input="onSearchInput"
							/>
						</view>
						<picker 
							:value="statusIndex" 
							:range="statusOptions" 
							range-key="label"
							@change="handleStatusChange"
						>
							<view class="filter-item">
								<text>{{ statusOptions[statusIndex].label }}</text>
								<wd-icon name="arrow-down" size="22px"></wd-icon>
							</view>
						</picker>
					</view>
					
					<!-- 新增按钮 -->
					<view class="add-section">
						<button class="btn btn-primary" @click="goCreateCustomer">
							<image
								class="btn-icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-add-white.png"
							/>
							新增客户
						</button>
					</view>
				</view>
			</template>

			<!-- 列表内容 -->
			<view class="list">
				<view class="customer-card" v-for="item in dataList" :key="item.id" @click="onItemClick(item)">
					<view class="card-header">
						<view class="customer-info">
							<view class="customer-name">{{ item.name || '未录入姓名' }}</view>
							<view class="customer-phone">{{ item.phone }}</view>
						</view>
						<view class="status-tag" :class="getStatusClass(item.status)">
							{{ item.status_text }}
						</view>
					</view>
					<view class="card-content">
						<view class="info-row" v-if="item.id_card">
							<text class="label">身份证号：</text>
							<text class="value">{{ formatIdCard(item.id_card) }}</text>
						</view>
						<view class="info-row" v-if="item.gender">
							<text class="label">性别：</text>
							<text class="value">{{ item.gender_text }}</text>
						</view>
						<view class="info-row">
							<text class="label">创建时间：</text>
							<text class="value">{{ formatDate(item.created_at) }}</text>
						</view>
					</view>
					<view class="card-footer">
						<view class="action-row">
							<button class="btn btn-edit" @click.stop="onEditCustomer(item)">
								编辑
							</button>
							<button class="btn btn-delete" @click.stop="onDeleteCustomer(item)">
								删除
							</button>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const dataList = ref([]);
const isInitialLoading = ref(true);
const searchKeyword = ref('');
const searchTimer = ref(null);

// 状态选项
const statusOptions = ref([
	{ label: '全部状态', value: '' },
	{ label: '正常', value: 'active' },
	{ label: '停用', value: 'inactive' },
	{ label: '黑名单', value: 'blacklist' }
]);
const statusIndex = ref(0);

// 空状态配置
const emptyText = computed(() => {
	if (isInitialLoading.value) {
		return '加载中...';
	}
	return '暂无客户数据';
});

const emptyImg = 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/empty.png';

// 分页组件引用
const paging = ref(null);

// 页面显示时刷新数据
onShow(() => {
	if (paging.value) {
		paging.value.reload();
	}
});

// 查询列表数据
const queryList = async (pageNo, pageSize) => {
	try {
		const params = {
			page: pageNo,
			page_size: pageSize,
			keyword: searchKeyword.value,
			status: statusOptions.value[statusIndex.value].value
		};
		
		const response = await request.get('/customers', params);
		
		// 添加调试信息
		console.log('客户管理API Response:', response);
		
		// 根据实际API格式判断成功状态
		let isSuccess = false;
		let list = [];
		
		if (response && response.status_code === 200) {
			// 正确的响应格式: { status_code: 200, message: "...", data: { list: [], pagination: {} } }
			isSuccess = true;
			list = response.data?.list || [];
		}
		
		console.log('客户管理处理后数据:', { isSuccess, list });
		
		if (isSuccess) {
			// 确保 list 是数组
			const dataList = Array.isArray(list) ? list : [];
			
			console.log('客户管理最终数据:', dataList);
			
			// 完成分页加载
			if (paging.value) {
				paging.value.complete(dataList);
				
				// 标记初始加载完成
				isInitialLoading.value = false;
			}
		} else {
			console.error('客户管理API调用失败:', response);
			isInitialLoading.value = false;
			if (paging.value) {
				paging.value.complete(false);
			}
			// 显示错误信息
			const errorMessage = response?.message || '获取数据失败';
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('查询客户列表失败:', error);
		isInitialLoading.value = false;
		if (paging.value) {
			paging.value.complete(false);
		}
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		});
	}
};

// 搜索输入处理
const onSearchInput = () => {
	// 清除之前的定时器
	if (searchTimer.value) {
		clearTimeout(searchTimer.value);
	}
	
	// 设置新的定时器，防抖搜索
	searchTimer.value = setTimeout(() => {
		if (paging.value) {
			paging.value.reload();
		}
	}, 500);
};

// 状态切换处理
const handleStatusChange = (e) => {
	statusIndex.value = e.detail.value;
	if (paging.value) {
		paging.value.reload();
	}
};

// 获取状态样式类
const getStatusClass = (status) => {
	switch (status) {
		case 'active':
			return 'active';
		case 'inactive':
			return 'inactive';
		case 'blacklist':
			return 'blacklist';
		default:
			return 'inactive';
	}
};

// 格式化身份证号（隐藏中间部分）
const formatIdCard = (idCard) => {
	if (!idCard) return '';
	if (idCard.length !== 18) return idCard;
	return idCard.substring(0, 6) + '********' + idCard.substring(14);
};

// 格式化日期
const formatDate = (dateStr) => {
	if (!dateStr) return '';
	const date = new Date(dateStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};

// 点击客户卡片
const onItemClick = (item) => {
	uni.navigateTo({
		url: `/pages/customer/detail?id=${item.id}`
	});
};

// 新增客户
const goCreateCustomer = () => {
	uni.navigateTo({
		url: '/pages/customer/create'
	});
};

// 编辑客户
const onEditCustomer = (item) => {
	uni.navigateTo({
		url: `/pages/customer/create?id=${item.id}`
	});
};

// 删除客户
const onDeleteCustomer = (item) => {
	uni.showModal({
		title: '确认删除',
		content: `确定要删除客户「${item.name || item.phone}」吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					const response = await request.delete(`/customers/${item.id}`);
					if (response.status_code === 200) {
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						// 刷新列表
						if (paging.value) {
							paging.value.reload();
						}
					} else {
						uni.showToast({
							title: response.message || '删除失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('删除客户失败:', error);
					uni.showToast({
						title: '删除失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	});
};
</script>

<style lang="scss" scoped>
.customer-management {
	height: 100vh;
	background-color: #f5f5f5;
}

.filter {
	background: #ffffff;
	padding: 20rpx;
	border-bottom: 1px solid #f0f0f0;
	
	.search-section {
		display: flex;
		align-items: center;
		gap: 20rpx;
		margin-bottom: 20rpx;
		
		.search-box {
			flex: 1;
			position: relative;
			background: #f8f8f8;
			border-radius: 12rpx;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			height: 72rpx;
			
			.search-icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 16rpx;
			}
			
			.search-input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				
				&::placeholder {
					color: #999;
				}
			}
		}
		
		.filter-item {
			display: flex;
			align-items: center;
			padding: 16rpx 24rpx;
			background: #f8f8f8;
			border-radius: 12rpx;
			font-size: 28rpx;
			color: #333;
			white-space: nowrap;
			
			:deep(.wd-icon) {
				margin-left: 8rpx;
			}
		}
	}
	
	.add-section {
		.btn {
			&.btn-primary {
				background: #000000;
				color: #ffffff;
				border-radius: 12rpx;
				border: none;
				padding: 20rpx 32rpx;
				font-size: 28rpx;
				font-weight: 500;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.btn-icon {
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
				}
			}
		}
	}
}

.list {
	padding: 20rpx;
}

.customer-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 24rpx;
		
		.customer-info {
			flex: 1;
			
			.customer-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.customer-phone {
				font-size: 28rpx;
				color: #666;
			}
		}
		
		.status-tag {
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			font-weight: 500;
			white-space: nowrap;
			
			&.active {
				background: #E8F5E8;
				color: #52C41A;
			}
			
			&.inactive {
				background: #F5F5F5;
				color: #999;
			}
			
			&.blacklist {
				background: #FFF2F0;
				color: #FF4D4F;
			}
		}
	}
	
	.card-content {
		margin-bottom: 24rpx;
		
		.info-row {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
			font-size: 28rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				color: #999;
				margin-right: 8rpx;
			}
			
			.value {
				color: #333;
			}
		}
	}
	
	.card-footer {
		.action-row {
			display: flex;
			gap: 20rpx;
			
							.btn {
					flex: 1;
					padding: 12rpx 0;
					border-radius: 8rpx;
					font-size: 28rpx;
					text-align: center;
					border: none;
					
					&.btn-edit {
						background: #000000;
						color: #ffffff;
					}
					
					&.btn-delete {
						background: #c41e3a;
						color: #ffffff;
					}
				}
		}
	}
}
</style> 