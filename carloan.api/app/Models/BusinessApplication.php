<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class BusinessApplication extends BaseModel
{
    use SoftDeletes;

    protected $table = 'business_applications';

    protected $fillable = [
        'application_no',
        'user_id',
        'business_type',
        'is_new_car_business',
        'customer_id',
        'channel_code',
        'customer_name',
        'customer_phone',
        'customer_id_card',
        'product_id',
        'product_name',
        'loan_amount',
        'loan_period',
        'interest_rate',
        'vehicle_brand',
        'vehicle_model',
        'vehicle_vin',
        'vehicle_year',
        'vehicle_price',
        'status',
        'status_text',
        'approval_notes',
        'approval_history',
        'submit_time',
        'approval_time',
        'need_supplement',
        'supplement_reason',
        'supplement_deadline',
        'customer_data',
        'risk_assessment',
        'attachments',
        'contacts',
        'assets',
        'liabilities',
        'vehicle'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'product_id' => 'integer',
        'business_type' => 'integer',
        'is_new_car_business' => 'boolean',
        'approval_history' => 'array',
        'customer_data' => 'array',
        'risk_assessment' => 'array',
        'attachments' => 'array',
        'contacts' => 'array',
        'assets' => 'array',
        'liabilities' => 'array',
        'vehicle' => 'array',
        'loan_amount' => 'decimal:2',
        'interest_rate' => 'decimal:4',
        'vehicle_price' => 'decimal:2',
        'need_supplement' => 'boolean',
        'submit_time' => 'datetime',
        'approval_time' => 'datetime',
        'supplement_deadline' => 'datetime'
    ];

    protected $dates = [
        'submit_time',
        'approval_time',
        'supplement_deadline',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 业务类型常量
    const BUSINESS_TYPE_CAR_LOAN = 1;        // 车抵贷
    const BUSINESS_TYPE_LEASE_TO_OWN = 2;    // 以租代购

    // 业务状态常量
    const STATUS_SUBMITTED = 'submitted';           // 已提交
    const STATUS_INITIAL_REVIEW = 'initial_review'; // 初审中
    const STATUS_PRE_APPROVAL = 'pre_approval';     // 预审通过
    const STATUS_INTERVIEW_PENDING = 'interview_pending'; // 待面审
    const STATUS_INTERVIEW_SCHEDULED = 'interview_scheduled'; // 已预约面审
    const STATUS_INTERVIEW_COMPLETED = 'interview_completed'; // 面审完成
    const STATUS_FINAL_REVIEW = 'final_review';     // 终审中
    const STATUS_SECONDARY_REVIEW = 'secondary_review'; // 复审中
    const STATUS_CONTRACT_PENDING = 'contract_pending'; // 待签约
    const STATUS_CONTRACT_PROCESSING = 'contract_processing'; // 签约中
    const STATUS_CONTRACT_COMPLETED = 'contract_completed'; // 签约完成
    const STATUS_APPROVED = 'approved';             // 审批通过
    const STATUS_REJECTED = 'rejected';             // 审批拒绝
    const STATUS_SUPPLEMENT_REQUIRED = 'supplement_required'; // 需要补件

    /**
     * 关联业务员
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联金融产品
     */
    public function financialProduct()
    {
        return $this->belongsTo(FinancialProduct::class, 'product_id');
    }

    /**
     * 关联渠道
     */
    public function channel()
    {
        return $this->belongsTo(Channel::class, 'channel_code', 'code');
    }

    /**
     * 关联业务附件
     */
    public function businessAttachments()
    {
        return $this->hasMany(BusinessAttachment::class, 'business_application_id');
    }

    /**
     * 关联客户补件
     */
    public function supplements()
    {
        return $this->hasMany(CustomerSupplement::class, 'application_id');
    }

    /**
     * 关联审批任务
     */
    public function approvalTasks()
    {
        return $this->hasMany(ApprovalTask::class, 'application_id');
    }

    /**
     * 关联抄送记录
     */
    public function approvalCcRecords()
    {
        return $this->hasMany(ApprovalCcRecord::class, 'application_id');
    }

    /**
     * 关联合同记录
     */
    public function contracts()
    {
        return $this->hasMany(BusinessApplicationContract::class, 'application_id');
    }

    /**
     * 获取当前合同
     */
    public function currentContract()
    {
        return $this->hasOne(BusinessApplicationContract::class, 'application_id')
            ->latest();
    }

    /**
     * 获取有效合同(已生成或签署中的合同)
     */
    public function activeContract()
    {
        return $this->hasOne(BusinessApplicationContract::class, 'application_id')
            ->whereIn('contract_status', [
                BusinessApplicationContract::STATUS_GENERATED,
                BusinessApplicationContract::STATUS_SIGNING,
                BusinessApplicationContract::STATUS_SIGNED
            ])
            ->latest();
    }

    /**
     * 获取当前待审批任务
     */
    public function currentApprovalTasks()
    {
        return $this->hasMany(ApprovalTask::class, 'application_id')
            ->where('status', ApprovalTask::STATUS_PENDING)
            ->orderBy('step_order');
    }

    /**
     * 获取当前补件记录
     */
    public function currentSupplement()
    {
        return $this->hasOne(CustomerSupplement::class, 'application_id')
            ->whereIn('status', ['pending', 'submitted'])
            ->latest();
    }

    /**
     * 关联面审预约
     */
    public function interviewAppointments()
    {
        return $this->hasMany(InterviewAppointment::class, 'application_id');
    }

    /**
     * 获取当前面审预约记录
     */
    public function currentInterviewAppointment()
    {
        return $this->hasOne(InterviewAppointment::class, 'application_id')
            ->whereIn('status', ['pending', 'scheduled'])
            ->latest();
    }

    /**
     * 生成申请单号
     */
    public static function generateApplicationNo()
    {
        return 'APP' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute($value)
    {
        if ($value) {
            return $value;
        }

        $statusMap = [
            self::STATUS_SUBMITTED => '已提交',
            self::STATUS_INITIAL_REVIEW => '初审中',
            self::STATUS_PRE_APPROVAL => '预审通过',
            self::STATUS_INTERVIEW_PENDING => '待面审',
            self::STATUS_INTERVIEW_SCHEDULED => '已预约面审',
            self::STATUS_INTERVIEW_COMPLETED => '面审完成',
            self::STATUS_FINAL_REVIEW => '终审中',
            self::STATUS_SECONDARY_REVIEW => '复审中',
            self::STATUS_CONTRACT_PENDING => '待签约',
            self::STATUS_CONTRACT_PROCESSING => '签约中',
            self::STATUS_CONTRACT_COMPLETED => '签约完成',
            self::STATUS_APPROVED => '审批通过',
            self::STATUS_REJECTED => '审批拒绝',
            self::STATUS_SUPPLEMENT_REQUIRED => '需要补件'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 检查是否需要补件
     */
    public function needsSupplement()
    {
        return $this->need_supplement || $this->status === self::STATUS_SUPPLEMENT_REQUIRED;
    }

    /**
     * 获取补件状态
     */
    public function getSupplementStatus()
    {
        if (!$this->needsSupplement()) {
            return null;
        }

        $currentSupplement = $this->currentSupplement;
        if (!$currentSupplement) {
            return 'no_supplement_record';
        }

        return $currentSupplement->status;
    }

    /**
     * 设置需要补件
     */
    public function requireSupplement($reason, $deadline = null)
    {
        $this->update([
            'need_supplement' => true,
            'supplement_reason' => $reason,
            'supplement_deadline' => $deadline ?? Carbon::now()->addDays(7),
            'status' => self::STATUS_SUPPLEMENT_REQUIRED,
            'status_text' => '需要补件'
        ]);
    }

    /**
     * 范围查询：需要补件的业务
     */
    public function scopeNeedsSupplement($query)
    {
        return $query->where(function ($q) {
            $q->where('need_supplement', true)
              ->orWhere('status', self::STATUS_SUPPLEMENT_REQUIRED);
        });
    }

    /**
     * 范围查询：指定用户的业务
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：指定状态的业务
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 获取业务类型文本
     */
    public function getBusinessTypeTextAttribute()
    {
        $typeMap = [
            self::BUSINESS_TYPE_CAR_LOAN => '车抵贷',
            self::BUSINESS_TYPE_LEASE_TO_OWN => '以租代购'
        ];

        return $typeMap[$this->business_type] ?? '未知类型';
    }

    /**
     * 检查是否为车抵贷业务
     */
    public function isCarLoanType()
    {
        return $this->business_type === self::BUSINESS_TYPE_CAR_LOAN;
    }

    /**
     * 检查是否为以租代购业务
     */
    public function isLeaseToOwnType()
    {
        return $this->business_type === self::BUSINESS_TYPE_LEASE_TO_OWN;
    }

    /**
     * 检查是否需要走审批流程
     * 车抵贷不走审批流程，直接为已审批状态
     */
    public function needsApprovalWorkflow()
    {
        return !$this->isCarLoanType();
    }

    /**
     * 获取业务类型列表
     */
    public static function getBusinessTypeOptions()
    {
        return [
            self::BUSINESS_TYPE_CAR_LOAN => '车抵贷',
            self::BUSINESS_TYPE_LEASE_TO_OWN => '以租代购'
        ];
    }

    /**
     * 根据业务类型查询
     */
    public function scopeByBusinessType($query, $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * 查询车抵贷业务
     */
    public function scopeCarLoanType($query)
    {
        return $query->where('business_type', self::BUSINESS_TYPE_CAR_LOAN);
    }

    /**
     * 查询以租代购业务
     */
    public function scopeLeaseToOwnType($query)
    {
        return $query->where('business_type', self::BUSINESS_TYPE_LEASE_TO_OWN);
    }

    /**
     * 判断是否是新车业务
     */
    public function isNewCarBusiness()
    {
        return $this->is_new_car_business === true;
    }

    /**
     * 获取新车业务状态文本
     */
    public function getNewCarBusinessTextAttribute()
    {
        return $this->is_new_car_business ? '新车业务' : '非新车业务';
    }

    /**
     * 查询新车业务
     */
    public function scopeNewCarBusiness($query)
    {
        return $query->where('is_new_car_business', true);
    }

    /**
     * 查询非新车业务
     */
    public function scopeNotNewCarBusiness($query)
    {
        return $query->where('is_new_car_business', false);
    }

    /**
     * 根据是否新车业务查询
     */
    public function scopeByNewCarBusiness($query, $isNewCarBusiness)
    {
        return $query->where('is_new_car_business', $isNewCarBusiness);
    }
} 