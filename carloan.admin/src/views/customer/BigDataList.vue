<template>
  <div class="big-data-list">
    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="客户姓名/手机号/身份证号"
        clearable
      />
      <el-select
        v-model="listQuery.status"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="查询状态"
        clearable
      >
        <el-option label="查询中" value="querying" />
        <el-option label="已完成" value="completed" />
        <el-option label="查询失败" value="failed" />
      </el-select>
      <el-select
        v-model="listQuery.risk_level"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="风险等级"
        clearable
      >
        <el-option label="低风险" value="low" />
        <el-option label="中风险" value="medium" />
        <el-option label="高风险" value="high" />
      </el-select>
      <el-select
        v-model="listQuery.time_range"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="时间范围"
        clearable
      >
        <el-option label="7天内" value="7days" />
        <el-option label="7-30天内" value="7to30days" />
        <el-option label="30天外" value="30plus" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-bottom: 10px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <!-- 统计区域 -->
    <div class="statistics-container" v-if="statistics">
      <div class="statistics-item">
        <div class="statistics-number">{{ statistics['7days'] || 0 }}</div>
        <div class="statistics-label">7天内</div>
      </div>
      <div class="statistics-item">
        <div class="statistics-number">{{ statistics['7to30days'] || 0 }}</div>
        <div class="statistics-label">7-30天内</div>
      </div>
      <div class="statistics-item">
        <div class="statistics-number">{{ statistics['30plus'] || 0 }}</div>
        <div class="statistics-label">30天外</div>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="客户姓名" prop="customer_name" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.customer_name || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="手机号" prop="customer_phone" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.customer_phone || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="身份证号" prop="customer_id_card" align="center" min-width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.customer_id_card ? hideIdCard(scope.row.customer_id_card) : '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="查询状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            size="small"
          >
            {{ scope.row.status_text }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="风险评分" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.risk_score"
                :class="getScoreClass(scope.row.risk_score)"
                style="font-weight: bold;">
            {{ scope.row.risk_score }}分
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="风险等级" align="center" width="100">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.risk_level"
            :type="getRiskLevelType(scope.row.risk_level)"
            size="small"
          >
            {{ getRiskLevelText(scope.row.risk_level) }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="查询时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.query_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.created_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="300"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            style="margin-right: 8px;"
            @click="handleView(row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="row.can_requery"
            type="warning"
            size="mini"
            style="margin-right: 8px;"
            @click="handleRequery(row)"
          >
            重新查询
          </el-button>
          <el-button
            v-if="row.can_create_business"
            type="success"
            size="mini"
            @click="handleCreateBusiness(row)"
          >
            创建业务
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.per_page"
      :page-count.sync="pageCount"
      :page-sizes="[20, 50, 100, 200]"
      @pagination="getList"
    />

    <!-- 详情弹窗 -->
    <el-dialog
      title="大数据查询详情"
      :visible.sync="detailDialogVisible"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <div v-if="detailData" class="detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>客户信息</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">客户姓名：</span>
                <span class="value">{{ detailData.customer_name }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">手机号：</span>
                <span class="value">{{ detailData.customer_phone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">身份证号：</span>
                <span class="value">{{ detailData.customer_id_card }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">查询状态：</span>
                <el-tag :type="getStatusType(detailData.status)" size="small">
                  {{ detailData.status_text }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="detailData.risk_score">
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">风险评分：</span>
                <span class="value" :class="getScoreClass(detailData.risk_score)" style="font-weight: bold;">
                  {{ detailData.risk_score }}分
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">风险等级：</span>
                <el-tag :type="getRiskLevelType(detailData.risk_level)" size="small">
                  {{ getRiskLevelText(detailData.risk_level) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <span class="label">查询时间：</span>
                <span class="value">{{ formatDate(detailData.query_time) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 查询结果详情 -->
        <div v-if="detailData.query_results" class="detail-section">
          <h3>查询结果详情</h3>

          <!-- 贷前风险综合查询 -->
          <div v-if="detailData.query_results.pre_loan_risk" class="result-item">
            <h4>贷前风险综合查询</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">风险评分：</span>
                  <span class="value">{{ detailData.query_results.pre_loan_risk.score }}分</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">决策结果：</span>
                  <span class="value">{{ detailData.query_results.pre_loan_risk.decision }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">报告链接：</span>
                  <a v-if="detailData.query_results.pre_loan_risk.pdf_url"
                     :href="detailData.query_results.pre_loan_risk.pdf_url"
                     target="_blank"
                     class="link">
                    查看PDF报告
                  </a>
                  <span v-else>-</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 信用风险三维报告 -->
          <div v-if="detailData.query_results.credit_risk" class="result-item">
            <h4>信用风险三维报告</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">申请准入分：</span>
                  <span class="value">{{ detailData.query_results.credit_risk.access_score }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">置信度：</span>
                  <span class="value">{{ detailData.query_results.credit_risk.confidence }}%</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="detail-item">
                  <span class="label">命中机构数：</span>
                  <span class="value">{{ detailData.query_results.credit_risk.hit_count }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 其他查询结果可以继续添加 -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="detailData && detailData.can_requery" type="warning" @click="handleRequery(detailData)">重新查询</el-button>
        <el-button v-if="detailData && detailData.can_create_business" type="success" @click="handleCreateBusiness(detailData)">创建业务</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getBigDataList, getBigDataDetail, requeueBigData } from '@/api/customer'
import Pagination from '@/components/Pagination'

export default {
  name: 'BigDataList',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      pageCount: 0,
      loading: false,
      statistics: null,
      detailDialogVisible: false,
      detailData: null,
      listQuery: {
        page: 1,
        per_page: 20,
        keyword: '',
        status: '',
        risk_level: '',
        time_range: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = { ...this.listQuery }
        const response = await getBigDataList(params)

        if (response.data) {
          this.list = response.data.list || []
          this.total = response.data.pagination?.total || 0
          this.pageCount = response.data.pagination?.last_page || 0
          this.statistics = response.data.statistics || null
        }
      } catch (error) {
        console.error('获取大数据查询列表失败:', error)
        this.$message.error('获取大数据查询列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 查看详情
    async handleView(row) {
      try {
        const response = await getBigDataDetail(row.id)
        this.detailData = response.data
        this.detailDialogVisible = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败')
      }
    },

    // 关闭详情弹窗
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.detailData = null
    },

    // 重新查询
    async handleRequery(row) {
      try {
        await this.$confirm('确定要重新查询该客户的风险数据吗？', '确认重新查询', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await requeueBigData(row.id)
        if (response.success) {
          this.$message.success('重新查询已启动')
          this.getList()
          if (this.detailDialogVisible) {
            this.handleCloseDetail()
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新查询失败:', error)
          this.$message.error('重新查询失败')
        }
      }
    },

    // 创建业务
    handleCreateBusiness(row) {
      // 这里可以跳转到业务创建页面，或者打开业务创建弹窗
      this.$message.info('创建业务功能待实现')
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      return idCard.substring(0, 6) + '****' + idCard.substring(idCard.length - 4)
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 'querying':
          return 'warning'
        case 'completed':
          return 'success'
        case 'failed':
          return 'danger'
        default:
          return 'info'
      }
    },

    // 获取风险等级类型
    getRiskLevelType(riskLevel) {
      switch (riskLevel) {
        case 'low':
          return 'success'
        case 'medium':
          return 'warning'
        case 'high':
          return 'danger'
        default:
          return 'info'
      }
    },

    // 获取风险等级文本
    getRiskLevelText(riskLevel) {
      switch (riskLevel) {
        case 'low':
          return '低风险'
        case 'medium':
          return '中风险'
        case 'high':
          return '高风险'
        default:
          return '未知'
      }
    },

    // 获取评分样式类
    getScoreClass(score) {
      if (score >= 80) return 'score-high'
      if (score >= 60) return 'score-medium'
      return 'score-low'
    }
  }
}
</script>

<style lang="scss" scoped>
.big-data-list {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.statistics-container {
  display: flex;
  margin-bottom: 20px;

  .statistics-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }

    .statistics-number {
      font-size: 32px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 8px;
    }

    .statistics-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.detail-content {
  .detail-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #e4e7ed;
      color: #303133;
    }

    h4 {
      margin: 20px 0 15px 0;
      color: #606266;
      font-size: 16px;
    }

    .detail-item {
      margin-bottom: 15px;

      .label {
        color: #909399;
        font-weight: 500;
        margin-right: 10px;
      }

      .value {
        color: #303133;
      }

      .link {
        color: #409eff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .result-item {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
  }
}

.score-high {
  color: #67c23a;
}

.score-medium {
  color: #e6a23c;
}

.score-low {
  color: #f56c6c;
}
</style>
