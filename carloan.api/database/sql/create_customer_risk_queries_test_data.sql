-- 客户大数据风险查询测试数据
-- 创建时间: 2024-03-17
-- 说明: 包含各种状态的客户风险查询记录，覆盖不同时间段和风险等级

-- 0. 创建表（如果不存在）
CREATE TABLE IF NOT EXISTS `customer_risk_queries` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
  `customer_id_card` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户身份证号',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'querying' COMMENT '查询状态:querying查询中,completed已完成,failed查询失败',
  `risk_score` int(11) DEFAULT NULL COMMENT '风险评分(0-100)',
  `risk_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '风险等级:low低风险,medium中风险,high高风险',
  `query_time` timestamp NULL DEFAULT NULL COMMENT '查询时间',
  `query_results` json DEFAULT NULL COMMENT '查询结果详情',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_risk_queries_user_id_index` (`user_id`),
  KEY `customer_risk_queries_customer_phone_index` (`customer_phone`),
  KEY `customer_risk_queries_customer_id_card_index` (`customer_id_card`),
  KEY `customer_risk_queries_status_index` (`status`),
  KEY `customer_risk_queries_user_id_status_index` (`user_id`,`status`),
  KEY `customer_risk_queries_customer_phone_customer_id_card_index` (`customer_phone`,`customer_id_card`),
  KEY `customer_risk_queries_query_time_index` (`query_time`),
  KEY `customer_risk_queries_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户风险查询表';

-- 1. 清理现有测试数据（可选，谨慎使用）
-- DELETE FROM `customer_risk_queries` WHERE `customer_name` LIKE '测试客户%';

-- 2. 确保用户数据存在
INSERT IGNORE INTO `users` (
  `id`, `phone`, `nickname`, `name`, `channel_code`, `status`, 
  `register_time`, `login_time`, `created_at`, `updated_at`
) VALUES 
(1, '13900139001', '张三业务员', '张三', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW()),
(2, '13900139002', '李四业务员', '李四', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW()),
(3, '13900139003', '王五业务员', '王五', 'PARTNER', 1, NOW(), NOW(), NOW(), NOW());

-- 3. 插入客户风险查询测试数据

-- 7天内的查询记录（12条）
INSERT INTO `customer_risk_queries` (
  `user_id`, `customer_name`, `customer_phone`, `customer_id_card`, 
  `status`, `risk_score`, `risk_level`, `query_time`, `query_results`,
  `notes`, `created_at`, `updated_at`
) VALUES

-- 查询中状态
(
  1, '测试客户001', '13800138001', '110101199001011001',
  'querying', NULL, NULL, DATE_SUB(NOW(), INTERVAL 2 HOUR),
  NULL, '正在进行风险评估查询',
  DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR)
),

-- 已完成 - 高风险
(
  1, '测试客户002', '13800138002', '110101199002021002',
  'completed', 45, 'high', DATE_SUB(NOW(), INTERVAL 4 HOUR),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 45, 'decision', 'Reject', 'pdf_url', 'https://example.com/risk-report-002.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 580, 'confidence', 75, 'hit_count', 12),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 95),
    'lawsuit_info', JSON_OBJECT('total_cases', 3, 'active_cases', 2),
    'dishonest_info', JSON_OBJECT('record_count', 2),
    'high_consumption', JSON_OBJECT('record_count', 1)
  ),
  '高风险客户，建议拒绝',
  DATE_SUB(NOW(), INTERVAL 4 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR)
),

-- 已完成 - 中风险  
(
  1, '测试客户003', '13800138003', '110101199003031003',
  'completed', 72, 'medium', DATE_SUB(NOW(), INTERVAL 6 HOUR),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 72, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-003.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 680, 'confidence', 85, 'hit_count', 6),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 98),
    'lawsuit_info', JSON_OBJECT('total_cases', 1, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  '中风险客户，需要人工复审',
  DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 5 HOUR)
),

-- 已完成 - 低风险
(
  1, '测试客户004', '13800138004', '110101199004041004',
  'completed', 89, 'low', DATE_SUB(NOW(), INTERVAL 8 HOUR),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 89, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-004.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 750, 'confidence', 92, 'hit_count', 3),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 100),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  '优质客户，建议通过',
  DATE_SUB(NOW(), INTERVAL 8 HOUR), DATE_SUB(NOW(), INTERVAL 7 HOUR)
),

-- 查询失败
(
  1, '测试客户005', '13800138005', '110101199005051005',
  'failed', NULL, NULL, DATE_SUB(NOW(), INTERVAL 12 HOUR),
  JSON_OBJECT('error', '身份证二要素验证失败', 'error_code', 'ID_VERIFY_FAILED'),
  '查询失败，需要重新查询',
  DATE_SUB(NOW(), INTERVAL 12 HOUR), DATE_SUB(NOW(), INTERVAL 11 HOUR)
),

-- 其他7天内的记录
(
  1, '测试客户006', '13800138006', '110101199006061006',
  'completed', 66, 'medium', DATE_SUB(NOW(), INTERVAL 1 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 66, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-006.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 650, 'confidence', 80, 'hit_count', 8),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 96),
    'lawsuit_info', JSON_OBJECT('total_cases', 2, 'active_cases', 1),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 1)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

(
  1, '测试客户007', '13800138007', '110101199007071007',
  'completed', 82, 'low', DATE_SUB(NOW(), INTERVAL 2 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 82, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-007.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 720, 'confidence', 88, 'hit_count', 4),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 99),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)
),

(
  1, '测试客户008', '13800138008', '110101199008081008',
  'completed', 58, 'high', DATE_SUB(NOW(), INTERVAL 3 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 58, 'decision', 'Reject', 'pdf_url', 'https://example.com/risk-report-008.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 550, 'confidence', 70, 'hit_count', 15),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 92),
    'lawsuit_info', JSON_OBJECT('total_cases', 5, 'active_cases', 3),
    'dishonest_info', JSON_OBJECT('record_count', 1),
    'high_consumption', JSON_OBJECT('record_count', 2)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)
),

(
  1, '测试客户009', '13800138009', '110101199009091009',
  'completed', 75, 'medium', DATE_SUB(NOW(), INTERVAL 4 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 75, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-009.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 690, 'confidence', 83, 'hit_count', 7),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 97),
    'lawsuit_info', JSON_OBJECT('total_cases', 1, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY)
),

(
  1, '测试客户010', '13800138010', '110101199010101010',
  'completed', 91, 'low', DATE_SUB(NOW(), INTERVAL 5 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 91, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-010.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 780, 'confidence', 95, 'hit_count', 2),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 100),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY)
),

(
  1, '测试客户011', '13800138011', '110101199011111011',
  'completed', 63, 'medium', DATE_SUB(NOW(), INTERVAL 6 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 63, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-011.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 630, 'confidence', 78, 'hit_count', 9),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 94),
    'lawsuit_info', JSON_OBJECT('total_cases', 2, 'active_cases', 1),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 1)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY)
),

(
  1, '测试客户012', '13800138012', '110101199012121012',
  'completed', 86, 'low', DATE_SUB(NOW(), INTERVAL 7 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 86, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-012.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 740, 'confidence', 90, 'hit_count', 5),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 98),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)
);

-- 7-30天内的查询记录（5条）
INSERT INTO `customer_risk_queries` (
  `user_id`, `customer_name`, `customer_phone`, `customer_id_card`, 
  `status`, `risk_score`, `risk_level`, `query_time`, `query_results`,
  `notes`, `created_at`, `updated_at`
) VALUES

(
  1, '测试客户013', '13800138013', '110101199001131013',
  'completed', 78, 'medium', DATE_SUB(NOW(), INTERVAL 10 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 78, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-013.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 700, 'confidence', 85, 'hit_count', 6),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 97),
    'lawsuit_info', JSON_OBJECT('total_cases', 1, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY)
),

(
  1, '测试客户014', '13800138014', '110101199002141014',
  'completed', 52, 'high', DATE_SUB(NOW(), INTERVAL 15 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 52, 'decision', 'Reject', 'pdf_url', 'https://example.com/risk-report-014.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 520, 'confidence', 65, 'hit_count', 18),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 89),
    'lawsuit_info', JSON_OBJECT('total_cases', 4, 'active_cases', 2),
    'dishonest_info', JSON_OBJECT('record_count', 3),
    'high_consumption', JSON_OBJECT('record_count', 2)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY)
),

(
  1, '测试客户015', '13800138015', '110101199003151015',
  'completed', 84, 'low', DATE_SUB(NOW(), INTERVAL 20 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 84, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-015.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 730, 'confidence', 89, 'hit_count', 4),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 99),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 20 DAY)
),

(
  1, '测试客户016', '13800138016', '110101199004161016',
  'completed', 69, 'medium', DATE_SUB(NOW(), INTERVAL 25 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 69, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-016.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 660, 'confidence', 82, 'hit_count', 7),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 95),
    'lawsuit_info', JSON_OBJECT('total_cases', 1, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 1)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 25 DAY), DATE_SUB(NOW(), INTERVAL 25 DAY)
),

(
  1, '测试客户017', '13800138017', '110101199005171017',
  'failed', NULL, NULL, DATE_SUB(NOW(), INTERVAL 28 DAY),
  JSON_OBJECT('error', '网络超时', 'error_code', 'NETWORK_TIMEOUT'),
  '查询超时失败',
  DATE_SUB(NOW(), INTERVAL 28 DAY), DATE_SUB(NOW(), INTERVAL 28 DAY)
);

-- 30天外的查询记录（模拟更多历史数据）
INSERT INTO `customer_risk_queries` (
  `user_id`, `customer_name`, `customer_phone`, `customer_id_card`, 
  `status`, `risk_score`, `risk_level`, `query_time`, `query_results`,
  `notes`, `created_at`, `updated_at`
) VALUES

-- 添加一些30天外的历史记录
(
  1, '测试客户018', '13800138018', '110101199006181018',
  'completed', 76, 'medium', DATE_SUB(NOW(), INTERVAL 35 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 76, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-018.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 690, 'confidence', 84, 'hit_count', 6),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 96),
    'lawsuit_info', JSON_OBJECT('total_cases', 1, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 35 DAY), DATE_SUB(NOW(), INTERVAL 35 DAY)
),

(
  1, '测试客户019', '13800138019', '110101199007191019',
  'completed', 88, 'low', DATE_SUB(NOW(), INTERVAL 45 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 88, 'decision', 'Accept', 'pdf_url', 'https://example.com/risk-report-019.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 760, 'confidence', 91, 'hit_count', 3),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 100),
    'lawsuit_info', JSON_OBJECT('total_cases', 0, 'active_cases', 0),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 0)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 45 DAY), DATE_SUB(NOW(), INTERVAL 45 DAY)
),

(
  1, '测试客户020', '13800138020', '110101199008201020',
  'completed', 61, 'medium', DATE_SUB(NOW(), INTERVAL 60 DAY),
  JSON_OBJECT(
    'pre_loan_risk', JSON_OBJECT('score', 61, 'decision', 'Review', 'pdf_url', 'https://example.com/risk-report-020.pdf'),
    'credit_risk', JSON_OBJECT('access_score', 620, 'confidence', 79, 'hit_count', 8),
    'id_verify', JSON_OBJECT('result', 'verified', 'match_rate', 93),
    'lawsuit_info', JSON_OBJECT('total_cases', 2, 'active_cases', 1),
    'dishonest_info', JSON_OBJECT('record_count', 0),
    'high_consumption', JSON_OBJECT('record_count', 1)
  ),
  NULL,
  DATE_SUB(NOW(), INTERVAL 60 DAY), DATE_SUB(NOW(), INTERVAL 60 DAY)
);

-- 4. 验证数据插入结果
SELECT 
  COUNT(*) as total_count,
  SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as within_7_days,
  SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as within_7_to_30_days,
  SUM(CASE WHEN created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as beyond_30_days
FROM customer_risk_queries 
WHERE customer_name LIKE '测试客户%'
AND user_id = 1;

-- 5. 按状态统计
SELECT 
  status,
  COUNT(*) as count,
  ROUND(AVG(risk_score), 2) as avg_score
FROM customer_risk_queries 
WHERE customer_name LIKE '测试客户%'
AND user_id = 1
GROUP BY status
ORDER BY count DESC;

-- 6. 按风险等级统计
SELECT 
  risk_level,
  COUNT(*) as count,
  MIN(risk_score) as min_score,
  MAX(risk_score) as max_score,
  ROUND(AVG(risk_score), 2) as avg_score
FROM customer_risk_queries 
WHERE customer_name LIKE '测试客户%'
AND user_id = 1
AND status = 'completed'
GROUP BY risk_level
ORDER BY avg_score DESC;

-- 7. 清理脚本（备用，慎用）
-- DELETE FROM customer_risk_queries WHERE customer_name LIKE '测试客户%'; 