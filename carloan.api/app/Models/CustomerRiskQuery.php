<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerRiskQuery extends Model
{
    use SoftDeletes;

    protected $table = 'customer_risk_queries';

    protected $fillable = [
        'user_id',
        'customer_id',
        'customer_name',
        'customer_phone', 
        'customer_id_card',
        'status',
        'risk_score',
        'risk_level',
        'query_time',
        'query_results',
        'notes'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'query_time' => 'datetime',
        'query_results' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_QUERYING = 'querying';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 风险等级常量
     */
    const RISK_LEVEL_LOW = 'low';
    const RISK_LEVEL_MEDIUM = 'medium';
    const RISK_LEVEL_HIGH = 'high';

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联的风险查询记录（技术层）
     */
    public function riskQueries()
    {
        return $this->hasMany(RiskQuery::class, 'user_id', 'user_id')
            ->where('name', $this->customer_name)
            ->where('cert_no', $this->customer_id_card);
    }

    /**
     * 获取最新的OCR识别记录
     */
    public function latestOcrResult()
    {
        return $this->hasOneThrough(
            IdCardOcrResult::class,
            RiskQuery::class,
            'user_id', // 中间表外键
            'risk_query_id', // 目标表外键
            'user_id', // 当前表主键
            'id' // 中间表主键
        )->where('risk_queries.name', $this->customer_name)
         ->where('risk_queries.cert_no', $this->customer_id_card)
         ->where('risk_queries.query_type', 1)
         ->latest('risk_queries.created_at');
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeInTimeRange($query, $days)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 获取风险等级文本
     */
    public function getRiskLevelTextAttribute()
    {
        $levels = [
            self::RISK_LEVEL_LOW => '低风险',
            self::RISK_LEVEL_MEDIUM => '中风险',
            self::RISK_LEVEL_HIGH => '高风险'
        ];

        return $levels[$this->risk_level] ?? '未评估';
    }

    /**
     * 获取风险等级颜色
     */
    public function getRiskLevelColorAttribute()
    {
        $colors = [
            self::RISK_LEVEL_LOW => 'blue',
            self::RISK_LEVEL_MEDIUM => 'yellow',
            self::RISK_LEVEL_HIGH => 'red'
        ];

        return $colors[$this->risk_level] ?? 'gray';
    }

    /**
     * 是否可以重新查询
     */
    public function getCanRequeryAttribute()
    {
        return in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_FAILED]);
    }

    /**
     * 是否可以创建业务
     */
    public function getCanCreateBusinessAttribute()
    {
        return $this->status === self::STATUS_COMPLETED && $this->risk_score >= 60;
    }
} 