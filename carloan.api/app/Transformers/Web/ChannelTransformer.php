<?php

namespace App\Transformers\Web;

use App\Models\Channel;
use League\Fractal\TransformerAbstract;

class ChannelTransformer extends TransformerAbstract
{
    public function transform(Channel $channel)
    {
        return [
            'id' => $channel->id,
            'code' => $channel->code,
            'name' => $channel->name,
            'status' => $channel->status,
            'status_text' => $channel->status === 1 ? '启用' : '禁用',
            'sort' => $channel->sort,
            'created_at' => $channel->created_at ? $channel->created_at->toDateTimeString() : '',
            'updated_at' => $channel->updated_at ? $channel->updated_at->toDateTimeString() : ''
        ];
    }
}