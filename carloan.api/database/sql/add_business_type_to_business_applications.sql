-- 为 business_applications 表添加业务类型字段
-- 执行时间：2024-12-25
-- 说明：增加业务类型字段，支持车抵贷和以租代购两种类型，车抵贷不走审批流程

-- 1. 添加业务类型字段
ALTER TABLE `business_applications` 
ADD COLUMN `business_type` TINYINT NOT NULL DEFAULT 2 COMMENT '业务类型:1车抵贷,2以租代购' AFTER `user_id`;

-- 2. 添加索引以优化查询性能
ALTER TABLE `business_applications` 
ADD INDEX `idx_business_type` (`business_type`);

-- 3. 添加复合索引：业务类型 + 状态
ALTER TABLE `business_applications` 
ADD INDEX `idx_business_type_status` (`business_type`, `status`);

-- 4. 添加复合索引：业务员 + 业务类型
ALTER TABLE `business_applications` 
ADD INDEX `idx_user_business_type` (`user_id`, `business_type`);

-- 5. 更新现有数据：所有现有业务申请默认设置为以租代购类型
UPDATE `business_applications` 
SET `business_type` = 2 
WHERE `business_type` IS NULL OR `business_type` = 0;

-- 6. 验证字段是否添加成功
SELECT 
    column_name, 
    data_type, 
    column_default, 
    column_comment,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'business_applications' 
AND column_name = 'business_type';

-- 7. 验证索引是否创建成功
SHOW INDEX FROM `business_applications` WHERE Column_name = 'business_type';

-- 8. 统计各业务类型的数据量
SELECT 
    CASE business_type
        WHEN 1 THEN '车抵贷'
        WHEN 2 THEN '以租代购'
        ELSE '未知类型'
    END as business_type_name,
    business_type,
    COUNT(*) as count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
    COUNT(CASE WHEN status IN ('submitted', 'initial_review', 'final_review', 'secondary_review') THEN 1 END) as pending_count
FROM `business_applications`
GROUP BY business_type
ORDER BY business_type;

-- 9. 回滚脚本（备用，慎用）
-- ALTER TABLE `business_applications` DROP INDEX `idx_user_business_type`;
-- ALTER TABLE `business_applications` DROP INDEX `idx_business_type_status`;  
-- ALTER TABLE `business_applications` DROP INDEX `idx_business_type`;
-- ALTER TABLE `business_applications` DROP COLUMN `business_type`; 