<?php

namespace App\Http\Controllers\Api\Web\V1;

use Illuminate\Http\Request;
use App\Models\FinancialProduct;
use App\Models\LoanPeriod;

class CalculationController extends BaseController
{
    /**
     * 获取金融产品列表
     *
     * @return mixed
     */
    public function getFinancialProducts()
    {
        try {
            $products = FinancialProduct::getActiveProducts();
            return $this->successHandler('获取金融产品成功', 200, $products);
        } catch (\Exception $e) {
            return $this->errorHandler('获取金融产品失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取期数选项
     *
     * @return mixed
     */
    public function getPeriods()
    {
        try {
            $periods = LoanPeriod::getActivePeriods();
            return $this->successHandler('获取期数选项成功', 200, $periods);
        } catch (\Exception $e) {
            return $this->errorHandler('获取期数选项失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 计算还款信息（等额本息）
     *
     * @param Request $request
     * @return mixed
     */
    public function calculate(Request $request)
    {
        try {
            // 验证参数
            $this->validate($request, [
                'amount' => 'required|numeric|min:1000|max:10000000',
                'periods' => 'required|integer|min:6|max:120',
                'product_id' => 'required|integer'
            ], [
                'amount.required' => '贷款金额不能为空',
                'amount.numeric' => '贷款金额必须为数字',
                'amount.min' => '贷款金额最少1000元',
                'amount.max' => '贷款金额最多1000万元',
                'periods.required' => '贷款期数不能为空',
                'periods.integer' => '贷款期数必须为整数',
                'periods.min' => '贷款期数最少6期',
                'periods.max' => '贷款期数最多120期',
                'product_id.required' => '产品ID不能为空',
                'product_id.integer' => '产品ID必须为整数'
            ]);

            $amount = $request->input('amount');
            $periods = $request->input('periods');
            $productId = $request->input('product_id');

            // 获取产品信息
            $product = FinancialProduct::enabled()->find($productId);
            if (!$product) {
                return $this->errorHandler('金融产品不存在或已禁用', 400);
            }

            // 验证贷款金额是否在产品限额范围内
            if (!$product->isAmountValid($amount)) {
                return $this->errorHandler("贷款金额必须在 {$product->min_amount} 到 {$product->max_amount} 之间", 400);
            }

            // 验证期数是否为该产品支持的期数
            if (!$product->isPeriodSupported($periods)) {
                return $this->errorHandler('该产品不支持所选期数', 400);
            }
            
            // 使用模型方法计算等额本息
            $result = $product->calculateEqualPayment($amount, $periods);

            return $this->successHandler('计算成功', 200, $result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorHandler('参数验证失败', 422, $e->errors());
        } catch (\Exception $e) {
            return $this->errorHandler('计算失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 等额本金计算
     *
     * @param Request $request
     * @return mixed
     */
    public function calculateEqualPrincipal(Request $request)
    {
        try {
            // 验证参数
            $this->validate($request, [
                'amount' => 'required|numeric|min:1000|max:10000000',
                'periods' => 'required|integer|min:6|max:120',
                'product_id' => 'required|integer'
            ], [
                'amount.required' => '贷款金额不能为空',
                'amount.numeric' => '贷款金额必须为数字',
                'amount.min' => '贷款金额最少1000元',
                'amount.max' => '贷款金额最多1000万元',
                'periods.required' => '贷款期数不能为空',
                'periods.integer' => '贷款期数必须为整数',
                'periods.min' => '贷款期数最少6期',
                'periods.max' => '贷款期数最多120期',
                'product_id.required' => '产品ID不能为空',
                'product_id.integer' => '产品ID必须为整数'
            ]);

            $amount = $request->input('amount');
            $periods = $request->input('periods');
            $productId = $request->input('product_id');

            // 获取产品信息
            $product = FinancialProduct::enabled()->find($productId);
            if (!$product) {
                return $this->errorHandler('金融产品不存在或已禁用', 400);
            }

            // 验证贷款金额是否在产品限额范围内
            if (!$product->isAmountValid($amount)) {
                return $this->errorHandler("贷款金额必须在 {$product->min_amount} 到 {$product->max_amount} 之间", 400);
            }

            // 验证期数是否为该产品支持的期数
            if (!$product->isPeriodSupported($periods)) {
                return $this->errorHandler('该产品不支持所选期数', 400);
            }
            
            // 使用模型方法计算等额本金
            $result = $product->calculateEqualPrincipal($amount, $periods);

            return $this->successHandler('计算成功', 200, $result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorHandler('参数验证失败', 422, $e->errors());
        } catch (\Exception $e) {
            return $this->errorHandler('计算失败：' . $e->getMessage(), 500);
        }
    }
} 