<?php

namespace App\Transformers\Admin;

use App\Models\Customer;
use League\Fractal\TransformerAbstract;

class CustomerTransformer extends TransformerAbstract
{
    public function transform(Customer $customer)
    {
        return [
            'id' => $customer->id,
            'phone' => $customer->phone,
            'name' => $customer->name,
            'id_card' => $customer->id_card,
            'gender' => $customer->gender,
            'gender_text' => $customer->gender_text,
            'nation' => $customer->nation,
            'address' => $customer->address,
            'authority' => $customer->authority,
            'valid_start' => $customer->valid_start ? $customer->valid_start->format('Y-m-d') : null,
            'valid_end' => $customer->valid_end ? $customer->valid_end->format('Y-m-d') : null,
            'status' => $customer->status,
            'status_text' => $customer->status_text,
            'age' => $customer->age,
            'is_id_card_valid' => $customer->isIdCardValid(),
            'created_at' => $customer->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $customer->updated_at->format('Y-m-d H:i:s'),
        ];
    }
} 