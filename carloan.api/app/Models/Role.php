<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends BaseModel
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'permissions',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'permissions' => 'array',
        'status' => 'integer',
        'sort_order' => 'integer'
    ];

    protected $hidden = [
        'deleted_at'
    ];

    /**
     * 角色类型常量
     */
    const TYPE_ADMIN = 'admin';      // 管理员
    const TYPE_USER = 'user';        // 业务员
    const TYPE_FINANCE = 'finance';  // 金融

    /**
     * 角色状态常量
     */
    const STATUS_DISABLED = 0;  // 禁用
    const STATUS_ENABLED = 1;   // 启用

    /**
     * 检查角色是否有指定权限
     *
     * @param string $permission 权限码
     * @return bool
     */
    public function hasPermission($permission)
    {
        if (!is_array($this->permissions)) {
            return false;
        }

        // 检查是否有所有权限
        if (in_array('*', $this->permissions)) {
            return true;
        }

        // 检查精确匹配
        if (in_array($permission, $this->permissions)) {
            return true;
        }

        // 检查通配符权限
        foreach ($this->permissions as $rolePermission) {
            if (str_ends_with($rolePermission, '.*')) {
                $prefix = str_replace('.*', '', $rolePermission);
                if (str_starts_with($permission, $prefix . '.') || $permission === $prefix) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取角色的所有权限列表
     *
     * @return array
     */
    public function getAllPermissions()
    {
        if (!is_array($this->permissions)) {
            return [];
        }

        // 如果有所有权限，返回特殊标识
        if (in_array('*', $this->permissions)) {
            return ['*'];
        }

        $allPermissions = [];
        
        // 获取所有权限码
        $permissionCodes = PermissionCode::where('status', 1)->pluck('code')->toArray();

        foreach ($this->permissions as $rolePermission) {
            if (str_ends_with($rolePermission, '.*')) {
                // 处理通配符权限
                $prefix = str_replace('.*', '', $rolePermission);
                $matchedPermissions = array_filter($permissionCodes, function($code) use ($prefix) {
                    return str_starts_with($code, $prefix . '.') || $code === $prefix;
                });
                $allPermissions = array_merge($allPermissions, $matchedPermissions);
            } else {
                // 精确权限
                $allPermissions[] = $rolePermission;
            }
        }

        return array_unique($allPermissions);
    }

    /**
     * 获取启用的角色
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 根据类型获取角色
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 获取管理员角色
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAdminRoles($query)
    {
        return $query->where('type', self::TYPE_ADMIN);
    }

    /**
     * 获取业务员角色
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUserRoles($query)
    {
        return $query->where('type', self::TYPE_USER);
    }

    /**
     * 关联管理员
     */
    public function admins()
    {
        return $this->hasMany(Admin::class, 'role_id');
    }

    /**
     * 关联用户
     */
    public function users()
    {
        return $this->hasMany(User::class, 'role_id');
    }
} 