<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\CustomerSupplement;
use App\Models\BusinessApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class SupplementController extends BaseController
{
    /**
     * 获取客户补件列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 10);
            $status = $request->get('status', ''); // 补件状态筛选
            $keyword = $request->get('keyword', ''); // 搜索关键词

            // 构建查询
            $query = CustomerSupplement::with(['application', 'reviewer'])
                ->forUser($user->id)
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if ($status) {
                $query->where('status', $status);
            }

            // 关键词搜索（客户姓名、手机号、申请单号）
            if ($keyword) {
                $query->whereHas('application', function ($q) use ($keyword) {
                    $q->where('customer_name', 'like', "%{$keyword}%")
                      ->orWhere('customer_phone', 'like', "%{$keyword}%")
                      ->orWhere('application_no', 'like', "%{$keyword}%");
                });
            }

            // 分页查询
            $supplements = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $data = [
                'list' => $supplements->items() ? array_map(function ($supplement) {
                    return $this->formatSupplementData($supplement);
                }, $supplements->items()) : [],
                'pagination' => [
                    'total' => $supplements->total(),
                    'per_page' => $supplements->perPage(),
                    'current_page' => $supplements->currentPage(),
                    'last_page' => $supplements->lastPage(),
                    'has_more' => $supplements->hasMorePages()
                ]
            ];

            return $this->successHandler('获取补件列表成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取补件列表失败: ' . $e->getMessage());
            return $this->errorHandler('获取补件列表失败', 500);
        }
    }

    /**
     * 获取补件详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $supplement = CustomerSupplement::with(['application', 'reviewer'])
                ->where('id', $id)
                ->forUser($user->id)
                ->first();

            if (!$supplement) {
                return $this->errorHandler('补件记录不存在', 404);
            }

            $data = $this->formatSupplementData($supplement, true);

            return $this->successHandler('获取补件详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取补件详情失败: ' . $e->getMessage());
            return $this->errorHandler('获取补件详情失败', 500);
        }
    }

    /**
     * 客户提交补件材料
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function submit(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'module_status' => 'nullable|array',
                'module_status.*.key' => 'required|string',
                'module_status.*.name' => 'required|string',
                'module_status.*.is_complete' => 'required|boolean',
                'module_status.*.status_text' => 'required|string',
                'notes' => 'nullable|string|max:500'
            ], [
                'module_status.array' => '模块状态格式错误',
                'module_status.*.key.required' => '模块标识不能为空',
                'module_status.*.name.required' => '模块名称不能为空',
                'module_status.*.is_complete.required' => '模块完成状态不能为空',
                'module_status.*.status_text.required' => '模块状态文本不能为空',
                'notes.max' => '备注不能超过500个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $supplement = CustomerSupplement::where('id', $id)
                ->forUser($user->id)
                ->first();

            if (!$supplement) {
                return $this->errorHandler('补件记录不存在', 404);
            }

            if (!in_array($supplement->status, [CustomerSupplement::STATUS_PENDING, CustomerSupplement::STATUS_REJECTED])) {
                return $this->errorHandler('当前状态不允许提交补件', 400);
            }

            // 检查是否所有必要模块都已完善
            $moduleStatus = $request->input('module_status', []);
            $incompleteModules = [];
            
            foreach ($moduleStatus as $module) {
                if (!$module['is_complete']) {
                    $incompleteModules[] = $module['name'];
                }
            }
            
            if (!empty($incompleteModules)) {
                return $this->errorHandler('以下模块信息不完整：' . implode('、', $incompleteModules), 400);
            }

            // 提交补件
            $supplement->submitByCustomer(
                $moduleStatus,
                $request->input('notes')
            );

            return $this->successHandler('补件提交成功', 200);

        } catch (\Exception $e) {
            \Log::error('提交补件失败: ' . $e->getMessage());
            return $this->errorHandler('提交补件失败', 500);
        }
    }

    /**
     * 撤销补件
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function withdraw(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 从请求中获取撤销原因（兼容POST请求体和URL参数）
            $withdrawReason = $request->input('withdraw_reason') ?: $request->get('withdraw_reason');
            
            // 验证撤销原因
            $validator = Validator::make(['withdraw_reason' => $withdrawReason], [
                'withdraw_reason' => 'required|string|max:500'
            ], [
                'withdraw_reason.required' => '撤销原因不能为空',
                'withdraw_reason.max' => '撤销原因不能超过500个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $supplement = CustomerSupplement::where('id', $id)
                ->forUser($user->id)
                ->first();

            if (!$supplement) {
                return $this->errorHandler('补件记录不存在', 404);
            }

            if (!in_array($supplement->status, [CustomerSupplement::STATUS_PENDING, CustomerSupplement::STATUS_REJECTED])) {
                return $this->errorHandler('当前状态不允许撤销', 400);
            }

            $supplement->withdraw($withdrawReason, $user->id);

            return $this->successHandler('撤销成功', 200);

        } catch (\Exception $e) {
            \Log::error('撤销补件失败: ' . $e->getMessage());
            return $this->errorHandler('撤销补件失败', 500);
        }
    }

    /**
     * 获取补件统计信息
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $stats = [
                'pending' => CustomerSupplement::forUser($user->id)->pending()->count(),
                'submitted' => CustomerSupplement::forUser($user->id)->submitted()->count(),
                'rejected' => CustomerSupplement::forUser($user->id)->rejected()->count(),
                'overdue' => CustomerSupplement::forUser($user->id)->overdue()->count(),
                'expiring_soon' => CustomerSupplement::forUser($user->id)->expiringSoon()->count()
            ];

            return $this->successHandler('获取统计信息成功', 200, $stats);

        } catch (\Exception $e) {
            \Log::error('获取补件统计失败: ' . $e->getMessage());
            return $this->errorHandler('获取补件统计失败', 500);
        }
    }

    /**
     * 格式化补件数据
     * 
     * @param CustomerSupplement $supplement
     * @param bool $detail 是否详情模式
     * @return array
     */
    private function formatSupplementData(CustomerSupplement $supplement, $detail = false)
    {
        $data = [
            'id' => $supplement->id,
            'supplement_no' => $supplement->supplement_no,
            'status' => $supplement->status,
            'status_text' => $supplement->status_text,
            'reason' => $supplement->reason,
            'deadline' => $supplement->deadline ? $supplement->deadline->format('Y-m-d H:i:s') : null,
            'created_time' => $supplement->created_time ? $supplement->created_time->format('Y-m-d H:i:s') : null,
            'submitted_time' => $supplement->submitted_time ? $supplement->submitted_time->format('Y-m-d H:i:s') : null,
            'reviewed_time' => $supplement->reviewed_time ? $supplement->reviewed_time->format('Y-m-d H:i:s') : null,
            'review_notes' => $supplement->review_notes,
            'is_overdue' => $supplement->isOverdue(),
            'remaining_days' => $supplement->getRemainingDays(),
            
            // 关联的业务申请信息
            'application' => [
                'id' => $supplement->application->id,
                'application_no' => $supplement->application->application_no,
                'customer_name' => $supplement->application->customer_name,
                'customer_phone' => $supplement->application->customer_phone,
                'customer_id_card' => $supplement->application->customer_id_card,
                'product_name' => $supplement->application->product_name,
                'loan_amount' => $supplement->application->loan_amount,
                'loan_period' => $supplement->application->loan_period,
                'customer_data' => $supplement->application->customer_data,
                'contacts' => $supplement->application->contacts,
                'assets' => $supplement->application->assets,
                'liabilities' => $supplement->application->liabilities,
                'attachments' => $supplement->application->attachments,
                'submit_time' => $supplement->application->submit_time ? $supplement->application->submit_time->format('Y-m-d H:i:s') : null
            ]
        ];

        // 详情模式下返回更多信息
        if ($detail) {
            $data['requirements'] = $supplement->requirements;
            $data['required_documents'] = $supplement->required_documents;
            $data['submitted_documents'] = $supplement->submitted_documents;
            $data['customer_notes'] = $supplement->customer_notes;
            $data['review_notes'] = $supplement->review_notes;
            $data['operation_log'] = $supplement->operation_log;
            $data['withdrawn_time'] = $supplement->withdrawn_time ? $supplement->withdrawn_time->format('Y-m-d H:i:s') : null;
            $data['withdraw_reason'] = $supplement->withdraw_reason;
            
            if ($supplement->reviewer) {
                $data['reviewer'] = [
                    'name' => $supplement->reviewer->name,
                    'reviewed_time' => $supplement->reviewed_time ? $supplement->reviewed_time->format('Y-m-d H:i:s') : null
                ];
            }
        }

        return $data;
    }
} 