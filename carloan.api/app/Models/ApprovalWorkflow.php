<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApprovalWorkflow extends BaseModel
{
    use SoftDeletes;

    protected $table = 'approval_workflows';

    protected $fillable = [
        'business_type',
        'step_name',
        'step_code',
        'step_order',
        'approver_type',
        'approver_ids',
        'cc_user_ids',
        'is_required',
        'auto_approve_conditions',
        'timeout_hours',
        'status'
    ];

    protected $casts = [
        'approver_ids' => 'array',
        'cc_user_ids' => 'array',
        'auto_approve_conditions' => 'array',
        'is_required' => 'boolean',
        'timeout_hours' => 'integer',
        'status' => 'boolean',
        'deleted_at' => 'datetime'
    ];

    // 业务类型常量
    const BUSINESS_TYPE_APPLICATION = 'business_application';
    const BUSINESS_TYPE_INTERVIEW = 'interview_appointment';
    const BUSINESS_TYPE_CONTRACT = 'contract_process';
    const BUSINESS_TYPE_SUPPLEMENT = 'supplement_review';

    // 审批人类型常量
    const APPROVER_TYPE_USER = 'user';
    const APPROVER_TYPE_ROLE = 'role';
    const APPROVER_TYPE_SYSTEM = 'system';

    /**
     * 关联审批任务
     */
    public function approvalTasks()
    {
        return $this->hasMany(ApprovalTask::class, 'workflow_id');
    }

    /**
     * 获取业务类型对应的工作流
     */
    public static function getWorkflowsByBusinessType($businessType)
    {
        return static::where('business_type', $businessType)
            ->where('status', 1)
            ->orderBy('step_order')
            ->get();
    }

    /**
     * 获取指定步骤的工作流配置
     */
    public static function getWorkflowByStep($businessType, $stepCode)
    {
        return static::where('business_type', $businessType)
            ->where('step_code', $stepCode)
            ->where('status', 1)
            ->first();
    }

    /**
     * 获取审批人列表
     */
    public function getApprovers()
    {
        if ($this->approver_type === self::APPROVER_TYPE_USER && $this->approver_ids) {
            return User::whereIn('id', $this->approver_ids)
                ->where('status', 1)
                ->get();
        }
        
        return collect();
    }

    /**
     * 获取抄送人列表
     */
    public function getCcUsers()
    {
        if ($this->cc_user_ids) {
            return User::whereIn('id', $this->cc_user_ids)
                ->where('status', 1)
                ->get();
        }
        
        return collect();
    }

    /**
     * 检查是否满足自动审批条件
     */
    public function checkAutoApproveConditions($application)
    {
        if (!$this->auto_approve_conditions) {
            return false;
        }

        // 这里可以根据具体业务逻辑实现自动审批条件检查
        // 例如：金额小于某个值、风险评分高于某个值等
        
        return false;
    }
} 