-- 销售线索相关表结构
-- 创建时间: 2024-03-17
-- 说明: 销售线索管理表，包含线索信息、跟进记录等

-- 1. 线索来源表
CREATE TABLE IF NOT EXISTS `lead_sources` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '来源名称',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '来源代码',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lead_sources_code_unique` (`code`),
  KEY `lead_sources_status_index` (`status`),
  KEY `lead_sources_sort_order_index` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索来源表';

-- 2. 销售线索表  
CREATE TABLE IF NOT EXISTS `sales_leads` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `lead_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '线索编号',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `channel_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道码',
  `source_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '线索来源ID',
  `source_detail` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源详情',
  
  -- 客户信息
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
  `customer_id_card` varchar(18) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户身份证号',
  `customer_age` int(11) DEFAULT NULL COMMENT '客户年龄',
  `customer_gender` tinyint(4) DEFAULT NULL COMMENT '性别:1男,2女',
  `customer_city` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所在城市',
  `customer_occupation` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职业',
  `customer_income` decimal(12,2) DEFAULT NULL COMMENT '月收入',
  
  -- 意向信息
  `intent_level` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'low' COMMENT '意向等级:high高,medium中,low低',
  `intent_brand` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '意向品牌',
  `intent_model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '意向车型',
  `intent_price_min` decimal(12,2) DEFAULT NULL COMMENT '预算最低价',
  `intent_price_max` decimal(12,2) DEFAULT NULL COMMENT '预算最高价',
  `intent_loan_amount` decimal(12,2) DEFAULT NULL COMMENT '意向贷款金额',
  `intent_loan_period` int(11) DEFAULT NULL COMMENT '意向贷款期限(月)',
  `purchase_timeline` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车时间:immediate立即,within_month一个月内,within_quarter三个月内,later三个月后',
  
  -- 线索状态
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'new' COMMENT '线索状态:new新线索,contacted已联系,follow_up跟进中,intent_strong强意向,converted已转化,lost已流失,invalid无效',
  `status_text` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '新线索' COMMENT '状态文本',
  `priority` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '优先级:high高,normal普通,low低',
  
  -- 转化信息
  `is_converted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已转化:1是,0否',
  `converted_application_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '转化的业务申请ID',
  `converted_time` timestamp NULL DEFAULT NULL COMMENT '转化时间',
  
  -- 其他信息
  `last_contact_time` timestamp NULL DEFAULT NULL COMMENT '最后联系时间',
  `next_follow_time` timestamp NULL DEFAULT NULL COMMENT '下次跟进时间',
  `follow_count` int(11) NOT NULL DEFAULT '0' COMMENT '跟进次数',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `tags` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签(逗号分隔)',
  
  -- 时间字段
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_leads_lead_no_unique` (`lead_no`),
  KEY `sales_leads_user_id_index` (`user_id`),
  KEY `sales_leads_channel_code_index` (`channel_code`),
  KEY `sales_leads_source_id_index` (`source_id`),
  KEY `sales_leads_customer_phone_index` (`customer_phone`),
  KEY `sales_leads_status_index` (`status`),
  KEY `sales_leads_intent_level_index` (`intent_level`),
  KEY `sales_leads_priority_index` (`priority`),
  KEY `sales_leads_is_converted_index` (`is_converted`),
  KEY `sales_leads_user_id_status_index` (`user_id`,`status`),
  KEY `sales_leads_next_follow_time_index` (`next_follow_time`),
  KEY `sales_leads_created_at_index` (`created_at`),
  KEY `sales_leads_deleted_at_index` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售线索表';

-- 3. 线索跟进记录表
CREATE TABLE IF NOT EXISTS `sales_lead_follow_ups` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `lead_id` bigint(20) UNSIGNED NOT NULL COMMENT '线索ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '跟进人ID',
  `follow_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '跟进方式:phone电话,wechat微信,meeting面谈,email邮件,sms短信,other其他',
  `follow_result` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '跟进结果:success成功联系,no_answer无人接听,busy线路忙,refused拒绝,interested有兴趣,not_interested无兴趣,other其他',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '跟进内容',
  `next_follow_time` timestamp NULL DEFAULT NULL COMMENT '下次跟进时间',
  `follow_duration` int(11) DEFAULT NULL COMMENT '跟进时长(分钟)',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  KEY `sales_lead_follow_ups_lead_id_index` (`lead_id`),
  KEY `sales_lead_follow_ups_user_id_index` (`user_id`),
  KEY `sales_lead_follow_ups_follow_type_index` (`follow_type`),
  KEY `sales_lead_follow_ups_follow_result_index` (`follow_result`),
  KEY `sales_lead_follow_ups_next_follow_time_index` (`next_follow_time`),
  KEY `sales_lead_follow_ups_created_at_index` (`created_at`),
  KEY `sales_lead_follow_ups_deleted_at_index` (`deleted_at`),
  CONSTRAINT `sales_lead_follow_ups_lead_id_foreign` FOREIGN KEY (`lead_id`) REFERENCES `sales_leads` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进记录表';

-- 4. 插入线索来源基础数据
INSERT IGNORE INTO `lead_sources` (`name`, `code`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('网络咨询', 'online', '通过官网、微信等在线渠道获得的线索', 1, 1, NOW(), NOW()),
('电话咨询', 'phone', '客户主动电话咨询', 1, 2, NOW(), NOW()),
('朋友推荐', 'referral', '现有客户或朋友推荐', 1, 3, NOW(), NOW()),
('广告投放', 'advertising', '通过广告投放获得的线索', 1, 4, NOW(), NOW()),
('展厅接待', 'showroom', '到店咨询的客户', 1, 5, NOW(), NOW()),
('社交媒体', 'social', '通过社交媒体平台获得', 1, 6, NOW(), NOW()),
('合作伙伴', 'partner', '合作渠道推荐', 1, 7, NOW(), NOW()),
('其他渠道', 'other', '其他未分类的线索来源', 1, 8, NOW(), NOW());

-- 5. 创建线索统计视图
CREATE OR REPLACE VIEW `v_sales_lead_summary` AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    -- 总线索数
    COALESCE(total_leads.count, 0) as total_leads_count,
    -- 新线索数
    COALESCE(new_leads.count, 0) as new_leads_count,
    -- 跟进中线索数
    COALESCE(follow_up_leads.count, 0) as follow_up_leads_count,
    -- 强意向线索数
    COALESCE(strong_intent_leads.count, 0) as strong_intent_leads_count,
    -- 已转化线索数
    COALESCE(converted_leads.count, 0) as converted_leads_count,
    -- 待跟进线索数（下次跟进时间在今天之前）
    COALESCE(pending_follow_leads.count, 0) as pending_follow_leads_count,
    -- 本月新增线索数
    COALESCE(month_new_leads.count, 0) as month_new_leads_count,
    -- 转化率
    CASE 
        WHEN COALESCE(total_leads.count, 0) > 0 
        THEN ROUND(COALESCE(converted_leads.count, 0) * 100.0 / total_leads.count, 2)
        ELSE 0 
    END as conversion_rate
FROM users u
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE deleted_at IS NULL
    GROUP BY user_id
) total_leads ON u.id = total_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE status = 'new' AND deleted_at IS NULL
    GROUP BY user_id
) new_leads ON u.id = new_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE status IN ('contacted', 'follow_up') AND deleted_at IS NULL
    GROUP BY user_id
) follow_up_leads ON u.id = follow_up_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE status = 'intent_strong' AND deleted_at IS NULL
    GROUP BY user_id
) strong_intent_leads ON u.id = strong_intent_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE is_converted = 1 AND deleted_at IS NULL
    GROUP BY user_id
) converted_leads ON u.id = converted_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE status NOT IN ('converted', 'lost', 'invalid') 
    AND next_follow_time IS NOT NULL 
    AND next_follow_time <= NOW()
    AND deleted_at IS NULL
    GROUP BY user_id
) pending_follow_leads ON u.id = pending_follow_leads.user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM sales_leads 
    WHERE DATE(created_at) >= DATE(DATE_SUB(NOW(), INTERVAL DAY(NOW())-1 DAY))
    AND deleted_at IS NULL
    GROUP BY user_id
) month_new_leads ON u.id = month_new_leads.user_id
WHERE u.status = 1 AND u.deleted_at IS NULL;

-- 6. 验证表创建结果
SELECT 'lead_sources' as table_name, COUNT(*) as record_count FROM lead_sources
UNION ALL
SELECT 'sales_leads' as table_name, COUNT(*) as record_count FROM sales_leads  
UNION ALL
SELECT 'sales_lead_follow_ups' as table_name, COUNT(*) as record_count FROM sales_lead_follow_ups;

-- 7. 可选：添加外键约束（在确认相关表存在后再执行）
-- 注意：请在确认以下表存在后再取消注释执行

-- 添加sales_leads表的外键约束
-- ALTER TABLE `sales_leads` 
-- ADD CONSTRAINT `sales_leads_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `sales_leads` 
-- ADD CONSTRAINT `sales_leads_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `lead_sources` (`id`) ON DELETE SET NULL;

-- 如果business_applications表存在，可以添加这个约束
-- ALTER TABLE `sales_leads` 
-- ADD CONSTRAINT `sales_leads_converted_application_id_foreign` FOREIGN KEY (`converted_application_id`) REFERENCES `business_applications` (`id`) ON DELETE SET NULL;

-- 添加sales_lead_follow_ups表的外键约束
-- ALTER TABLE `sales_lead_follow_ups` 
-- ADD CONSTRAINT `sales_lead_follow_ups_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- 8. 清理脚本（备用，慎用）
-- DROP VIEW IF EXISTS v_sales_lead_summary;
-- DELETE FROM sales_lead_follow_ups;
-- DELETE FROM sales_leads;
-- DELETE FROM lead_sources;
-- DROP TABLE IF EXISTS sales_lead_follow_ups;
-- DROP TABLE IF EXISTS sales_leads;
-- DROP TABLE IF EXISTS lead_sources; 