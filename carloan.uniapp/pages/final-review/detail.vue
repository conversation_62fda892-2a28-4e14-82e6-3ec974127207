<template>
	<!-- 终审详情 -->
	<view class="final-review-detail">
		<navbar :title="pageTitle" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 详情内容 -->
		<view class="content" v-else-if="detail">
			<!-- 审批状态 -->
			<view class="section">
				<view class="section-title">审批状态</view>
				<view class="section-content">
					<view class="status-info">
						<view class="status-badge" :class="getStatusClass(detail.status)">
							{{ detail.status_text }}
						</view>
						<view class="status-desc" v-if="detail.status_description">
							{{ detail.status_description }}
						</view>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="section">
				<view class="section-title">基本信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">业务单号：</view>
						<view class="info-value">{{ detail.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">客户姓名：</view>
						<view class="info-value">{{ detail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">联系电话：</view>
						<view class="info-value">{{ detail.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">产品名称：</view>
						<view class="info-value">{{ detail.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">申请金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_amount) }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">提交时间：</view>
						<view class="info-value">{{ detail.submit_time || detail.created_time }}</view>
					</view>
				</view>
			</view>
			
			<!-- 审批进度 -->
			<view class="section">
				<view class="section-title">审批进度</view>
				<view class="section-content">
					<view class="progress-timeline">
						<view 
							class="timeline-item" 
							v-for="(step, index) in detail.review_steps" 
							:key="index"
							:class="{ active: step.status === 'completed', current: step.status === 'current' }"
						>
							<view class="timeline-dot">
								<image 
									class="dot-icon" 
									:src="getTimelineIcon(step.status)" 
								/>
							</view>
							<view class="timeline-content">
								<view class="timeline-title">{{ step.title }}</view>
								<view class="timeline-desc" v-if="step.description">{{ step.description }}</view>
								<view class="timeline-time" v-if="step.time">{{ step.time }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 终审结果 -->
			<view class="section" v-if="detail.final_review_result">
				<view class="section-title">{{ detail.review_type === 'secondary_review' ? '复审结果' : '终审结果' }}</view>
				<view class="section-content">
					<view class="review-result">
						<view class="result-status" :class="getResultClass(detail.final_review_result.result)">
							{{ detail.final_review_result.result_text }}
						</view>
						<view class="result-info">
							<view class="info-item" v-if="detail.final_review_result.reviewer">
								<view class="info-label">审批人：</view>
								<view class="info-value">{{ detail.final_review_result.reviewer.name }}</view>
							</view>
							<view class="info-item" v-if="detail.final_review_result.review_time">
								<view class="info-label">审批时间：</view>
								<view class="info-value">{{ detail.final_review_result.review_time }}</view>
							</view>
							<view class="info-item" v-if="detail.final_review_result.approved_amount">
								<view class="info-label">批准金额：</view>
								<view class="info-value amount">¥{{ formatAmount(detail.final_review_result.approved_amount) }}</view>
							</view>
							<view class="info-item" v-if="detail.final_review_result.approved_rate">
								<view class="info-label">批准利率：</view>
								<view class="info-value">{{ detail.final_review_result.approved_rate }}%</view>
							</view>
							<view class="info-item" v-if="detail.final_review_result.approved_term">
								<view class="info-label">批准期限：</view>
								<view class="info-value">{{ detail.final_review_result.approved_term }}个月</view>
							</view>
						</view>
						<view class="result-notes" v-if="detail.final_review_result.review_notes">
							<view class="notes-label">审批意见：</view>
							<view class="notes-content">{{ detail.final_review_result.review_notes }}</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 风险评估 -->
			<view class="section" v-if="detail.risk_assessment">
				<view class="section-title">风险评估</view>
				<view class="section-content">
					<view class="risk-info">
						<view class="risk-level" :class="getRiskLevelClass(detail.risk_assessment.risk_level)">
							{{ detail.risk_assessment.risk_level_text }}
						</view>
						<view class="risk-score" v-if="detail.risk_assessment.risk_score">
							<view class="score-label">风险评分：</view>
							<view class="score-value">{{ detail.risk_assessment.risk_score }}分</view>
						</view>
						<view class="risk-factors" v-if="detail.risk_assessment.risk_factors && detail.risk_assessment.risk_factors.length > 0">
							<view class="factors-label">风险因素：</view>
							<view class="factors-list">
								<view 
									class="factor-item" 
									v-for="(factor, index) in detail.risk_assessment.risk_factors" 
									:key="index"
								>
									{{ factor }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 操作记录 -->
			<view class="section" v-if="detail.operation_logs && detail.operation_logs.length > 0">
				<view class="section-title">操作记录</view>
				<view class="section-content">
					<view class="operation-list">
						<view 
							class="operation-item" 
							v-for="(log, index) in detail.operation_logs" 
							:key="index"
						>
							<view class="operation-dot"></view>
							<view class="operation-content">
								<view class="operation-desc">{{ log.description }}</view>
								<view class="operation-operator" v-if="log.operator">操作人：{{ log.operator.name }}</view>
								<view class="operation-time">{{ log.created_time }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else>
			<view class="error-icon">❌</view>
			<view class="error-text">{{ errorMessage || '数据加载失败' }}</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 响应式数据
const loading = ref(true)
const detail = ref(null)
const errorMessage = ref('')
const applicationId = ref('')

// 计算属性
const pageTitle = computed(() => {
	if (detail.value) {
		return detail.value.review_type === 'secondary_review' ? '复审详情' : '终审详情'
	}
	return '审批详情'
})

// 页面加载
onLoad((options) => {
	if (options.id) {
		applicationId.value = options.id
		loadDetail()
	} else {
		errorMessage.value = '参数错误'
		loading.value = false
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		errorMessage.value = ''
		
		const response = await request.get(`/business-applications/${applicationId.value}/final-review`)
		
		console.log('终审详情API Response:', response)
		
		if (response && response.status_code === 200) {
			detail.value = response.data
		} else {
			errorMessage.value = response?.message || '获取数据失败'
		}
	} catch (error) {
		console.error('获取终审详情失败:', error)
		errorMessage.value = '网络错误，请重试'
	} finally {
		loading.value = false
	}
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return Number(amount).toLocaleString()
}

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		'final_review': 'warning',
		'secondary_review': 'info',
		'approved': 'success',
		'rejected': 'error'
	}
	return classMap[status] || 'default'
}

// 获取时间轴图标
const getTimelineIcon = (status) => {
	const iconMap = {
		'completed': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-green.png',
		'current': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-blue.png',
		'pending': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-grey.png'
	}
	return iconMap[status] || iconMap['pending']
}

// 获取结果样式类
const getResultClass = (result) => {
	const classMap = {
		'approved': 'success',
		'rejected': 'error',
		'conditional': 'warning'
	}
	return classMap[result] || 'default'
}

// 获取风险等级样式类
const getRiskLevelClass = (level) => {
	const classMap = {
		'low': 'success',
		'medium': 'warning',
		'high': 'error'
	}
	return classMap[level] || 'default'
}
</script>

<style lang="scss" scoped>
.final-review-detail {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		background: #f8f9fa;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.section-content {
		padding: 30rpx;
	}
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.info-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
		flex-shrink: 0;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		
		&.amount {
			color: #e6a23c;
			font-weight: 500;
		}
	}
}

.status-info {
	text-align: center;
	
	.status-badge {
		display: inline-block;
		padding: 12rpx 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		
		&.warning {
			background: #fff7e6;
			color: #fa8c16;
		}
		
		&.info {
			background: #e6f7ff;
			color: #1890ff;
		}
		
		&.success {
			background: #f6ffed;
			color: #52c41a;
		}
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
		
		&.default {
			background: #f0f0f0;
			color: #666;
		}
	}
	
	.status-desc {
		margin-top: 20rpx;
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
}

.progress-timeline {
	.timeline-item {
		display: flex;
		margin-bottom: 40rpx;
		position: relative;
		
		&:last-child {
			margin-bottom: 0;
			
			&::after {
				display: none;
			}
		}
		
		&::after {
			content: '';
			position: absolute;
			left: 19rpx;
			top: 38rpx;
			width: 2rpx;
			height: calc(100% + 20rpx);
			background: #ddd;
			z-index: 0;
		}
		
		&.active::after {
			background: #52c41a;
		}
		
		.timeline-dot {
			width: 38rpx;
			height: 38rpx;
			margin-right: 20rpx;
			position: relative;
			z-index: 1;
			
			.dot-icon {
				width: 100%;
				height: 100%;
			}
		}
		
		.timeline-content {
			flex: 1;
			
			.timeline-title {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.timeline-desc {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 8rpx;
			}
			
			.timeline-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.review-result {
	.result-status {
		text-align: center;
		padding: 16rpx 32rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: 500;
		margin-bottom: 30rpx;
		
		&.success {
			background: #f6ffed;
			color: #52c41a;
		}
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
		
		&.warning {
			background: #fff7e6;
			color: #fa8c16;
		}
	}
	
	.result-info {
		margin-bottom: 30rpx;
	}
	
	.result-notes {
		.notes-label {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 16rpx;
		}
		
		.notes-content {
			padding: 20rpx;
			background: #f8f9fa;
			border-radius: 8rpx;
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
		}
	}
}

.risk-info {
	.risk-level {
		text-align: center;
		padding: 16rpx 32rpx;
		border-radius: 12rpx;
		font-size: 30rpx;
		font-weight: 500;
		margin-bottom: 30rpx;
		
		&.success {
			background: #f6ffed;
			color: #52c41a;
		}
		
		&.warning {
			background: #fff7e6;
			color: #fa8c16;
		}
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
	}
	
	.risk-score {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.score-label {
			font-size: 28rpx;
			color: #666;
			margin-right: 16rpx;
		}
		
		.score-value {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
	}
	
	.risk-factors {
		.factors-label {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
		}
		
		.factors-list {
			.factor-item {
				background: #fff7e6;
				color: #fa8c16;
				padding: 8rpx 16rpx;
				border-radius: 6rpx;
				font-size: 26rpx;
				display: inline-block;
				margin-right: 16rpx;
				margin-bottom: 12rpx;
			}
		}
	}
}

.operation-list {
	.operation-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 30rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.operation-dot {
			width: 12rpx;
			height: 12rpx;
			background: #1890ff;
			border-radius: 50%;
			margin-right: 20rpx;
			margin-top: 10rpx;
			flex-shrink: 0;
		}
		
		.operation-content {
			flex: 1;
			
			.operation-desc {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.operation-operator {
				font-size: 24rpx;
				color: #666;
				margin-bottom: 4rpx;
			}
			
			.operation-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.error-state {
	padding: 100rpx 40rpx;
	text-align: center;
	
	.error-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
	}
	
	.btn {
		height: 76rpx;
		padding: 20rpx 32rpx;
		border-radius: 8rpx;
		border: none;
		font-size: 28rpx;
		
		&.btn-primary {
			background: #333;
			color: #fff;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 