<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\BusinessApplication;
use App\Services\ESignService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ContractController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 获取签约列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 10);

            // 查询待签约和签约中的业务申请
            $query = BusinessApplication::with(['user'])
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_CONTRACT_PENDING,
                    BusinessApplication::STATUS_CONTRACT_PROCESSING,
                    BusinessApplication::STATUS_CONTRACT_COMPLETED
                ])
                ->orderBy('approval_time', 'desc');

            // 分页查询
            $applications = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $data = [
                'list' => $applications->items() ? array_map(function ($application) {
                    return $this->formatContractData($application);
                }, $applications->items()) : [],
                'pagination' => [
                    'total' => $applications->total(),
                    'per_page' => $applications->perPage(),
                    'current_page' => $applications->currentPage(),
                    'last_page' => $applications->lastPage(),
                    'has_more' => $applications->hasMorePages()
                ]
            ];

            return $this->successHandler('获取签约列表成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取签约列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取签约列表失败', 500);
        }
    }

    /**
     * 获取签约详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::with(['user'])
                ->where('id', $id)
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_CONTRACT_PENDING,
                    BusinessApplication::STATUS_CONTRACT_PROCESSING,
                    BusinessApplication::STATUS_CONTRACT_COMPLETED
                ])
                ->first();

            if (!$application) {
                return $this->errorHandler('签约记录不存在', 404);
            }

            $data = $this->formatContractData($application, true);

            return $this->successHandler('获取签约详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取签约详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取签约详情失败', 500);
        }
    }

    /**
     * 发起签约
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function initiate(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'contract_type' => 'nullable|string|max:50',
                'signing_method' => 'required|string|in:electronic,offline',
                'notes' => 'nullable|string|max:1000'
            ], [
                'signing_method.required' => '请选择签约方式',
                'signing_method.in' => '签约方式格式错误',
                'notes.max' => '备注不能超过1000个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->where('status', BusinessApplication::STATUS_CONTRACT_PENDING)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }



            DB::beginTransaction();

            // 发起签约
            $this->startContractProcess($application, $request->all());

            DB::commit();

            return $this->successHandler('签约已发起', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('发起签约失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('发起签约失败', 500);
        }
    }

    /**
     * 完成签约
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function complete(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'contract_files' => 'nullable|array',
                'signing_notes' => 'nullable|string|max:1000'
            ], [
                'signing_notes.max' => '签约备注不能超过1000个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->where('status', BusinessApplication::STATUS_CONTRACT_PROCESSING)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            DB::beginTransaction();

            // 完成签约
            $this->completeContractProcess($application, $request->all());

            DB::commit();

            return $this->successHandler('签约已完成，业务审批通过', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('完成签约失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('完成签约失败', 500);
        }
    }

    /**
     * 获取业务进度
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function progress($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在', 404);
            }

            $data = $this->formatProgressData($application);

            return $this->successHandler('获取业务进度成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取业务进度失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取业务进度失败', 500);
        }
    }

    /**
     * 开始签约流程
     */
    private function startContractProcess(BusinessApplication $application, $contractData)
    {
        try {
            // 调用e签宝创建签署流程
            $eSignService = new ESignService();
            $flowId = $eSignService->createSignFlow([
                'application_no' => $application->application_no,
                'customer_name' => $application->customer_name,
                'customer_id_card' => $application->customer_id_card,
                'customer_phone' => $application->customer_phone,
                'product_name' => $application->product_name,
                'loan_amount' => $application->loan_amount,
                'loan_period' => $application->loan_period,
                'interest_rate' => $application->interest_rate,
                'vehicle_brand' => $application->vehicle_brand,
                'vehicle_model' => $application->vehicle_model,
                'vehicle_vin' => $application->vehicle_vin,
                'vehicle_year' => $application->vehicle_year,
                'vehicle_price' => $application->vehicle_price
            ]);

            // 更新业务申请状态
            $application->update([
                'status' => BusinessApplication::STATUS_CONTRACT_PROCESSING,
                'status_text' => '签约中',
                'approval_notes' => '已发起电子签约，签约流程ID: ' . $flowId
            ]);

            // 记录签约信息
            $this->recordApprovalHistory($application, 'contract_initiated', 'processing', 
                '发起电子签约，流程ID: ' . $flowId . '，' . ($contractData['notes'] ?? ''));
                
        } catch (\Exception $e) {
            \Log::error('发起e签宝签约失败: ' . $e->getMessage());
            
            // 如果e签宝调用失败，仍然更新状态但记录错误
            $application->update([
                'status' => BusinessApplication::STATUS_CONTRACT_PROCESSING,
                'status_text' => '签约中',
                'approval_notes' => '签约发起失败: ' . $e->getMessage()
            ]);

            $this->recordApprovalHistory($application, 'contract_initiated', 'processing', 
                '签约发起失败: ' . $e->getMessage());
        }
    }

    /**
     * 完成签约流程
     */
    private function completeContractProcess(BusinessApplication $application, $contractData)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_CONTRACT_COMPLETED,
            'status_text' => '签约完成'
        ]);

        // 如果签约完成，自动标记为审批通过
        $this->finalizeApplication($application, $contractData['signing_notes'] ?? null);
    }

    /**
     * 最终完成业务申请
     */
    private function finalizeApplication(BusinessApplication $application, $notes = null)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_APPROVED,
            'status_text' => '审批通过',
            'approval_time' => Carbon::now()
        ]);

        $this->recordApprovalHistory($application, 'contract_completed', 'approved', $notes);
    }

    /**
     * 记录审批历史
     */
    private function recordApprovalHistory(BusinessApplication $application, $stage, $status, $notes = null)
    {
        $approvalHistory = $application->approval_history ?: [];
        $approvalHistory[] = [
            'stage' => $stage,
            'status' => $status,
            'operator_id' => Auth::id(),
            'operator_name' => Auth::user()->name,
            'notes' => $notes,
            'created_at' => Carbon::now()->toDateTimeString()
        ];
        $application->update(['approval_history' => $approvalHistory]);
    }

    /**
     * 格式化签约数据
     * 
     * @param BusinessApplication $application
     * @param bool $detail
     * @return array
     */
    private function formatContractData(BusinessApplication $application, $detail = false)
    {
        $data = [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'customer_name' => $application->customer_name,
            'customer_phone' => $application->customer_phone,
            'product_name' => $application->product_name,
            'loan_amount' => $application->loan_amount,
            'status' => $application->status,
            'status_text' => $application->status_text,
            'submit_time' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : null,
            'approval_time' => $application->approval_time ? $application->approval_time->format('Y-m-d H:i:s') : null,
            'created_time' => $application->created_at->format('Y-m-d H:i:s'),
            
            // 操作权限
            'can_initiate' => $application->status === BusinessApplication::STATUS_CONTRACT_PENDING,
            'can_complete' => $application->status === BusinessApplication::STATUS_CONTRACT_PROCESSING,
            'can_view_progress' => in_array($application->status, [
                BusinessApplication::STATUS_CONTRACT_PROCESSING,
                BusinessApplication::STATUS_CONTRACT_COMPLETED,
                BusinessApplication::STATUS_APPROVED
            ])
        ];

        if ($detail) {
            $data = array_merge($data, [
                'customer_id_card' => $application->customer_id_card,
                'loan_period' => $application->loan_period,
                'interest_rate' => $application->interest_rate,
                'vehicle_brand' => $application->vehicle_brand,
                'vehicle_model' => $application->vehicle_model,
                'vehicle_vin' => $application->vehicle_vin,
                'vehicle_year' => $application->vehicle_year,
                'vehicle_price' => $application->vehicle_price,
                'approval_notes' => $application->approval_notes,
                'approval_history' => $application->approval_history
            ]);
        }

        return $data;
    }

    /**
     * 格式化进度数据
     */
    private function formatProgressData(BusinessApplication $application)
    {
        $history = $application->approval_history ?: [];

        // 定义标准流程步骤
        $standardSteps = [
            'submitted' => '业务提交',
            'initial_review' => '初审',
            'pre_approval' => '预审',
            'interview_completed' => '面审完成',
            'final_review' => '终审',
            'secondary_review' => '复审',
            'contract_initiated' => '发起签约',
            'contract_completed' => '签约完成'
        ];

        $currentStepIndex = 0;
        $steps = [];

        foreach ($standardSteps as $stepKey => $stepName) {
            $stepHistory = collect($history)->firstWhere('stage', $stepKey);
            $isCompleted = $stepHistory !== null;
            $isCurrent = false;

            // 判断当前步骤
            if ($application->status === BusinessApplication::STATUS_SUBMITTED && $stepKey === 'submitted') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_INITIAL_REVIEW && $stepKey === 'initial_review') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_PRE_APPROVAL && $stepKey === 'pre_approval') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_INTERVIEW_COMPLETED && $stepKey === 'interview_completed') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_FINAL_REVIEW && $stepKey === 'final_review') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_SECONDARY_REVIEW && $stepKey === 'secondary_review') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif ($application->status === BusinessApplication::STATUS_CONTRACT_PENDING && $stepKey === 'contract_initiated') {
                $isCurrent = true;
                $currentStepIndex = count($steps);
            } elseif (in_array($application->status, [
                BusinessApplication::STATUS_CONTRACT_PROCESSING,
                BusinessApplication::STATUS_CONTRACT_COMPLETED,
                BusinessApplication::STATUS_APPROVED
            ]) && $stepKey === 'contract_completed') {
                $isCurrent = !$isCompleted;
                if ($isCurrent) {
                    $currentStepIndex = count($steps);
                }
            }

            $steps[] = [
                'key' => $stepKey,
                'name' => $stepName,
                'status' => $isCompleted ? 'completed' : ($isCurrent ? 'current' : 'pending'),
                'time' => $stepHistory ? $stepHistory['created_at'] : null,
                'notes' => $stepHistory ? $stepHistory['notes'] : null
            ];
        }

        return [
            'application' => $this->formatContractData($application, true),
            'current_step' => $currentStepIndex,
            'steps' => $steps,
            'progress_percentage' => round(($currentStepIndex + 1) / count($steps) * 100)
        ];
    }

    /**
     * 签约成功页面
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function success(Request $request)
    {
        $flowId = $request->query('flowId');
        $message = $request->query('message', '签约成功');
        
        // 如果有flowId，可以查询签约状态
        if ($flowId) {
            try {
                $eSignService = new ESignService();
                $signStatus = $eSignService->getSignStatus($flowId);
                \Log::info('签约成功页面访问', [
                    'flow_id' => $flowId,
                    'sign_status' => $signStatus
                ]);
            } catch (\Exception $e) {
                \Log::error('查询签约状态失败: ' . $e->getMessage());
            }
        }
        
        // 返回一个简单的HTML页面
        $html = '
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>签约成功</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                }
                .success-container {
                    background: white;
                    padding: 40px 30px;
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 400px;
                    width: 100%;
                }
                .success-icon {
                    font-size: 60px;
                    color: #52c41a;
                    margin-bottom: 20px;
                }
                .success-title {
                    font-size: 24px;
                    color: #333;
                    margin-bottom: 16px;
                    font-weight: 600;
                }
                .success-message {
                    font-size: 16px;
                    color: #666;
                    line-height: 1.5;
                    margin-bottom: 30px;
                }
                .success-tips {
                    font-size: 14px;
                    color: #999;
                    background: #f9f9f9;
                    padding: 15px;
                    border-radius: 8px;
                    margin-top: 20px;
                }
            </style>
        </head>
        <body>
            <div class="success-container">
                <div class="success-icon">✓</div>
                <div class="success-title">签约成功</div>
                <div class="success-message">
                    恭喜您！合同已成功签署完成。<br>
                    我们会尽快处理您的申请，请保持手机畅通。
                </div>
                <div class="success-tips">
                    温馨提示：签署完成的合同将通过短信或邮件发送给您，请注意查收。
                </div>
            </div>
        </body>
        </html>';
        
        return response($html, 200, [
            'Content-Type' => 'text/html; charset=UTF-8'
        ]);
    }
} 