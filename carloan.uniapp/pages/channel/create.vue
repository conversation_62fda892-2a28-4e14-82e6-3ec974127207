<template>
	<!-- 渠道管理 -->
	<view class="channel-form">
		<navbar :title="isEditMode ? '编辑渠道' : '新增渠道'" />
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text>加载中...</text>
		</view>
		
		<view class="container" v-else>
			<view class="form-section">
				<view class="form-item">
					<view class="label">渠道码 <text class="required">*</text></view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.code"
							placeholder="请输入渠道码（仅支持数字和字母）"
							maxlength="20"
							:disabled="isEditMode"
						/>
					</view>
					<view class="tip" v-if="formData.code">
						<text class="tip-icon">⚠️</text>
						{{ isEditMode ? '渠道码不可修改' : '渠道码一旦创建不可修改' }}
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">渠道名称 <text class="required">*</text></view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.name"
							placeholder="请输入渠道名称"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">状态</view>
					<view class="switch-container">
						<switch 
							:checked="formData.status === 1" 
							@change="handleStatusChange"
							color="#007AFF"
						/>
						<text class="switch-text">{{ formData.status === 1 ? '启用' : '禁用' }}</text>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">排序</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.sort"
							placeholder="请输入排序数字，数字越小越靠前"
							type="number"
						/>
					</view>
				</view>
			</view>
		</view>
		
		<view class="btn-container" v-if="!loading">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? (isEditMode ? '保存中...' : '提交中...') : (isEditMode ? '保存修改' : '确认新增') }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 页面状态
const channelId = ref('');
const isEditMode = ref(false);
const loading = ref(false);

// 表单数据
const formData = ref({
	code: '',
	name: '',
	status: 1,
	sort: 0
});

const submitLoading = ref(false);

// 计算是否可以提交
const canSubmit = computed(() => {
	return formData.value.code.trim() && formData.value.name.trim();
});

// 页面加载时获取参数
onMounted(() => {
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage.options;
	
	if (options.id) {
		// 编辑模式
		channelId.value = options.id;
		isEditMode.value = true;
		loadChannelDetail();
	} else {
		// 创建模式
		isEditMode.value = false;
		loading.value = false;
	}
});

// 加载渠道详情（编辑模式）
const loadChannelDetail = async () => {
	try {
		loading.value = true;
		const response = await request.get(`/channels/${channelId.value}`);
		
		if (response.status_code === 200) {
			const data = response.data;
			formData.value = {
				code: data.code || '',
				name: data.name || '',
				status: data.status || 1,
				sort: data.sort || 0
			};
		} else {
			uni.showToast({
				title: '获取渠道信息失败',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	} catch (error) {
		console.error('获取渠道详情失败:', error);
		uni.showToast({
			title: '网络错误',
			icon: 'none'
		});
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	} finally {
		loading.value = false;
	}
};

// 状态切换处理
const handleStatusChange = (e) => {
	formData.value.status = e.detail.value ? 1 : 0;
};

// 提交表单
const handleSubmit = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请完善必填信息',
			icon: 'none'
		});
		return;
	}
	
	// 验证渠道码格式
	const codeRegex = /^[A-Za-z0-9]+$/;
	if (!codeRegex.test(formData.value.code)) {
		uni.showToast({
			title: '渠道码只能包含数字和字母',
			icon: 'none'
		});
		return;
	}
	
	submitLoading.value = true;
	
	try {
		const requestData = {
			code: formData.value.code.trim(),
			name: formData.value.name.trim(),
			status: formData.value.status,
			sort: parseInt(formData.value.sort) || 0
		};
		
		let response;
		if (isEditMode.value) {
			// 编辑模式
			response = await request.put(`/channels/${channelId.value}`, requestData);
		} else {
			// 创建模式
			response = await request.post('/channels', requestData);
		}
		
		if (response.status_code === 200) {
			uni.showToast({
				title: isEditMode.value ? '保存成功' : '新增成功',
				icon: 'success'
			});
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		} else {
			uni.showToast({
				title: response.message || (isEditMode.value ? '保存失败' : '新增失败'),
				icon: 'none'
			});
		}
	} catch (error) {
		console.error(`${isEditMode.value ? '保存' : '新增'}渠道失败:`, error);
		uni.showToast({
			title: error.message || (isEditMode.value ? '保存失败' : '新增失败'),
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.channel-form {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 120rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
	
	.loading-container {
		padding: 200rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
	}
	
	.container {
		padding: 40rpx;
		
		.form-section {
			background: #ffffff;
			border-radius: 16rpx;
			padding: 40rpx;
			margin-bottom: 20rpx;
			box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0, 0, 0, 0.06);
			
			.form-item {
				margin-bottom: 40rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					font-weight: 500;
					font-size: 28rpx;
					color: #000000;
					margin-bottom: 20rpx;
					
					.required {
						color: #ff4757;
						margin-left: 4rpx;
					}
				}
				
				.input-container {
					.form-input {
						width: 100%;
						height: 88rpx;
						background: #f8f8f8;
						border-radius: 12rpx;
						padding: 0 24rpx;
						font-size: 28rpx;
						color: #000000;
						box-sizing: border-box;
						border: 2rpx solid transparent;
						
						&:focus {
							background: #ffffff;
							border-color: #007AFF;
						}
						
						&:disabled {
							background: #f0f0f0;
							color: #999999;
						}
						
						&::placeholder {
							color: #999999;
						}
					}
				}
				
				.tip {
					display: flex;
					align-items: center;
					margin-top: 16rpx;
					padding: 16rpx;
					background: #fff9e6;
					border-radius: 8rpx;
					font-size: 24rpx;
					color: #d4851e;
					border: 1rpx solid #ffeaa7;
					width: auto;
					height: auto;
					line-height: auto;
					
					.tip-icon {
						margin-right: 8rpx;
						font-size: 24rpx;
					}
				}
				
				.switch-container {
					display: flex;
					align-items: center;
					
					.switch-text {
						margin-left: 16rpx;
						font-size: 28rpx;
						color: #000000;
					}
				}
			}
		}
	}
	
	.btn-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 40rpx;
		background: #ffffff;
		box-shadow: 0rpx -8rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
		padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
		
		.btn {
			width: 100%;
			height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 500;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&.btn-primary {
				background: #000000;
				color: #ffffff;
				
				&:disabled {
					background: #cccccc;
					color: #999999;
				}
			}
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 