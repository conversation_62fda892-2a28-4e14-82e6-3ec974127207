<template>
  <div class="miniapp-home-config">
    <div class="page-header">
      <h3>小程序首页配置</h3>
      <p>管理小程序首页的Logo和轮播图配置</p>
    </div>

    <el-card class="config-card">
      <div slot="header" class="clearfix">
        <span>Logo配置</span>
      </div>
      <div class="config-item">
        <div class="config-label">首页Logo</div>
        <div class="config-content">
          <el-upload
            class="logo-uploader"
            action=""
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload"
            :http-request="uploadLogo"
          >
                         <img v-if="logoUrl" :src="logoUrl" class="logo-preview">
             <div v-else-if="logoUploading" class="logo-uploader-icon">
               <i class="el-icon-loading"></i>
               <div class="upload-text">上传中...</div>
             </div>
             <div v-else class="logo-uploader-icon">
               <i class="el-icon-plus"></i>
               <div class="upload-text">点击上传Logo</div>
             </div>
          </el-upload>
          <div class="config-tips">建议尺寸：350x88px，支持PNG、JPG格式</div>
        </div>
      </div>
    </el-card>

    <el-card class="config-card">
      <div slot="header" class="clearfix">
        <span>轮播图配置</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="addSwiper"
          :disabled="swipers.length >= 5"
        >
          添加轮播图
        </el-button>
      </div>
      <div class="swiper-list">
        <div v-for="(swiper, index) in swipers" :key="index" class="swiper-item">
          <div class="swiper-number">{{ index + 1 }}</div>
          <div class="swiper-upload">
            <el-upload
              class="swiper-uploader"
              action=""
              :show-file-list="false"
              :on-success="(response) => handleSwiperSuccess(response, index)"
              :before-upload="beforeSwiperUpload"
              :http-request="(file) => uploadSwiper(file, index)"
            >
                             <img v-if="swiper.url" :src="swiper.url" class="swiper-preview">
               <div v-else-if="swiper.uploading" class="swiper-uploader-icon">
                 <i class="el-icon-loading"></i>
                 <div class="upload-text">上传中...</div>
               </div>
               <div v-else class="swiper-uploader-icon">
                 <i class="el-icon-plus"></i>
                 <div class="upload-text">点击上传</div>
               </div>
            </el-upload>
          </div>
          <div class="swiper-actions">
            <el-button type="text" size="small" @click="moveSwiperUp(index)" :disabled="index === 0">
              <i class="el-icon-arrow-up"></i>
            </el-button>
            <el-button type="text" size="small" @click="moveSwiperDown(index)" :disabled="index === swipers.length - 1">
              <i class="el-icon-arrow-down"></i>
            </el-button>
            <el-button type="text" size="small" @click="removeSwiper(index)" class="delete-btn">
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </div>
        <div v-if="swipers.length === 0" class="empty-state">
          <i class="el-icon-picture-outline"></i>
          <p>暂无轮播图，点击右上角添加</p>
        </div>
      </div>
      <div class="config-tips">建议尺寸：750x520px，支持PNG、JPG格式，最多5张</div>
    </el-card>

    <div class="action-bar">
      <el-button type="primary" @click="saveConfig" :loading="saving">
        保存配置
      </el-button>
      <el-button @click="loadConfig">
        重新加载
      </el-button>
    </div>
  </div>
</template>

<script>
import { getSetting, create } from '@/api/setting'
import { getOssSign, getOssPublicUrl } from '@/api/oss'

export default {
  name: 'MiniappHomeConfig',
  data() {
         return {
       logoUrl: '',
       logoUploading: false,
       swipers: [],
       saving: false,
       loading: false
     }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    async loadConfig() {
      this.loading = true
      try {
        // 加载Logo配置
        const logoResponse = await getSetting('miniapp_home_logo')
        if (logoResponse.data && logoResponse.data.value) {
          this.logoUrl = logoResponse.data.value
        }

        // 加载轮播图配置
        const swiperResponse = await getSetting('miniapp_home_swipers_json')
        if (swiperResponse.data && swiperResponse.data.value) {
          try {
            const swiperUrls = JSON.parse(swiperResponse.data.value)
            this.swipers = swiperUrls.map(url => ({ url }))
          } catch (e) {
            console.error('解析轮播图配置失败:', e)
            this.swipers = []
          }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败')
      } finally {
        this.loading = false
      }
    },

    async saveConfig() {
      this.saving = true
      try {
        // 保存Logo配置
        if (this.logoUrl) {
          await create({
            name: 'miniapp_home_logo',
            value: this.logoUrl
          })
        }

        // 保存轮播图配置
        const swiperUrls = this.swipers.map(swiper => swiper.url).filter(url => url)
        await create({
          name: 'miniapp_home_swipers_json',
          value: JSON.stringify(swiperUrls)
        })

        this.$message.success('配置保存成功')
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      } finally {
        this.saving = false
      }
    },

    // Logo上传相关
    beforeLogoUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('Logo只能是JPG或PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('Logo大小不能超过2MB!')
        return false
      }
      return true
    },

         async uploadLogo(file) {
       try {
         this.logoUploading = true
         const uploadedUrl = await this.uploadToOss(file.file)
         this.logoUrl = uploadedUrl
         this.$message.success('Logo上传成功')
       } catch (error) {
         console.error('上传Logo失败:', error)
         this.$message.error('上传Logo失败')
       } finally {
         this.logoUploading = false
       }
     },

    handleLogoSuccess(response, file) {
      // 上传成功后的处理
    },

    // 轮播图上传相关
    beforeSwiperUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('轮播图只能是JPG或PNG格式!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('轮播图大小不能超过5MB!')
        return false
      }
      return true
    },

    async uploadSwiper(file, index) {
      try {
        // 确保数组中有对应的位置
        if (!this.swipers[index]) {
          this.$set(this.swipers, index, { url: '', uploading: true })
        } else {
          this.$set(this.swipers[index], 'uploading', true)
        }

        const uploadedUrl = await this.uploadToOss(file.file)
        this.$set(this.swipers[index], 'url', uploadedUrl)
        this.$set(this.swipers[index], 'uploading', false)
        this.$message.success('轮播图上传成功')
      } catch (error) {
        console.error('上传轮播图失败:', error)
        this.$message.error('上传轮播图失败')
        this.$set(this.swipers[index], 'uploading', false)
      }
    },

    handleSwiperSuccess(response, index) {
      // 上传成功后的处理
    },

    // 轮播图管理
    addSwiper() {
      if (this.swipers.length < 5) {
        this.swipers.push({ url: '' })
      }
    },

    removeSwiper(index) {
      this.swipers.splice(index, 1)
    },

    moveSwiperUp(index) {
      if (index > 0) {
        const temp = this.swipers[index]
        this.$set(this.swipers, index, this.swipers[index - 1])
        this.$set(this.swipers, index - 1, temp)
      }
    },

         moveSwiperDown(index) {
       if (index < this.swipers.length - 1) {
         const temp = this.swipers[index]
         this.$set(this.swipers, index, this.swipers[index + 1])
         this.$set(this.swipers, index + 1, temp)
       }
     },

     // OSS上传方法
     async uploadToOss(file) {
       try {
         // 获取OSS签名
         const response = await getOssSign()
         const upload = response.data

         // 生成文件名
         const type = file.type.split("/")[1]
         const filename = file.name + file.size
         const key = this.$md5(new Date() + filename) + "." + type

         // 构建表单数据
         const formData = new FormData()
         formData.append('name', file.name)
         formData.append('key', key)
         formData.append('policy', upload.policy)
         formData.append('OSSAccessKeyId', upload.accessid)
         formData.append('success_action_status', 200)
         formData.append('signature', upload.signature)
         formData.append('file', file)

         // 上传到OSS
         const config = {
           headers: {"Content-Type": "multipart/form-data;boundary=" + new Date().getTime()}
         }
         await this.$http.post(upload.host, formData, config)

         // 获取公共访问URL
         const urlResponse = await getOssPublicUrl({key: key})
         return urlResponse.data
       } catch (error) {
         console.error('OSS上传失败:', error)
         throw error
       }
     }
   }
 }
</script>

<style lang="scss" scoped>
.miniapp-home-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    color: #303133;
  }
  p {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }
}

.config-card {
  margin-bottom: 20px;
}

.config-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.config-label {
  width: 120px;
  font-weight: 500;
  color: #606266;
  padding-top: 10px;
}

.config-content {
  flex: 1;
}

.config-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}

.logo-uploader, .swiper-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.2s;

  &:hover {
    border-color: #409EFF;
  }
}

.logo-uploader {
  width: 175px;
  height: 44px;
}

.logo-preview {
  width: 175px;
  height: 44px;
  display: block;
  object-fit: contain;
}

 .logo-uploader-icon {
   font-size: 28px;
   color: #8c939d;
   width: 175px;
   height: 44px;
   text-align: center;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;

   .upload-text {
     font-size: 12px;
     margin-top: 2px;
   }

   .el-icon-loading {
     animation: rotating 2s linear infinite;
   }
 }

.swiper-list {
  min-height: 100px;
}

.swiper-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.swiper-number {
  width: 30px;
  height: 30px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
}

.swiper-upload {
  flex: 1;
  margin-right: 15px;
}

.swiper-uploader {
  width: 150px;
  height: 104px;
}

.swiper-preview {
  width: 150px;
  height: 104px;
  display: block;
  object-fit: cover;
}

 .swiper-uploader-icon {
   font-size: 24px;
   color: #8c939d;
   width: 150px;
   height: 104px;
   text-align: center;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;

   .upload-text {
     font-size: 12px;
     margin-top: 5px;
   }

   .el-icon-loading {
     animation: rotating 2s linear infinite;
   }
 }

.swiper-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.delete-btn {
  color: #f56c6c;
  &:hover {
    color: #f56c6c;
  }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 10px;
  }

  p {
    margin: 0;
  }
}

 .action-bar {
   margin-top: 30px;
   text-align: center;
 }

 @keyframes rotating {
   0% {
     transform: rotate(0deg);
   }
   100% {
     transform: rotate(360deg);
   }
 }
 </style>
