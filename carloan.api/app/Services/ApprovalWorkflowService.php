<?php

namespace App\Services;

use App\Models\BusinessApplication;
use App\Models\ApprovalWorkflow;
use App\Models\ApprovalTask;
use App\Models\ApprovalCcRecord;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ApprovalWorkflowService
{
    /**
     * 启动业务申请的审批流程
     * 
     * @param BusinessApplication $application
     * @param string $businessType
     * @return bool
     */
    public function startWorkflow(BusinessApplication $application, $businessType = ApprovalWorkflow::BUSINESS_TYPE_APPLICATION)
    {
        try {
            DB::beginTransaction();

            // 获取对应的工作流配置
            $workflows = ApprovalWorkflow::getWorkflowsByBusinessType($businessType);
            
            if ($workflows->isEmpty()) {
                \Log::warning("未找到业务类型 {$businessType} 的工作流配置");
                return false;
            }

            // 创建第一个审批步骤的任务
            $firstWorkflow = $workflows->first();
            $this->createApprovalTasks($application->id, $firstWorkflow);
            
            // 创建抄送记录
            $this->createCcRecords($application->id, $firstWorkflow);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('启动审批流程失败: ' . $e->getMessage(), [
                'application_id' => $application->id,
                'business_type' => $businessType,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 创建审批任务
     * 
     * @param int $applicationId
     * @param ApprovalWorkflow $workflow
     * @return void
     */
    private function createApprovalTasks($applicationId, ApprovalWorkflow $workflow)
    {
        $approvers = $workflow->getApprovers();
        
        foreach ($approvers as $approver) {
            ApprovalTask::createTask(
                $applicationId,
                $workflow,
                $approver->id,
                $approver->name
            );
        }
    }

    /**
     * 创建抄送记录
     * 
     * @param int $applicationId
     * @param ApprovalWorkflow $workflow
     * @param int|null $taskId
     * @return void
     */
    private function createCcRecords($applicationId, ApprovalWorkflow $workflow, $taskId = null)
    {
        $ccUsers = $workflow->getCcUsers();
        
        foreach ($ccUsers as $ccUser) {
            ApprovalCcRecord::createCcRecord(
                $applicationId,
                $ccUser->id,
                $ccUser->name,
                ApprovalCcRecord::CC_TYPE_WORKFLOW,
                $workflow->step_name,
                $workflow->step_code,
                "工作流自动抄送：{$workflow->step_name}",
                $taskId
            );
        }
    }

    /**
     * 完成审批任务并推进到下一步
     * 
     * @param ApprovalTask $task
     * @param bool $approveResult
     * @param string|null $approveNotes
     * @return bool
     */
    public function completeTask(ApprovalTask $task, $approveResult, $approveNotes = null)
    {
        try {
            DB::beginTransaction();

            // 完成当前任务
            $task->completeTask($approveResult, $approveNotes);

            if ($approveResult) {
                // 审批通过，检查是否需要推进到下一步
                $this->advanceToNextStep($task);
            } else {
                // 审批拒绝，取消后续所有待审批任务
                $this->cancelPendingTasks($task->application_id, $task->step_order);
                
                // 更新业务申请状态为拒绝
                $task->application->update([
                    'status' => BusinessApplication::STATUS_REJECTED,
                    'status_text' => '审批拒绝',
                    'approval_time' => Carbon::now()
                ]);
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('完成审批任务失败: ' . $e->getMessage(), [
                'task_id' => $task->id,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 推进到下一审批步骤
     * 
     * @param ApprovalTask $completedTask
     * @return void
     */
    private function advanceToNextStep(ApprovalTask $completedTask)
    {
        // 检查当前步骤的所有任务是否都已完成
        $pendingTasksInCurrentStep = ApprovalTask::where('application_id', $completedTask->application_id)
            ->where('step_order', $completedTask->step_order)
            ->where('status', ApprovalTask::STATUS_PENDING)
            ->count();

        if ($pendingTasksInCurrentStep > 0) {
            // 当前步骤还有未完成的任务，不能推进
            return;
        }

        // 检查当前步骤是否有拒绝的任务
        $rejectedTasksInCurrentStep = ApprovalTask::where('application_id', $completedTask->application_id)
            ->where('step_order', $completedTask->step_order)
            ->where('status', ApprovalTask::STATUS_REJECTED)
            ->count();

        if ($rejectedTasksInCurrentStep > 0) {
            // 当前步骤有拒绝的任务，不能推进
            return;
        }

        // 获取下一步的工作流配置
        $nextWorkflow = ApprovalWorkflow::where('business_type', $completedTask->workflow->business_type)
            ->where('step_order', $completedTask->step_order + 1)
            ->where('status', 1)
            ->first();

        if ($nextWorkflow) {
            // 创建下一步的审批任务
            $this->createApprovalTasks($completedTask->application_id, $nextWorkflow);
            
            // 创建下一步的抄送记录
            $this->createCcRecords($completedTask->application_id, $nextWorkflow);

            // 更新业务申请状态
            $this->updateApplicationStatus($completedTask->application, $nextWorkflow->step_code);
        } else {
            // 没有下一步，审批流程完成
            $this->completeWorkflow($completedTask->application);
        }
    }

    /**
     * 取消待审批任务
     * 
     * @param int $applicationId
     * @param int $fromStepOrder
     * @return void
     */
    private function cancelPendingTasks($applicationId, $fromStepOrder)
    {
        ApprovalTask::where('application_id', $applicationId)
            ->where('step_order', '>=', $fromStepOrder)
            ->where('status', ApprovalTask::STATUS_PENDING)
            ->update([
                'status' => ApprovalTask::STATUS_CANCELLED,
                'status_text' => '已取消',
                'completed_time' => Carbon::now(),
                'approve_notes' => '审批被拒绝，自动取消'
            ]);
    }

    /**
     * 更新业务申请状态
     * 
     * @param BusinessApplication $application
     * @param string $stepCode
     * @return void
     */
    private function updateApplicationStatus(BusinessApplication $application, $stepCode)
    {
        $statusMap = [
            'initial_review' => [
                'status' => BusinessApplication::STATUS_INITIAL_REVIEW,
                'status_text' => '初审中'
            ],
            'final_review' => [
                'status' => BusinessApplication::STATUS_FINAL_REVIEW,
                'status_text' => '终审中'
            ],
            'secondary_review' => [
                'status' => BusinessApplication::STATUS_SECONDARY_REVIEW,
                'status_text' => '复审中'
            ],
            'interview_schedule' => [
                'status' => BusinessApplication::STATUS_INTERVIEW_PENDING,
                'status_text' => '待面审'
            ],
            'contract_review' => [
                'status' => BusinessApplication::STATUS_CONTRACT_PENDING,
                'status_text' => '待签约'
            ]
        ];

        if (isset($statusMap[$stepCode])) {
            $application->update($statusMap[$stepCode]);
        }
    }

    /**
     * 完成整个审批流程
     * 
     * @param BusinessApplication $application
     * @return void
     */
    private function completeWorkflow(BusinessApplication $application)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_APPROVED,
            'status_text' => '审批通过',
            'approval_time' => Carbon::now()
        ]);
    }

    /**
     * 手动创建抄送记录
     * 
     * @param int $applicationId
     * @param array $ccUserIds
     * @param string $ccReason
     * @param int|null $taskId
     * @return bool
     */
    public function createManualCcRecords($applicationId, array $ccUserIds, $ccReason, $taskId = null)
    {
        try {
            foreach ($ccUserIds as $ccUserId) {
                $ccUser = User::find($ccUserId);
                if ($ccUser) {
                    ApprovalCcRecord::createCcRecord(
                        $applicationId,
                        $ccUser->id,
                        $ccUser->name,
                        ApprovalCcRecord::CC_TYPE_MANUAL,
                        null,
                        null,
                        $ccReason,
                        $taskId
                    );
                }
            }
            return true;
        } catch (\Exception $e) {
            \Log::error('创建手动抄送失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户的工作统计数据
     * 
     * @param int $userId
     * @return array
     */
    public function getUserWorkStatistics($userId)
    {
        return [
            'pending_approval_count' => ApprovalTask::getPendingTaskCount($userId),
            'cc_count' => ApprovalCcRecord::getUnreadCcCount($userId),
            'my_application_count' => BusinessApplication::where('user_id', $userId)->count()
        ];
    }

    /**
     * 检查超时任务
     * 
     * @return int 超时任务数量
     */
    public function checkTimeoutTasks()
    {
        $timeoutCount = 0;
        
        $pendingTasks = ApprovalTask::where('status', ApprovalTask::STATUS_PENDING)
            ->whereNotNull('timeout_time')
            ->where('timeout_time', '<', Carbon::now())
            ->get();

        foreach ($pendingTasks as $task) {
            if ($task->checkTimeout()) {
                $timeoutCount++;
            }
        }

        return $timeoutCount;
    }
} 