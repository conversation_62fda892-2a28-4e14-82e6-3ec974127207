-- 创建新业务功能相关数据库表
-- 执行时间：2024-01-XX
-- 版本：v1.0
-- 说明：基于现有表结构，只创建新增的表，优化表关联关系

-- 1. 创建客户表（核心表）
CREATE TABLE IF NOT EXISTS `customers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `id_card` varchar(18) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `id_card_front` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证正面照片',
  `id_card_back` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证背面照片',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：1男，2女',
  `nation` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '民族',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '户籍地址',
  `authority` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '签发机关',
  `valid_start` date DEFAULT NULL COMMENT '有效期开始',
  `valid_end` date DEFAULT NULL COMMENT '有效期结束',
  `status` enum('active','inactive','blacklist') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customers_phone_unique` (`phone`),
  UNIQUE KEY `customers_id_card_unique` (`id_card`),
  KEY `customers_created_by_index` (`created_by`),
  KEY `customers_status_index` (`status`),
  KEY `customers_name_phone_index` (`name`, `phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 2. 修改business_applications表，增加customer_id关联
ALTER TABLE `business_applications` 
ADD COLUMN IF NOT EXISTS `customer_id` bigint(20) unsigned DEFAULT NULL COMMENT '客户ID' AFTER `user_id`,
ADD COLUMN IF NOT EXISTS `product_id` bigint(20) unsigned DEFAULT NULL COMMENT '金融产品ID' AFTER `customer_id`,
ADD COLUMN IF NOT EXISTS `contacts` json DEFAULT NULL COMMENT '联系人信息（JSON格式）' AFTER `product_id`,
ADD COLUMN IF NOT EXISTS `assets` json DEFAULT NULL COMMENT '资产信息（JSON格式）' AFTER `contacts`,
ADD COLUMN IF NOT EXISTS `liabilities` json DEFAULT NULL COMMENT '负债信息（JSON格式）' AFTER `assets`,
ADD COLUMN IF NOT EXISTS `vehicle` json DEFAULT NULL COMMENT '车辆信息（JSON格式）' AFTER `liabilities`;

-- 添加外键和索引
ALTER TABLE `business_applications` 
ADD INDEX IF NOT EXISTS `idx_customer_id` (`customer_id`),
ADD INDEX IF NOT EXISTS `idx_product_id` (`product_id`);

-- 添加外键约束
ALTER TABLE `business_applications` 
ADD CONSTRAINT `business_applications_customer_id_foreign` 
FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL;

ALTER TABLE `business_applications` 
ADD CONSTRAINT `business_applications_product_id_foreign` 
FOREIGN KEY (`product_id`) REFERENCES `financial_products` (`id`) ON DELETE SET NULL;

-- 3. 修改customer_risk_queries表，增加customer_id关联
ALTER TABLE `customer_risk_queries` 
ADD COLUMN IF NOT EXISTS `customer_id` bigint(20) unsigned DEFAULT NULL COMMENT '客户ID' AFTER `user_id`;

ALTER TABLE `customer_risk_queries` 
ADD INDEX IF NOT EXISTS `idx_customer_id` (`customer_id`);

-- 添加外键约束
ALTER TABLE `customer_risk_queries` 
ADD CONSTRAINT `customer_risk_queries_customer_id_foreign` 
FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL;

-- 4. 客户联系人表
CREATE TABLE IF NOT EXISTS `customer_contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL COMMENT '客户ID',
  `business_application_id` bigint(20) unsigned DEFAULT NULL COMMENT '业务申请ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人姓名',
  `relationship` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关系',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `id_card` varchar(18) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系地址',
  `work_unit` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作单位',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_contacts_customer_id_index` (`customer_id`),
  KEY `customer_contacts_business_application_id_index` (`business_application_id`),
  KEY `customer_contacts_phone_index` (`phone`),
  CONSTRAINT `customer_contacts_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户联系人表';

-- 5. 客户资产表
CREATE TABLE IF NOT EXISTS `customer_assets` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL COMMENT '客户ID',
  `business_application_id` bigint(20) unsigned DEFAULT NULL COMMENT '业务申请ID',
  `work_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作性质',
  `company_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司名称',
  `monthly_income` decimal(15,2) DEFAULT NULL COMMENT '月收入',
  `annual_income` decimal(15,2) DEFAULT NULL COMMENT '年收入',
  `has_property` tinyint(1) DEFAULT '0' COMMENT '是否有房产',
  `property_count` int(11) DEFAULT '0' COMMENT '房产套数',
  `property_value` decimal(15,2) DEFAULT NULL COMMENT '房产价值',
  `property_loan_balance` decimal(15,2) DEFAULT NULL COMMENT '房产贷款余额',
  `has_vehicle` tinyint(1) DEFAULT '0' COMMENT '是否有车辆',
  `vehicle_count` int(11) DEFAULT '0' COMMENT '车辆数量',
  `vehicle_value` decimal(15,2) DEFAULT NULL COMMENT '车辆价值',
  `vehicle_loan_balance` decimal(15,2) DEFAULT NULL COMMENT '车贷余额',
  `other_assets` decimal(15,2) DEFAULT NULL COMMENT '其他资产',
  `total_assets` decimal(15,2) DEFAULT NULL COMMENT '总资产',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_assets_customer_id_index` (`customer_id`),
  KEY `customer_assets_business_application_id_index` (`business_application_id`),
  CONSTRAINT `customer_assets_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户资产表';

-- 6. 客户负债表
CREATE TABLE IF NOT EXISTS `customer_liabilities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL COMMENT '客户ID',
  `business_application_id` bigint(20) unsigned DEFAULT NULL COMMENT '业务申请ID',
  `has_credit_card` tinyint(1) DEFAULT '0' COMMENT '是否有信用卡',
  `credit_card_limit` decimal(15,2) DEFAULT NULL COMMENT '信用卡额度',
  `credit_card_used` decimal(15,2) DEFAULT NULL COMMENT '信用卡已用额度',
  `has_bank_loan` tinyint(1) DEFAULT '0' COMMENT '是否有银行贷款',
  `bank_loan_balance` decimal(15,2) DEFAULT NULL COMMENT '银行贷款余额',
  `bank_loan_monthly` decimal(15,2) DEFAULT NULL COMMENT '银行贷款月供',
  `has_other_debt` tinyint(1) DEFAULT '0' COMMENT '是否有其他负债',
  `other_debt_balance` decimal(15,2) DEFAULT NULL COMMENT '其他负债余额',
  `other_debt_monthly` decimal(15,2) DEFAULT NULL COMMENT '其他负债月供',
  `total_debt` decimal(15,2) DEFAULT NULL COMMENT '总负债',
  `total_monthly_payment` decimal(15,2) DEFAULT NULL COMMENT '总月供',
  `debt_to_asset_ratio` decimal(8,4) DEFAULT NULL COMMENT '负债率',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_liabilities_customer_id_index` (`customer_id`),
  KEY `customer_liabilities_business_application_id_index` (`business_application_id`),
  CONSTRAINT `customer_liabilities_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户负债表';

-- 7. 车辆信息表
CREATE TABLE IF NOT EXISTS `customer_vehicles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL COMMENT '客户ID',
  `business_application_id` bigint(20) unsigned DEFAULT NULL COMMENT '业务申请ID',
  `vehicle_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车辆类型',
  `brand` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '品牌',
  `model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车型',
  `series` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车系',
  `configuration` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置',
  `year` int(11) DEFAULT NULL COMMENT '年份',
  `fuel_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '燃料类型',
  `transmission` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变速箱',
  `seats` int(11) DEFAULT NULL COMMENT '座位数',
  `guide_price` decimal(15,2) DEFAULT NULL COMMENT '指导价',
  `discount_amount` decimal(15,2) DEFAULT NULL COMMENT '优惠金额',
  `actual_price` decimal(15,2) DEFAULT NULL COMMENT '实际价格',
  `down_payment` decimal(15,2) DEFAULT NULL COMMENT '首付金额',
  `loan_amount` decimal(15,2) DEFAULT NULL COMMENT '贷款金额',
  `dealer_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经销商名称',
  `dealer_contact` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经销商联系人',
  `dealer_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经销商电话',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_vehicles_customer_id_index` (`customer_id`),
  KEY `customer_vehicles_business_application_id_index` (`business_application_id`),
  CONSTRAINT `customer_vehicles_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户车辆信息表';

-- 8. 业务附件表
CREATE TABLE IF NOT EXISTS `business_attachments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `business_application_id` bigint(20) unsigned NOT NULL COMMENT '业务申请ID',
  `customer_id` bigint(20) unsigned DEFAULT NULL COMMENT '客户ID',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件URL',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '附件类别',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必需',
  `upload_time` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `business_attachments_business_application_id_index` (`business_application_id`),
  KEY `business_attachments_customer_id_index` (`customer_id`),
  KEY `business_attachments_category_index` (`category`),
  CONSTRAINT `business_attachments_business_application_id_foreign` FOREIGN KEY (`business_application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务附件表';

-- 9. 创建数据迁移脚本（可选执行）
-- 将现有的business_applications记录关联到customers表
-- 注意：这个操作需要谨慎执行，建议在测试环境先验证

/*
-- 创建迁移过程的临时存储过程
DELIMITER $$
CREATE PROCEDURE MigrateExistingBusinessApplications()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE app_id BIGINT;
    DECLARE customer_name_val VARCHAR(50);
    DECLARE customer_phone_val VARCHAR(20);
    DECLARE customer_id_card_val VARCHAR(18);
    DECLARE customer_id_val BIGINT;
    
    DECLARE cur CURSOR FOR 
        SELECT id, customer_name, customer_phone, customer_id_card 
        FROM business_applications 
        WHERE customer_id IS NULL;
        
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO app_id, customer_name_val, customer_phone_val, customer_id_card_val;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 检查是否已存在该客户
        SELECT id INTO customer_id_val 
        FROM customers 
        WHERE phone = customer_phone_val OR id_card = customer_id_card_val 
        LIMIT 1;
        
        -- 如果不存在则创建客户记录
        IF customer_id_val IS NULL THEN
            INSERT INTO customers (phone, name, id_card, status, created_at, updated_at)
            VALUES (customer_phone_val, customer_name_val, customer_id_card_val, 'active', NOW(), NOW());
            SET customer_id_val = LAST_INSERT_ID();
        END IF;
        
        -- 更新业务申请记录
        UPDATE business_applications 
        SET customer_id = customer_id_val 
        WHERE id = app_id;
        
        SET customer_id_val = NULL;
    END LOOP;
    
    CLOSE cur;
END$$
DELIMITER ;

-- 执行迁移（取消注释下面一行来执行）
-- CALL MigrateExistingBusinessApplications();

-- 删除临时存储过程
-- DROP PROCEDURE IF EXISTS MigrateExistingBusinessApplications;
*/ 