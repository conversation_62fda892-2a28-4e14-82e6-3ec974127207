-- 客户风险查询表DDL建表语句
-- 创建时间: 2024-03-17
-- 说明: 客户大数据风险评估查询记录表

CREATE TABLE `customer_risk_queries` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
  `customer_id_card` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户身份证号',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'querying' COMMENT '查询状态:querying查询中,completed已完成,failed查询失败',
  `risk_score` int(11) DEFAULT NULL COMMENT '风险评分(0-100)',
  `risk_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '风险等级:low低风险,medium中风险,high高风险',
  `query_time` timestamp NULL DEFAULT NULL COMMENT '查询时间',
  `query_results` json DEFAULT NULL COMMENT '查询结果详情',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_risk_queries_user_id_index` (`user_id`),
  KEY `customer_risk_queries_customer_phone_index` (`customer_phone`),
  KEY `customer_risk_queries_customer_id_card_index` (`customer_id_card`),
  KEY `customer_risk_queries_status_index` (`status`),
  KEY `customer_risk_queries_user_id_status_index` (`user_id`,`status`),
  KEY `customer_risk_queries_customer_phone_customer_id_card_index` (`customer_phone`,`customer_id_card`),
  KEY `customer_risk_queries_query_time_index` (`query_time`),
  KEY `customer_risk_queries_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户风险查询表'; 