<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ApprovalTask extends BaseModel
{
    use SoftDeletes;

    protected $table = 'approval_tasks';

    protected $fillable = [
        'application_id',
        'workflow_id',
        'task_no',
        'step_name',
        'step_code',
        'step_order',
        'approver_id',
        'approver_name',
        'status',
        'status_text',
        'assigned_time',
        'completed_time',
        'approve_result',
        'approve_notes',
        'timeout_time',
        'is_timeout'
    ];

    protected $casts = [
        'application_id' => 'integer',
        'workflow_id' => 'integer',
        'approver_id' => 'integer',
        'step_order' => 'integer',
        'approve_result' => 'boolean',
        'is_timeout' => 'boolean',
        'assigned_time' => 'datetime',
        'completed_time' => 'datetime',
        'timeout_time' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // 任务状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_TIMEOUT = 'timeout';

    /**
     * 关联业务申请
     */
    public function application()
    {
        return $this->belongsTo(BusinessApplication::class, 'application_id');
    }

    /**
     * 关联工作流配置
     */
    public function workflow()
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'workflow_id');
    }

    /**
     * 关联审批人
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    /**
     * 生成任务编号
     */
    public static function generateTaskNo()
    {
        return 'TASK' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 创建审批任务
     */
    public static function createTask($applicationId, $workflow, $approverId, $approverName)
    {
        $timeoutTime = null;
        if ($workflow->timeout_hours) {
            $timeoutTime = Carbon::now()->addHours($workflow->timeout_hours);
        }

        return static::create([
            'application_id' => $applicationId,
            'workflow_id' => $workflow->id,
            'task_no' => static::generateTaskNo(),
            'step_name' => $workflow->step_name,
            'step_code' => $workflow->step_code,
            'step_order' => $workflow->step_order,
            'approver_id' => $approverId,
            'approver_name' => $approverName,
            'status' => static::STATUS_PENDING,
            'status_text' => '待审批',
            'assigned_time' => Carbon::now(),
            'timeout_time' => $timeoutTime
        ]);
    }

    /**
     * 完成审批任务
     */
    public function completeTask($approveResult, $approveNotes = null)
    {
        $this->update([
            'status' => $approveResult ? static::STATUS_APPROVED : static::STATUS_REJECTED,
            'status_text' => $approveResult ? '已通过' : '已拒绝',
            'completed_time' => Carbon::now(),
            'approve_result' => $approveResult,
            'approve_notes' => $approveNotes
        ]);

        return $this;
    }

    /**
     * 取消审批任务
     */
    public function cancelTask($reason = null)
    {
        $this->update([
            'status' => static::STATUS_CANCELLED,
            'status_text' => '已取消',
            'completed_time' => Carbon::now(),
            'approve_notes' => $reason
        ]);

        return $this;
    }

    /**
     * 标记为超时
     */
    public function markAsTimeout()
    {
        $this->update([
            'status' => static::STATUS_TIMEOUT,
            'status_text' => '已超时',
            'is_timeout' => true,
            'completed_time' => Carbon::now()
        ]);

        return $this;
    }

    /**
     * 检查是否超时
     */
    public function checkTimeout()
    {
        if ($this->timeout_time && Carbon::now()->gt($this->timeout_time) && $this->status === static::STATUS_PENDING) {
            $this->markAsTimeout();
            return true;
        }

        return false;
    }

    /**
     * 获取待审批任务
     */
    public static function getPendingTasks($approverId, $limit = 10)
    {
        return static::with(['application', 'workflow'])
            ->where('approver_id', $approverId)
            ->where('status', static::STATUS_PENDING)
            ->orderBy('assigned_time', 'asc')
            ->limit($limit)
            ->get();
    }

    /**
     * 获取用户的待审批任务数量
     */
    public static function getPendingTaskCount($approverId)
    {
        return static::where('approver_id', $approverId)
            ->where('status', static::STATUS_PENDING)
            ->count();
    }

    /**
     * 作用域：按审批人筛选
     */
    public function scopeForApprover($query, $approverId)
    {
        return $query->where('approver_id', $approverId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待审批
     */
    public function scopePending($query)
    {
        return $query->where('status', static::STATUS_PENDING);
    }
} 