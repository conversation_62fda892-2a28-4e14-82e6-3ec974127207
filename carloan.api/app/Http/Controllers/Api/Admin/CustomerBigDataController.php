<?php

namespace App\Http\Controllers\Api\Admin;

use App\Models\CustomerRiskQuery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class CustomerBigDataController extends BaseController
{
    /**
     * 获取客户大数据查询历史列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);
            $keyword = $request->get('keyword', ''); // 搜索关键词
            $status = $request->get('status', ''); // 查询状态
            $riskLevel = $request->get('risk_level', ''); // 风险等级
            $timeRange = $request->get('time_range', ''); // 时间范围筛选

            // 构建查询
            $query = CustomerRiskQuery::orderBy('created_at', 'desc');

            // 关键词搜索（客户姓名、身份证号、手机号）
            if ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('customer_name', 'like', "%{$keyword}%")
                      ->orWhere('customer_phone', 'like', "%{$keyword}%")
                      ->orWhere('customer_id_card', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($status) {
                $query->where('status', $status);
            }

            // 风险等级筛选
            if ($riskLevel) {
                $query->where('risk_level', $riskLevel);
            }

            // 时间范围筛选
            if ($timeRange) {
                switch ($timeRange) {
                    case '7days':
                        $query->where('created_at', '>=', Carbon::now()->subDays(7));
                        break;
                    case '7to30days':
                        $query->where('created_at', '>=', Carbon::now()->subDays(30))
                              ->where('created_at', '<', Carbon::now()->subDays(7));
                        break;
                    case '30plus':
                        $query->where('created_at', '<', Carbon::now()->subDays(30));
                        break;
                }
            }

            // 分页查询
            $queries = $query->paginate($perPage, ['*'], 'page', $page);

            // 格式化数据
            $list = $queries->items();
            $formattedList = array_map(function ($item) {
                return $this->formatQueryData($item);
            }, $list);

            // 获取统计数据
            $statistics = $this->getStatistics();

            return $this->response->array([
                'data' => [
                    'list' => $formattedList,
                    'pagination' => [
                        'current_page' => $queries->currentPage(),
                        'per_page' => $queries->perPage(),
                        'total' => $queries->total(),
                        'last_page' => $queries->lastPage()
                    ],
                    'statistics' => $statistics
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->errorBadRequest('获取客户大数据列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取客户大数据查询详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $query = CustomerRiskQuery::find($id);

            if (!$query) {
                return $this->errorHandler('查询记录不存在');
            }

            return $this->successHandler('获取详情成功', 200, $this->formatQueryData($query, true));

        } catch (\Exception $e) {
            return $this->errorHandler('获取查询详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 重新执行客户风险查询
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function requery(Request $request, $id)
    {
        try {
            $query = CustomerRiskQuery::find($id);

            if (!$query) {
                return $this->errorHandler('查询记录不存在');
            }

            // 更新查询状态为查询中
            $query->update([
                'status' => 'querying',
                'query_time' => Carbon::now(),
                'risk_score' => null,
                'risk_level' => null,
                'query_results' => null
            ]);

            // 这里应该调用实际的风险查询API
            // 由于是模拟数据，我们直接生成结果
            $this->simulateRiskQuery($query);

            return $this->successHandler('重新查询已启动', 200, $this->formatQueryData($query->fresh(), true));

        } catch (\Exception $e) {
            return $this->errorHandler('重新查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取统计数据
     * 
     * @return array
     */
    private function getStatistics()
    {
        $now = Carbon::now();

        return [
            '7days' => CustomerRiskQuery::where('created_at', '>=', $now->copy()->subDays(7))->count(),
            '7to30days' => CustomerRiskQuery::where('created_at', '>=', $now->copy()->subDays(30))
                ->where('created_at', '<', $now->copy()->subDays(7))->count(),
            '30plus' => CustomerRiskQuery::where('created_at', '<', $now->copy()->subDays(30))->count(),
        ];
    }

    /**
     * 格式化查询数据
     * 
     * @param CustomerRiskQuery $query
     * @param bool $detail
     * @return array
     */
    private function formatQueryData(CustomerRiskQuery $query, $detail = false)
    {
        $data = [
            'id' => $query->id,
            'customer_name' => $query->customer_name,
            'customer_phone' => $query->customer_phone,
            'customer_id_card' => $query->customer_id_card,
            'status' => $query->status,
            'status_text' => $this->getStatusText($query->status),
            'risk_score' => $query->risk_score,
            'risk_level' => $query->risk_level,
            'query_time' => $query->query_time ? $query->query_time->format('Y-m-d H:i:s') : null,
            'created_time' => $query->created_at->format('Y-m-d H:i:s'),
            'can_requery' => in_array($query->status, ['completed', 'failed']),
            'can_create_business' => $query->status === 'completed' && $query->risk_score >= 60
        ];

        if ($detail && $query->query_results) {
            $data['query_results'] = $query->query_results;
        }

        return $data;
    }

    /**
     * 获取状态文本
     * 
     * @param string $status
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'querying' => '查询中',
            'completed' => '已完成',
            'failed' => '查询失败'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 模拟风险查询过程（实际应该调用真实API）
     * 
     * @param CustomerRiskQuery $query
     */
    private function simulateRiskQuery(CustomerRiskQuery $query)
    {
        // 模拟查询延迟
        sleep(2);

        // 生成模拟数据
        $riskScore = rand(45, 95);
        $riskLevel = $riskScore >= 80 ? 'low' : ($riskScore >= 60 ? 'medium' : 'high');
        
        $queryResults = [
            'pre_loan_risk' => [
                'score' => $riskScore,
                'decision' => $riskScore >= 80 ? 'Accept' : ($riskScore >= 60 ? 'Review' : 'Reject'),
                'pdf_url' => 'https://example.com/risk-report-' . $query->id . '.pdf'
            ],
            'credit_risk' => [
                'access_score' => rand(600, 800),
                'confidence' => rand(70, 95),
                'hit_count' => rand(0, 10)
            ],
            'id_verify' => [
                'result' => $riskScore >= 70 ? 'verified' : 'failed',
                'match_rate' => rand(85, 100)
            ],
            'lawsuit_info' => [
                'total_cases' => rand(0, 5),
                'active_cases' => rand(0, 2)
            ],
            'dishonest_info' => [
                'record_count' => $riskScore < 60 ? rand(1, 3) : 0
            ],
            'high_consumption' => [
                'record_count' => $riskScore < 70 ? rand(0, 2) : 0
            ]
        ];

        $query->update([
            'status' => 'completed',
            'risk_score' => $riskScore,
            'risk_level' => $riskLevel,
            'query_results' => $queryResults
        ]);
    }
} 