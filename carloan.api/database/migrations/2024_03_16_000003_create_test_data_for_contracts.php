<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CreateTestDataForContracts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 为签约功能创建测试数据
        $testData = [
            [
                'application_no' => 'APP20240316100001',
                'user_id' => 1,
                'channel_code' => 'DIRECT',
                'customer_name' => '张三',
                'customer_phone' => '13800138001',
                'customer_id_card' => '110101199001011001',
                'product_name' => '24年以租代售计划',
                'loan_amount' => 150000.00,
                'loan_period' => 24,
                'interest_rate' => 0.0850,
                'vehicle_brand' => '大众',
                'vehicle_model' => '帕萨特',
                'vehicle_vin' => 'WVWZZZ3CZKE123456',
                'vehicle_year' => 2023,
                'vehicle_price' => 180000.00,
                'status' => 'contract_pending',
                'status_text' => '待签约',
                'approval_notes' => '终审通过，可以进行签约',
                'approval_history' => json_encode([
                    [
                        'stage' => 'submitted',
                        'status' => 'submitted',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '客户提交申请',
                        'created_at' => '2024-03-15 09:00:00'
                    ],
                    [
                        'stage' => 'initial_review',
                        'status' => 'approved',
                        'operator_id' => 2,
                        'operator_name' => '系统',
                        'notes' => '初审通过',
                        'created_at' => '2024-03-15 09:30:00'
                    ],
                    [
                        'stage' => 'pre_approval',
                        'status' => 'approved',
                        'operator_id' => 3,
                        'operator_name' => '预审员',
                        'notes' => '风险评估通过',
                        'created_at' => '2024-03-15 10:00:00'
                    ],
                    [
                        'stage' => 'interview_completed',
                        'status' => 'completed',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '面审完成，客户资料齐全',
                        'created_at' => '2024-03-15 14:00:00'
                    ],
                    [
                        'stage' => 'final_review',
                        'status' => 'approved',
                        'operator_id' => 4,
                        'operator_name' => '终审员',
                        'notes' => '终审通过',
                        'created_at' => '2024-03-15 16:00:00'
                    ],
                    [
                        'stage' => 'secondary_review',
                        'status' => 'approved',
                        'operator_id' => 5,
                        'operator_name' => '复审员',
                        'notes' => '复审通过，可以签约',
                        'created_at' => '2024-03-15 17:00:00'
                    ]
                ]),
                'submit_time' => '2024-03-15 09:00:00',
                'approval_time' => '2024-03-15 17:00:00',
                'need_supplement' => 0,
                'customer_data' => json_encode([
                    'income' => 8000,
                    'work_years' => 5,
                    'education' => '本科'
                ]),
                'risk_assessment' => json_encode([
                    'score' => 85,
                    'level' => 'low'
                ]),
                'created_at' => Carbon::parse('2024-03-15 09:00:00'),
                'updated_at' => Carbon::parse('2024-03-15 17:00:00')
            ],
            [
                'application_no' => 'APP20240316100002',
                'user_id' => 1,
                'channel_code' => 'DIRECT',
                'customer_name' => '李四',
                'customer_phone' => '13800138002',
                'customer_id_card' => '110101199002022002',
                'product_name' => '36年融资租赁计划',
                'loan_amount' => 200000.00,
                'loan_period' => 36,
                'interest_rate' => 0.0900,
                'vehicle_brand' => '奔驰',
                'vehicle_model' => 'C级',
                'vehicle_vin' => 'WDD2050291F123456',
                'vehicle_year' => 2024,
                'vehicle_price' => 250000.00,
                'status' => 'contract_processing',
                'status_text' => '签约中',
                'approval_notes' => '已发起签约，等待客户确认',
                'approval_history' => json_encode([
                    [
                        'stage' => 'submitted',
                        'status' => 'submitted',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '客户提交申请',
                        'created_at' => '2024-03-14 10:00:00'
                    ],
                    [
                        'stage' => 'initial_review',
                        'status' => 'approved',
                        'operator_id' => 2,
                        'operator_name' => '系统',
                        'notes' => '初审通过',
                        'created_at' => '2024-03-14 10:30:00'
                    ],
                    [
                        'stage' => 'pre_approval',
                        'status' => 'approved',
                        'operator_id' => 3,
                        'operator_name' => '预审员',
                        'notes' => '风险评估通过',
                        'created_at' => '2024-03-14 11:00:00'
                    ],
                    [
                        'stage' => 'interview_completed',
                        'status' => 'completed',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '面审完成',
                        'created_at' => '2024-03-14 15:00:00'
                    ],
                    [
                        'stage' => 'final_review',
                        'status' => 'approved',
                        'operator_id' => 4,
                        'operator_name' => '终审员',
                        'notes' => '终审通过',
                        'created_at' => '2024-03-14 16:30:00'
                    ],
                    [
                        'stage' => 'secondary_review',
                        'status' => 'approved',
                        'operator_id' => 5,
                        'operator_name' => '复审员',
                        'notes' => '复审通过',
                        'created_at' => '2024-03-14 17:30:00'
                    ],
                    [
                        'stage' => 'contract_initiated',
                        'status' => 'processing',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '发起电子签约',
                        'created_at' => '2024-03-15 09:00:00'
                    ]
                ]),
                'submit_time' => '2024-03-14 10:00:00',
                'approval_time' => '2024-03-14 17:30:00',
                'need_supplement' => 0,
                'customer_data' => json_encode([
                    'income' => 12000,
                    'work_years' => 8,
                    'education' => '硕士'
                ]),
                'risk_assessment' => json_encode([
                    'score' => 92,
                    'level' => 'low'
                ]),
                'created_at' => Carbon::parse('2024-03-14 10:00:00'),
                'updated_at' => Carbon::parse('2024-03-15 09:00:00')
            ],
            [
                'application_no' => 'APP20240316100003',
                'user_id' => 1,
                'channel_code' => 'DIRECT',
                'customer_name' => '王五',
                'customer_phone' => '13800138003',
                'customer_id_card' => '110101199003033003',
                'product_name' => '24年以租代售计划',
                'loan_amount' => 120000.00,
                'loan_period' => 24,
                'interest_rate' => 0.0850,
                'vehicle_brand' => '丰田',
                'vehicle_model' => '凯美瑞',
                'vehicle_vin' => '4T1BE46K37U123456',
                'vehicle_year' => 2023,
                'vehicle_price' => 150000.00,
                'status' => 'contract_completed',
                'status_text' => '签约完成',
                'approval_notes' => '签约完成，业务审批通过',
                'approval_history' => json_encode([
                    [
                        'stage' => 'submitted',
                        'status' => 'submitted',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '客户提交申请',
                        'created_at' => '2024-03-13 11:00:00'
                    ],
                    [
                        'stage' => 'initial_review',
                        'status' => 'approved',
                        'operator_id' => 2,
                        'operator_name' => '系统',
                        'notes' => '初审通过',
                        'created_at' => '2024-03-13 11:30:00'
                    ],
                    [
                        'stage' => 'pre_approval',
                        'status' => 'approved',
                        'operator_id' => 3,
                        'operator_name' => '预审员',
                        'notes' => '风险评估通过',
                        'created_at' => '2024-03-13 12:00:00'
                    ],
                    [
                        'stage' => 'interview_completed',
                        'status' => 'completed',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '面审完成',
                        'created_at' => '2024-03-13 16:00:00'
                    ],
                    [
                        'stage' => 'final_review',
                        'status' => 'approved',
                        'operator_id' => 4,
                        'operator_name' => '终审员',
                        'notes' => '终审通过',
                        'created_at' => '2024-03-13 17:00:00'
                    ],
                    [
                        'stage' => 'secondary_review',
                        'status' => 'approved',
                        'operator_id' => 5,
                        'operator_name' => '复审员',
                        'notes' => '复审通过',
                        'created_at' => '2024-03-14 09:00:00'
                    ],
                    [
                        'stage' => 'contract_initiated',
                        'status' => 'processing',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '发起电子签约',
                        'created_at' => '2024-03-14 10:00:00'
                    ],
                    [
                        'stage' => 'contract_completed',
                        'status' => 'approved',
                        'operator_id' => 1,
                        'operator_name' => '张三业务员',
                        'notes' => '客户已完成签约',
                        'created_at' => '2024-03-14 16:00:00'
                    ]
                ]),
                'submit_time' => '2024-03-13 11:00:00',
                'approval_time' => '2024-03-14 16:00:00',
                'need_supplement' => 0,
                'customer_data' => json_encode([
                    'income' => 6500,
                    'work_years' => 3,
                    'education' => '大专'
                ]),
                'risk_assessment' => json_encode([
                    'score' => 78,
                    'level' => 'medium'
                ]),
                'created_at' => Carbon::parse('2024-03-13 11:00:00'),
                'updated_at' => Carbon::parse('2024-03-14 16:00:00')
            ]
        ];

        foreach ($testData as $data) {
            // 检查是否已存在相同application_no的记录
            $exists = DB::table('business_applications')
                ->where('application_no', $data['application_no'])
                ->exists();
            
            if (!$exists) {
                DB::table('business_applications')->insert($data);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除测试数据
        $testApplicationNos = [
            'APP20240316100001',
            'APP20240316100002',
            'APP20240316100003'
        ];

        DB::table('business_applications')
            ->whereIn('application_no', $testApplicationNos)
            ->delete();
    }
} 