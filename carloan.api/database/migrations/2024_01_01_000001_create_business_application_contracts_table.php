<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBusinessApplicationContractsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('business_application_contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('application_id')->comment('业务申请ID');
            
            // e签宝相关字段
            $table->string('esign_flow_id')->nullable()->comment('e签宝签署流程ID');
            $table->string('esign_account_id')->nullable()->comment('e签宝客户账户ID');
            $table->string('esign_file_id')->nullable()->comment('e签宝合同文件ID');
            $table->text('sign_url')->nullable()->comment('签署链接');
            
            // 合同信息
            $table->string('contract_type')->default('standard')->comment('合同类型: standard(标准合同), table(表格合同)');
            $table->string('contract_title')->nullable()->comment('合同标题');
            $table->string('contract_status')->default('draft')->comment('合同状态: draft(草稿), generated(已生成), signing(签署中), signed(已签署), failed(失败), revoked(已撤销)');
            
            // 时间字段
            $table->timestamp('generated_at')->nullable()->comment('合同生成时间');
            $table->timestamp('sign_started_at')->nullable()->comment('签署开始时间');
            $table->timestamp('signed_at')->nullable()->comment('签署完成时间');
            $table->timestamp('expired_at')->nullable()->comment('签署链接过期时间');
            
            // 回调和扩展数据
            $table->json('esign_callback_data')->nullable()->comment('e签宝回调数据');
            $table->json('contract_data')->nullable()->comment('合同生成数据快照');
            $table->text('remarks')->nullable()->comment('备注');
            
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index('application_id', 'idx_application_id');
            $table->index('esign_flow_id', 'idx_esign_flow_id');
            $table->index('esign_account_id', 'idx_esign_account_id');
            $table->index('contract_type', 'idx_contract_type');
            $table->index('contract_status', 'idx_contract_status');
            $table->index('generated_at', 'idx_generated_at');
            
            // 外键约束
            $table->foreign('application_id')->references('id')->on('business_applications')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('business_application_contracts');
    }
} 