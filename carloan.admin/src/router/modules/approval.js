import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const approvalRouter = {
  path: '/approval',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Approval',
  meta: {
    title: '审批管理',
    icon: 'el-icon-s-check'
  },
  children: [
    {
      path: 'initial',
      component: PlaceholderPage,
      name: 'InitialApproval',
      meta: { title: '初审管理' }
    },
    {
      path: 'interview',
      component: PlaceholderPage,
      name: 'InterviewApproval',
      meta: { title: '面审管理' }
    },
    {
      path: 'final',
      component: PlaceholderPage,
      name: 'FinalApproval',
      meta: { title: '终审管理' }
    },
    {
      path: 'secondary',
      component: PlaceholderPage,
      name: 'SecondaryApproval',
      meta: { title: '复审管理' }
    },
    {
      path: 'detail/:id',
      component: PlaceholderPage,
      name: 'ApprovalDetail',
      meta: { title: '审批详情', hidden: true }
    },
    {
      path: 'process/:id',
      component: PlaceholderPage,
      name: 'ApprovalProcess',
      meta: { title: '审批处理', hidden: true }
    }
  ]
}

export default approvalRouter
