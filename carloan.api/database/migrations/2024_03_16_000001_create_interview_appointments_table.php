<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateInterviewAppointmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('interview_appointments', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // 关联信息
            $table->unsignedBigInteger('application_id')->index()->comment('业务申请ID');
            $table->unsignedBigInteger('user_id')->index()->comment('业务员ID');
            
            // 预约基本信息
            $table->string('appointment_no', 32)->unique()->comment('预约单号');
            $table->string('status', 20)->default('pending')->index()->comment('状态:pending待预约,scheduled已预约,completed已完成,cancelled已取消');
            $table->string('status_text', 50)->default('待预约')->comment('状态文本');
            
            // 预约时间信息
            $table->timestamp('appointment_time')->nullable()->comment('预约面审时间');
            $table->string('appointment_location', 200)->nullable()->comment('面审地点');
            $table->text('appointment_notes')->nullable()->comment('预约备注');
            
            // 面审结果
            $table->tinyInteger('interview_result')->nullable()->comment('面审结果:1通过,0不通过');
            $table->text('interview_notes')->nullable()->comment('面审备注');
            $table->json('interview_materials')->nullable()->comment('面审材料');
            
            // 时间记录
            $table->timestamp('created_time')->nullable()->comment('创建时间');
            $table->timestamp('scheduled_time')->nullable()->comment('预约时间');
            $table->timestamp('completed_time')->nullable()->comment('完成时间');
            $table->timestamp('cancelled_time')->nullable()->comment('取消时间');
            
            // 操作人员
            $table->unsignedBigInteger('interviewer_id')->nullable()->comment('面审官ID');
            $table->string('cancel_reason', 200)->nullable()->comment('取消原因');
            
            // 操作记录
            $table->json('operation_log')->nullable()->comment('操作日志');
            
            $table->timestamps();
            $table->softDeletes();
            
            // 外键约束
            $table->foreign('application_id')
                ->references('id')
                ->on('business_applications')
                ->onDelete('cascade');
                
            // 索引
            $table->index(['status', 'user_id']);
            $table->index('appointment_time');
            $table->index('scheduled_time');
        });

        DB::statement("ALTER TABLE `interview_appointments` comment '面审预约表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('interview_appointments');
    }
} 