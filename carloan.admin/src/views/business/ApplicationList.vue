<template>
  <div class="business-application-list">
    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="客户姓名/手机号/申请单号"
        clearable
      />
      <el-select
        v-model="listQuery.status"
        class="filter-item"
        style="width: 150px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="状态筛选"
        clearable
      >
        <el-option label="初审中" value="initial_review" />
        <el-option label="面审中" value="interview" />
        <el-option label="终审中" value="final_review" />
        <el-option label="复审中" value="secondary_review" />
        <el-option label="待签约" value="contract" />
        <el-option label="已完成" value="completed" />
        <el-option label="已驳回" value="rejected" />
        <el-option label="需补件" value="supplement" />
      </el-select>
      <el-select
        v-model="listQuery.user_id"
        class="filter-item"
        style="width: 150px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="业务员"
        clearable
      >
        <el-option
          v-for="user in userList"
          :key="user.id"
          :label="user.name"
          :value="user.id"
        />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="margin-right: 10px; margin-bottom: 10px;"
        @change="handleDateChange"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-bottom: 10px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="default"
        icon="el-icon-refresh"
        style="margin-bottom: 10px;"
        @click="resetFilter"
      >
        重置
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-container" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.total || 0 }}</div>
            <div class="stat-label">全部</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.initial_review || 0 }}</div>
            <div class="stat-label">初审中</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.interview || 0 }}</div>
            <div class="stat-label">面审中</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.final_review || 0 }}</div>
            <div class="stat-label">终审中</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.contract || 0 }}</div>
            <div class="stat-label">待签约</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.completed || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :span="3">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.rejected || 0 }}</div>
            <div class="stat-label">已驳回</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="申请单号" prop="application_no" align="center" min-width="160">
        <template slot-scope="scope">
          <el-button type="text" @click="showDetail(scope.row)">
            {{ scope.row.application_no }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="客户信息" min-width="140">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.customer_name }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.customer_phone }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="产品信息" min-width="120">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.product_name }}</div>
            <div style="color: #999; font-size: 12px;">
              ¥{{ formatAmount(scope.row.loan_amount) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="车辆信息" min-width="140">
        <template slot-scope="scope">
          <div>
            <div>{{ scope.row.vehicle_brand }} {{ scope.row.vehicle_model }}</div>
            <div style="color: #999; font-size: 12px;">
              ¥{{ formatAmount(scope.row.vehicle_price) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="业务员" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.user.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="渠道" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.channel.name || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            size="small"
          >
            {{ scope.row.status_text }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="补件" align="center" width="80">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.need_supplement"
            type="warning"
            size="mini"
          >
            需补件
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="提交时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.submit_time || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="审批时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.approval_time || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="100"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            size="mini"
            @click="showDetail(row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.per_page"
      :page-count.sync="pageCount"
      :page-sizes="[20, 50, 100, 200]"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      :title="`业务申请详情 - ${currentDetail.application_no}`"
      :visible.sync="detailVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail.id">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>基本信息</h4>
            <div class="detail-item">
              <span class="label">申请单号：</span>
              <span>{{ currentDetail.application_no }}</span>
            </div>
            <div class="detail-item">
              <span class="label">客户姓名：</span>
              <span>{{ currentDetail.customer_name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">联系电话：</span>
              <span>{{ currentDetail.customer_phone }}</span>
            </div>
            <div class="detail-item">
              <span class="label">身份证号：</span>
              <span>{{ currentDetail.customer_id_card }}</span>
            </div>
            <div class="detail-item">
              <span class="label">产品名称：</span>
              <span>{{ currentDetail.product_name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">贷款金额：</span>
              <span>¥{{ formatAmount(currentDetail.loan_amount) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">贷款期限：</span>
              <span>{{ currentDetail.loan_period }}个月</span>
            </div>
            <div class="detail-item">
              <span class="label">利率：</span>
              <span>{{ currentDetail.interest_rate }}%</span>
            </div>
          </el-col>
          <el-col :span="12">
            <h4>车辆信息</h4>
            <div class="detail-item">
              <span class="label">车辆品牌：</span>
              <span>{{ currentDetail.vehicle_brand }}</span>
            </div>
            <div class="detail-item">
              <span class="label">车辆型号：</span>
              <span>{{ currentDetail.vehicle_model }}</span>
            </div>
            <div class="detail-item">
              <span class="label">车辆价格：</span>
              <span>¥{{ formatAmount(currentDetail.vehicle_price) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">业务员：</span>
              <span>{{ currentDetail.user.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">渠道：</span>
              <span>{{ currentDetail.channel.name || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <el-tag :type="getStatusType(currentDetail.status)">
                {{ currentDetail.status_text }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">提交时间：</span>
              <span>{{ currentDetail.submit_time || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">审批时间：</span>
              <span>{{ currentDetail.approval_time || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <div v-if="currentDetail.need_supplement" style="margin-top: 20px;">
          <h4>补件信息</h4>
          <div class="detail-item">
            <span class="label">补件原因：</span>
            <span>{{ currentDetail.supplement_reason }}</span>
          </div>
          <div class="detail-item">
            <span class="label">补件截止时间：</span>
            <span>{{ currentDetail.supplement_deadline || '-' }}</span>
          </div>
        </div>

        <div v-if="currentDetail.approval_notes" style="margin-top: 20px;">
          <h4>审批备注</h4>
          <p>{{ currentDetail.approval_notes }}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBusinessApplicationList, getBusinessApplicationDetail } from '@/api/businessApplication'
import { getUserList } from '@/api/user'
import Pagination from '@/components/Pagination'

export default {
  name: 'BusinessApplicationList',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      pageCount: 0,
      loading: false,
      userList: [],
      statistics: {},
      dateRange: null,
      detailVisible: false,
      currentDetail: {},
      listQuery: {
        page: 1,
        per_page: 20,
        search: '',
        status: '',
        user_id: '',
        start_date: '',
        end_date: ''
      }
    }
  },
  created() {
    this.getList()
    this.getUserList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getBusinessApplicationList(this.listQuery)
        this.list = response.data
        this.total = response.meta.pagination.total
        this.pageCount = response.meta.pagination.total_pages
        this.statistics = response.meta.statistics || {}
      } catch (error) {
        console.error('获取业务申请列表失败:', error)
        this.$message.error('获取业务申请列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取用户列表
    async getUserList() {
      try {
        const response = await getUserList({ page: 1, per_page: 1000 })
        this.userList = response.data || []
      } catch (error) {
        console.error('获取用户列表失败:', error)
      }
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 重置筛选
    resetFilter() {
      this.listQuery = {
        page: 1,
        per_page: 20,
        search: '',
        status: '',
        user_id: '',
        start_date: '',
        end_date: ''
      }
      this.dateRange = null
      this.getList()
    },

    // 处理日期范围变化
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.listQuery.start_date = this.formatDate(value[0])
        this.listQuery.end_date = this.formatDate(value[1])
      } else {
        this.listQuery.start_date = ''
        this.listQuery.end_date = ''
      }
    },

    // 显示详情
    async showDetail(row) {
      try {
        this.loading = true
        const response = await getBusinessApplicationDetail(row.id)
        this.currentDetail = response.data
        this.detailVisible = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败')
      } finally {
        this.loading = false
      }
    },

    // 获取状态样式
    getStatusType(status) {
      const statusMap = {
        'submitted': '',
        'initial_review': 'warning',
        'interview_pending': 'warning',
        'interview_scheduled': 'info',
        'interview_completed': 'info',
        'final_review': 'warning',
        'secondary_review': 'warning',
        'contract_pending': 'success',
        'contract_processing': 'info',
        'contract_completed': 'success',
        'approved': 'success',
        'rejected': 'danger',
        'supplement_required': 'warning'
      }
      return statusMap[status] || ''
    },

    // 格式化金额
    formatAmount(amount) {
      if (!amount) return '0'
      return Number(amount).toLocaleString()
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style lang="scss" scoped>
.business-application-list {
  padding: 20px;

  .filter-container {
    margin-bottom: 20px;
  }

  .statistics-container {
    .stat-card {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .detail-item {
    margin-bottom: 10px;
    line-height: 1.5;

    .label {
      font-weight: bold;
      color: #666;
      margin-right: 10px;
    }
  }
}
</style>
