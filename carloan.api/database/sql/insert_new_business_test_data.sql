-- 新业务功能测试数据
-- 执行时间：2024-01-XX
-- 版本：v1.0
-- 说明：基于优化后的表结构创建测试数据，确保表关联关系正确

-- 1. 插入客户测试数据
INSERT INTO `customers` (`id`, `phone`, `name`, `id_card`, `gender`, `nation`, `address`, `authority`, `valid_start`, `valid_end`, `status`, `created_by`, `created_at`, `updated_at`) VALUES
(1, '***********', '张三', '110101199001011234', 1, '汉', '北京市东城区XX街道XX号', '北京市公安局东城分局', '2020-01-01', '2030-01-01', 'active', 1, NOW(), NOW()),
(2, '***********', '李四', '310101199002022345', 1, '汉', '上海市黄浦区XX路XX号', '上海市公安局黄浦分局', '2019-02-02', '2029-02-02', 'active', 1, NOW(), NOW()),
(3, '13800138003', '王五', '******************', 2, '汉', '广州市天河区XX大道XX号', '广州市公安局天河分局', '2018-03-03', '2028-03-03', 'active', 1, NOW(), NOW()),
(4, '13800138004', '赵六', '330101199004044567', 1, '汉', '杭州市西湖区XX街XX号', '杭州市公安局西湖分局', '2021-04-04', '2031-04-04', 'active', 1, NOW(), NOW()),
(5, '13800138005', '钱七', '320101199005055678', 2, '汉', '南京市鼓楼区XX路XX号', '南京市公安局鼓楼分局', '2020-05-05', '2030-05-05', 'active', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 2. 更新现有的financial_products表，确保有测试产品数据
INSERT INTO `financial_products` (`id`, `product_code`, `product_name`, `product_type`, `annual_rate`, `min_amount`, `max_amount`, `supported_periods`, `description`, `features`, `conditions`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'YXCD001', '优享车贷', 'loan', 0.0750, 50000.00, 1000000.00, '[12, 24, 36, 48, 60]', '适合优质客户的低利率车贷产品', '["免费评估", "快速审批", "低利率", "灵活还款"]', '["月收入5000以上", "征信良好", "有稳定工作"]', 1, 1, NOW(), NOW()),
(2, 'PHCD002', '普惠车贷', 'loan', 0.0950, 30000.00, 800000.00, '[6, 12, 24, 36, 48, 60]', '覆盖面广的标准车贷产品', '["门槛较低", "申请简便", "审批快速"]', '["月收入3000以上", "征信正常", "年龄18-65岁"]', 1, 2, NOW(), NOW()),
(3, 'JSCD003', '急速车贷', 'loan', 0.1200, 20000.00, 500000.00, '[6, 12, 24, 36]', '快速放款的应急车贷产品', '["当天审批", "快速放款", "手续简单"]', '["有固定收入", "征信可查", "提供完整资料"]', 1, 3, NOW(), NOW()),
(4, 'XNYCD004', '新能源车贷', 'loan', 0.0550, 50000.00, 1200000.00, '[12, 24, 36, 48, 60]', '专为新能源汽车设计的贷款产品', '["政策支持", "超低利率", "环保优先", "补贴优惠"]', '["购买新能源车", "征信优良", "有环保意识"]', 1, 4, NOW(), NOW()),
(5, 'ESCD005', '二手车贷', 'loan', 0.1000, 20000.00, 600000.00, '[6, 12, 24, 36, 48]', '专门针对二手车的贷款产品', '["车龄灵活", "评估专业", "手续便捷"]', '["车龄不超过8年", "有车辆评估报告", "征信良好"]', 1, 5, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    product_name = VALUES(product_name),
    annual_rate = VALUES(annual_rate),
    min_amount = VALUES(min_amount),
    max_amount = VALUES(max_amount),
    supported_periods = VALUES(supported_periods),
    description = VALUES(description),
    features = VALUES(features),
    conditions = VALUES(conditions),
    updated_at = NOW();

-- 3. 插入loan_periods测试数据
INSERT INTO `loan_periods` (`id`, `period_months`, `period_label`, `is_default`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 6, '6期', 0, 1, 1, NOW(), NOW()),
(2, 12, '12期', 0, 1, 2, NOW(), NOW()),
(3, 24, '24期', 1, 1, 3, NOW(), NOW()),
(4, 36, '36期', 0, 1, 4, NOW(), NOW()),
(5, 48, '48期', 0, 1, 5, NOW(), NOW()),
(6, 60, '60期', 0, 1, 6, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    period_label = VALUES(period_label),
    is_default = VALUES(is_default),
    updated_at = NOW();

-- 4. 插入客户联系人测试数据
INSERT INTO `customer_contacts` (`id`, `customer_id`, `name`, `relationship`, `phone`, `id_card`, `address`, `work_unit`, `created_at`, `updated_at`) VALUES
(1, 1, '张妻', '配偶', '***********', '110101199002011235', '北京市东城区XX街道XX号', '北京XX公司', NOW(), NOW()),
(2, 1, '张父', '父亲', '13900139002', '110101196001011236', '北京市朝阳区XX路XX号', '已退休', NOW(), NOW()),
(3, 2, '李妻', '配偶', '***********', '310101199003022346', '上海市黄浦区XX路XX号', '上海XX集团', NOW(), NOW()),
(4, 3, '王夫', '配偶', '13900139004', '******************', '广州市天河区XX大道XX号', '广州XX科技', NOW(), NOW()),
(5, 4, '赵妻', '配偶', '13900139005', '330101199005044568', '杭州市西湖区XX街XX号', '杭州XX银行', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 5. 插入客户资产测试数据
INSERT INTO `customer_assets` (`id`, `customer_id`, `work_type`, `company_name`, `monthly_income`, `annual_income`, `has_property`, `property_count`, `property_value`, `property_loan_balance`, `has_vehicle`, `vehicle_count`, `vehicle_value`, `vehicle_loan_balance`, `other_assets`, `total_assets`, `created_at`, `updated_at`) VALUES
(1, 1, '企业高管', '北京XX科技有限公司', 25000.00, 300000.00, 1, 1, 800000.00, 300000.00, 1, 1, 200000.00, 80000.00, 100000.00, 1100000.00, NOW(), NOW()),
(2, 2, '金融从业者', '上海XX银行', 18000.00, 216000.00, 1, 1, 1200000.00, 600000.00, 0, 0, 0.00, 0.00, 50000.00, 1250000.00, NOW(), NOW()),
(3, 3, '医生', '广州XX医院', 22000.00, 264000.00, 1, 2, 1500000.00, 800000.00, 1, 1, 150000.00, 0.00, 80000.00, 1730000.00, NOW(), NOW()),
(4, 4, 'IT工程师', '杭州XX互联网公司', 20000.00, 240000.00, 1, 1, 600000.00, 200000.00, 0, 0, 0.00, 0.00, 120000.00, 720000.00, NOW(), NOW()),
(5, 5, '教师', '南京XX大学', 12000.00, 144000.00, 1, 1, 500000.00, 150000.00, 0, 0, 0.00, 0.00, 30000.00, 530000.00, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 6. 插入客户负债测试数据
INSERT INTO `customer_liabilities` (`id`, `customer_id`, `has_credit_card`, `credit_card_limit`, `credit_card_used`, `has_bank_loan`, `bank_loan_balance`, `bank_loan_monthly`, `has_other_debt`, `other_debt_balance`, `other_debt_monthly`, `total_debt`, `total_monthly_payment`, `debt_to_asset_ratio`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 100000.00, 25000.00, 1, 300000.00, 2500.00, 1, 80000.00, 800.00, 405000.00, 3300.00, 0.3682, NOW(), NOW()),
(2, 2, 1, 80000.00, 15000.00, 1, 600000.00, 5000.00, 0, 0.00, 0.00, 615000.00, 5000.00, 0.4920, NOW(), NOW()),
(3, 3, 1, 150000.00, 30000.00, 1, 800000.00, 6500.00, 0, 0.00, 0.00, 830000.00, 6500.00, 0.4798, NOW(), NOW()),
(4, 4, 1, 60000.00, 12000.00, 1, 200000.00, 1800.00, 0, 0.00, 0.00, 212000.00, 1800.00, 0.2944, NOW(), NOW()),
(5, 5, 1, 40000.00, 8000.00, 1, 150000.00, 1200.00, 0, 0.00, 0.00, 158000.00, 1200.00, 0.2981, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 7. 插入车辆信息测试数据
INSERT INTO `customer_vehicles` (`id`, `customer_id`, `vehicle_type`, `brand`, `model`, `series`, `configuration`, `year`, `fuel_type`, `transmission`, `seats`, `guide_price`, `discount_amount`, `actual_price`, `down_payment`, `loan_amount`, `dealer_name`, `dealer_contact`, `dealer_phone`, `remarks`, `created_at`, `updated_at`) VALUES
(1, 1, '轿车', '奔驰', 'C级', 'C 260 L', '运动版', 2023, '汽油', '自动挡', 5, 350000.00, 20000.00, 330000.00, 100000.00, 230000.00, '北京奔驰4S店', '张经理', '010-12345678', '商务用车', NOW(), NOW()),
(2, 2, 'SUV', '宝马', 'X3', 'xDrive25i', '豪华套装', 2023, '汽油', '自动挡', 5, 420000.00, 15000.00, 405000.00, 120000.00, 285000.00, '上海宝马4S店', '李经理', '021-12345678', '家庭用车', NOW(), NOW()),
(3, 3, '轿车', '奥迪', 'A4L', '40 TFSI', '时尚动感型', 2023, '汽油', '自动挡', 5, 320000.00, 25000.00, 295000.00, 90000.00, 205000.00, '广州奥迪4S店', '王经理', '020-12345678', '代步车辆', NOW(), NOW()),
(4, 4, '新能源', '特斯拉', 'Model 3', '标准续航版', '后轮驱动', 2023, '纯电动', '自动挡', 5, 280000.00, 10000.00, 270000.00, 80000.00, 190000.00, '杭州特斯拉体验店', '赵经理', '0571-12345678', '环保出行', NOW(), NOW()),
(5, 5, '轿车', '大众', '迈腾', '330TSI', 'DSG豪华型', 2022, '汽油', '自动挡', 5, 220000.00, 18000.00, 202000.00, 60000.00, 142000.00, '南京大众4S店', '钱经理', '025-12345678', '性价比高', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 8. 插入业务申请测试数据，关联客户
INSERT INTO `business_applications` (
    `id`, `application_no`, `user_id`, `customer_id`, `channel_code`, `customer_name`, `customer_phone`, `customer_id_card`, 
    `product_id`, `product_name`, `loan_amount`, `loan_period`, `interest_rate`, 
    `vehicle_brand`, `vehicle_model`, `vehicle_vin`, `vehicle_year`, `vehicle_price`, 
    `status`, `status_text`, `approval_notes`,
    `contacts`, `assets`, `liabilities`, `vehicle`,
    `submit_time`, `created_at`, `updated_at`
) VALUES
(
    101, 'CA20241201001', 1, 1, 'DIRECT', '张三', '***********', '110101199001011234',
    1, '优享车贷', 230000.00, 36, 0.0750,
    '奔驰', 'C级', 'LGXC16EV8PN123456', 2023, 330000.00,
    'submitted', '已提交', '客户已提交申请，等待初审',
    JSON_ARRAY(
        JSON_OBJECT('name', '张妻', 'relationship', '配偶', 'phone', '***********', 'id_card', '110101199002011235', 'address', '北京市东城区XX街道XX号', 'work_unit', '北京XX公司')
    ),
    JSON_OBJECT('work_type', '企业高管', 'company_name', '北京XX科技有限公司', 'monthly_income', 25000, 'annual_income', 300000, 'has_property', true, 'property_count', 1, 'property_value', 800000, 'total_assets', 1100000),
    JSON_OBJECT('has_credit_card', true, 'credit_card_limit', 100000, 'credit_card_used', 25000, 'has_bank_loan', true, 'bank_loan_balance', 300000, 'total_debt', 405000, 'debt_to_asset_ratio', 0.3682),
    JSON_OBJECT('vehicle_type', '轿车', 'brand', '奔驰', 'model', 'C级', 'series', 'C 260 L', 'configuration', '运动版', 'year', 2023, 'fuel_type', '汽油', 'guide_price', 350000, 'actual_price', 330000, 'down_payment', 100000, 'loan_amount', 230000),
    NOW(), NOW(), NOW()
),
(
    102, 'CA20241201002', 1, 2, 'DIRECT', '李四', '***********', '310101199002022345',
    2, '普惠车贷', 285000.00, 48, 0.0950,
    '宝马', 'X3', 'LXPD3E2M8KM123456', 2023, 405000.00,
    'initial_review', '初审中', '系统自动初审通过，人工复核中',
    JSON_ARRAY(
        JSON_OBJECT('name', '李妻', 'relationship', '配偶', 'phone', '***********', 'id_card', '310101199003022346', 'address', '上海市黄浦区XX路XX号', 'work_unit', '上海XX集团')
    ),
    JSON_OBJECT('work_type', '金融从业者', 'company_name', '上海XX银行', 'monthly_income', 18000, 'annual_income', 216000, 'has_property', true, 'property_count', 1, 'property_value', 1200000, 'total_assets', 1250000),
    JSON_OBJECT('has_credit_card', true, 'credit_card_limit', 80000, 'credit_card_used', 15000, 'has_bank_loan', true, 'bank_loan_balance', 600000, 'total_debt', 615000, 'debt_to_asset_ratio', 0.4920),
    JSON_OBJECT('vehicle_type', 'SUV', 'brand', '宝马', 'model', 'X3', 'series', 'xDrive25i', 'configuration', '豪华套装', 'year', 2023, 'fuel_type', '汽油', 'guide_price', 420000, 'actual_price', 405000, 'down_payment', 120000, 'loan_amount', 285000),
    DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW()
)
ON DUPLICATE KEY UPDATE 
    customer_id = VALUES(customer_id),
    product_id = VALUES(product_id),
    contacts = VALUES(contacts),
    assets = VALUES(assets),
    liabilities = VALUES(liabilities),
    vehicle = VALUES(vehicle),
    updated_at = NOW();

-- 9. 插入业务附件测试数据
INSERT INTO `business_attachments` (`id`, `business_application_id`, `customer_id`, `file_name`, `file_url`, `file_type`, `file_size`, `category`, `is_required`, `upload_time`, `created_at`, `updated_at`) VALUES
(1, 101, 1, '张三_身份证正面.jpg', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/idcard_front_001.jpg', 'image/jpeg', 2048576, 'id_card_front', 1, NOW(), NOW(), NOW()),
(2, 101, 1, '张三_身份证背面.jpg', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/idcard_back_001.jpg', 'image/jpeg', 1890432, 'id_card_back', 1, NOW(), NOW(), NOW()),
(3, 101, 1, '张三_收入证明.pdf', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/income_proof_001.pdf', 'application/pdf', 5242880, 'income_proof', 1, NOW(), NOW(), NOW()),
(4, 101, 1, '张三_房产证明.jpg', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/property_proof_001.jpg', 'image/jpeg', 3145728, 'property_proof', 0, NOW(), NOW(), NOW()),
(5, 102, 2, '李四_身份证正面.jpg', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/idcard_front_002.jpg', 'image/jpeg', 2097152, 'id_card_front', 1, NOW(), NOW(), NOW()),
(6, 102, 2, '李四_身份证背面.jpg', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/idcard_back_002.jpg', 'image/jpeg', 1835008, 'id_card_back', 1, NOW(), NOW(), NOW()),
(7, 102, 2, '李四_银行流水.pdf', 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/attachments/bank_statement_002.pdf', 'application/pdf', 4194304, 'income_proof', 1, NOW(), NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 10. 更新customer_risk_queries表，关联客户
UPDATE `customer_risk_queries` crq 
LEFT JOIN `customers` c ON crq.customer_phone = c.phone OR crq.customer_id_card = c.id_card
SET crq.customer_id = c.id 
WHERE crq.customer_id IS NULL AND c.id IS NOT NULL;

-- 11. 验证数据关联关系
SELECT 
    'customers' as table_name,
    COUNT(*) as total_count
FROM customers
UNION ALL
SELECT 
    'business_applications_with_customer',
    COUNT(*)
FROM business_applications ba
INNER JOIN customers c ON ba.customer_id = c.id
UNION ALL
SELECT 
    'customer_risk_queries_with_customer',
    COUNT(*)
FROM customer_risk_queries crq
INNER JOIN customers c ON crq.customer_id = c.id
UNION ALL
SELECT 
    'customer_contacts',
    COUNT(*)
FROM customer_contacts
UNION ALL
SELECT 
    'customer_assets',
    COUNT(*)
FROM customer_assets
UNION ALL
SELECT 
    'customer_liabilities',
    COUNT(*)
FROM customer_liabilities
UNION ALL
SELECT 
    'customer_vehicles',
    COUNT(*)
FROM customer_vehicles
UNION ALL
SELECT 
    'business_attachments',
    COUNT(*)
FROM business_attachments;

-- 12. 示例查询：验证客户关联的完整信息
SELECT 
    c.name as customer_name,
    c.phone as customer_phone,
    COUNT(DISTINCT ba.id) as business_applications_count,
    COUNT(DISTINCT cc.id) as contacts_count,
    COUNT(DISTINCT ca.id) as assets_count,
    COUNT(DISTINCT cl.id) as liabilities_count,
    COUNT(DISTINCT cv.id) as vehicles_count,
    COUNT(DISTINCT bat.id) as attachments_count
FROM customers c
LEFT JOIN business_applications ba ON c.id = ba.customer_id
LEFT JOIN customer_contacts cc ON c.id = cc.customer_id
LEFT JOIN customer_assets ca ON c.id = ca.customer_id
LEFT JOIN customer_liabilities cl ON c.id = cl.customer_id
LEFT JOIN customer_vehicles cv ON c.id = cv.customer_id
LEFT JOIN business_attachments bat ON c.id = bat.customer_id
GROUP BY c.id, c.name, c.phone
ORDER BY c.id; 