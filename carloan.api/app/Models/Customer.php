<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Customer extends BaseModel
{
    use SoftDeletes;

    protected $table = 'customers';

    protected $fillable = [
        'phone',
        'name',
        'id_card',
        'id_card_front',
        'id_card_back',
        'gender',
        'nation',
        'address',
        'authority',
        'valid_start',
        'valid_end',
        'status',
        'created_by',
    ];

    protected $casts = [
        'gender' => 'integer',
        'valid_start' => 'date',
        'valid_end' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // 性别常量
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;

    // 状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_BLACKLIST = 'blacklist';

    /**
     * 关联业务申请
     */
    public function businessApplications()
    {
        return $this->hasMany(BusinessApplication::class, 'customer_id');
    }

    /**
     * 关联风险查询
     */
    public function riskQueries()
    {
        return $this->hasMany(CustomerRiskQuery::class, 'customer_id');
    }

    /**
     * 关联联系人
     */
    public function contacts()
    {
        return $this->hasMany(CustomerContact::class);
    }

    /**
     * 关联资产信息
     */
    public function assets()
    {
        return $this->hasMany(CustomerAsset::class);
    }

    /**
     * 关联负债信息
     */
    public function liabilities()
    {
        return $this->hasMany(CustomerLiability::class);
    }

    /**
     * 关联车辆信息
     */
    public function vehicles()
    {
        return $this->hasMany(CustomerVehicle::class);
    }

    /**
     * 关联业务附件
     */
    public function businessAttachments()
    {
        return $this->hasMany(BusinessAttachment::class, 'customer_id');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 范围查询：活跃状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 范围查询：按手机号
     */
    public function scopeByPhone($query, $phone)
    {
        return $query->where('phone', $phone);
    }

    /**
     * 范围查询：按身份证号
     */
    public function scopeByIdCard($query, $idCard)
    {
        return $query->where('id_card', $idCard);
    }

    /**
     * 获取性别文本
     */
    public function getGenderTextAttribute()
    {
        switch ($this->gender) {
            case self::GENDER_MALE:
                return '男';
            case self::GENDER_FEMALE:
                return '女';
            default:
                return '未知';
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        switch ($this->status) {
            case self::STATUS_ACTIVE:
                return '正常';
            case self::STATUS_INACTIVE:
                return '停用';
            case self::STATUS_BLACKLIST:
                return '黑名单';
            default:
                return '未知';
        }
    }

    /**
     * 检查身份证是否有效
     */
    public function isIdCardValid()
    {
        if (!$this->valid_end) {
            return true; // 长期有效
        }
        
        return $this->valid_end >= Carbon::now()->format('Y-m-d');
    }

    /**
     * 获取年龄
     */
    public function getAgeAttribute()
    {
        if (!$this->id_card || strlen($this->id_card) !== 18) {
            return null;
        }

        $birthday = substr($this->id_card, 6, 8);
        $birthYear = substr($birthday, 0, 4);
        $birthMonth = substr($birthday, 4, 2);
        $birthDay = substr($birthday, 6, 2);

        $birthDate = "$birthYear-$birthMonth-$birthDay";
        
        return Carbon::now()->diffInYears($birthDate);
    }

    /**
     * 从身份证号解析性别
     */
    public function parseGenderFromIdCard()
    {
        if (!$this->id_card || strlen($this->id_card) !== 18) {
            return null;
        }

        $genderCode = substr($this->id_card, 16, 1);
        return ($genderCode % 2 === 0) ? self::GENDER_FEMALE : self::GENDER_MALE;
    }

    /**
     * 自动填充身份证解析的信息
     */
    public function fillIdCardInfo()
    {
        if ($this->id_card && !$this->gender) {
            $this->gender = $this->parseGenderFromIdCard();
        }
    }
} 