<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RiskQuery extends Model
{
    use SoftDeletes;

    protected $table = 'risk_queries';

    protected $fillable = [
        'user_id',
        'name',
        'cert_no',
        'phone',
        'query_type',
        'out_trade_no',
        'tran_amt',
        'tran_time',
        'request_params',
        'code',
        'result_msg',
        'result_data',
        'sign',
        'pdf_url',
        'status',
        'error_msg',
        'query_time'
    ];

    protected $casts = [
        'tran_amt' => 'decimal:3',
        'query_type' => 'integer',
        'status' => 'integer',
        'query_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 查询类型常量
     */
    const QUERY_TYPE_OCR = 1;           // 身份证OCR
    const QUERY_TYPE_LAWSUIT = 2;       // 涉诉信息
    const QUERY_TYPE_DISHONEST = 3;     // 失信被执行
    const QUERY_TYPE_HIGH_CONSUME = 4;  // 限制高消费
    const QUERY_TYPE_CREDIT_RISK = 5;   // 信用风险
    const QUERY_TYPE_PRE_LOAN = 6;      // 贷前风险
    const QUERY_TYPE_VERIFY = 7;        // 二要素认证

    /**
     * 状态常量
     */
    const STATUS_FAILED = 0;
    const STATUS_SUCCESS = 1;

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联身份证OCR结果
     */
    public function idCardOcrResult()
    {
        return $this->hasOne(IdCardOcrResult::class, 'risk_query_id');
    }

    /**
     * 关联的客户风险查询（业务层）
     */
    public function customerRiskQuery()
    {
        return $this->hasOne(CustomerRiskQuery::class, 'user_id', 'user_id')
            ->where('customer_name', $this->name)
            ->where('customer_id_card', $this->cert_no);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按查询类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('query_type', $type);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 获取查询类型文本
     */
    public function getQueryTypeTextAttribute()
    {
        $types = [
            self::QUERY_TYPE_OCR => '身份证OCR',
            self::QUERY_TYPE_LAWSUIT => '涉诉信息',
            self::QUERY_TYPE_DISHONEST => '失信被执行',
            self::QUERY_TYPE_HIGH_CONSUME => '限制高消费',
            self::QUERY_TYPE_CREDIT_RISK => '信用风险',
            self::QUERY_TYPE_PRE_LOAN => '贷前风险',
            self::QUERY_TYPE_VERIFY => '二要素认证'
        ];

        return $types[$this->query_type] ?? '未知类型';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status === self::STATUS_SUCCESS ? '成功' : '失败';
    }

    /**
     * 是否成功
     */
    public function getIsSuccessAttribute()
    {
        return $this->status === self::STATUS_SUCCESS;
    }
} 