<template>
	<!-- 金融产品选择 -->
	<view class="product-select">
		<navbar title="选择金融产品" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 产品列表 -->
		<view class="content" v-else>
			<view class="product-list">
				<view 
					class="product-item" 
					v-for="product in productList" 
					:key="product.id"
					:class="{ active: selectedProduct?.id === product.id }"
					@click="selectProduct(product)"
				>
					<view class="product-header">
						<view class="product-name">{{ product.name }}</view>
						<view class="product-type">金融产品</view>
					</view>
					<view class="product-info">
						<view class="info-item">
							<text class="label">年利率：</text>
							<text class="value highlight">{{ (product.annual_rate * 100).toFixed(2) }}%</text>
						</view>
						<view class="info-item">
							<text class="label">额度范围：</text>
							<text class="value">{{ formatAmount(product.min_amount) }} - {{ formatAmount(product.max_amount) }}</text>
						</view>
						<view class="info-item">
							<text class="label">支持期数：</text>
							<text class="value">{{ formatPeriods(product.supported_periods) }}</text>
						</view>
					</view>
					<view class="product-features" v-if="product.features && product.features.length">
						<view class="feature-tag" v-for="feature in product.features.slice(0, 2)" :key="feature">
							{{ feature }}
						</view>
					</view>
					<view class="product-desc" v-if="product.description">
						{{ product.description }}
					</view>
					<view class="select-indicator" v-if="selectedProduct?.id === product.id">
						<image 
							class="check-icon" 
							src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-check-circle.png" 
						/>
					</view>
				</view>
			</view>
			<view style="height: 120rpx;"></view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-btn" v-if="!loading">
				<button 
					class="btn  btn-primary" 
					:disabled="!selectedProduct"
					@click="handleConfirm"
				>
					确认选择
				</button>
			</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const loading = ref(true);
const productList = ref([]);
const selectedProduct = ref(null);

// 页面加载
onMounted(() => {
	loadProducts();
});

// 加载金融产品列表
const loadProducts = async () => {
	try {
		loading.value = true;
		
		const response = await request.get('/financial-products');
		
		if (response.status_code === 200) {
			productList.value = response.data || [];
			// 产品列表加载完成后，设置默认选中
			loadSelectedProduct();
		} else {
			uni.showToast({
				title: response.message || '获取产品列表失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取产品列表失败:', error);
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		});
	} finally {
		loading.value = false;
	}
};

// 加载已选择的产品
const loadSelectedProduct = () => {
	try {
		const businessData = uni.getStorageSync('business_draft');
		if (businessData && businessData.product_id && productList.value.length > 0) {
			const product = productList.value.find(p => p.id === businessData.product_id);
			if (product) {
				selectedProduct.value = product;
				console.log('已设置默认选中产品:', product.name);
			}
		}
	} catch (error) {
		console.error('加载已选产品失败:', error);
	}
};

// 选择产品
const selectProduct = (product) => {
	selectedProduct.value = product;
};

// 获取产品类型文本
const getProductTypeText = (type) => {
	const typeMap = {
		'loan': '贷款',
		'lease': '租赁'
	};
	return typeMap[type] || type;
};

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0';
	return `${(amount / 10000).toFixed(0)}万`;
};

// 格式化期数
const formatPeriods = (periods) => {
	if (!periods || !Array.isArray(periods)) return '';
	return periods.map(p => `${p}期`).join('、');
};

// 确认选择
const handleConfirm = () => {
	if (!selectedProduct.value) {
		return uni.showToast({
			title: '请选择金融产品',
			icon: 'none'
		});
	}

	try {
		// 保存选择的产品到业务数据
		const businessData = uni.getStorageSync('business_draft') || {};
		businessData.product_id = selectedProduct.value.id;
		businessData.product_name = selectedProduct.value.name;
		businessData.product_type = 'loan'; // 默认为贷款类型
		businessData.annual_rate = selectedProduct.value.annual_rate;
		businessData.supported_periods = selectedProduct.value.supported_periods;
		
		uni.setStorageSync('business_draft', businessData);

		uni.showToast({
			title: `已选择：${selectedProduct.value.name}`,
			icon: 'success'
		});

		// 返回上一页
		setTimeout(() => {
			uni.navigateBack();
		}, 1000);
		
	} catch (error) {
		console.error('保存产品选择失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	}
};
</script>

<style lang="scss" scoped>
.product-select {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 120rpx;
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.product-list {
	.product-item {
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		padding: 32rpx;
		position: relative;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
		
		&.active {
			border-color: #1890ff;
			background: #f6ffff;
		}
		
		.product-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;
			
			.product-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
			
			.product-type {
				font-size: 24rpx;
				color: #1890ff;
				background: #e6f7ff;
				padding: 8rpx 16rpx;
				border-radius: 16rpx;
			}
		}
		
		.product-info {
			margin-bottom: 20rpx;
			
			.info-item {
				display: flex;
				align-items: center;
				margin-bottom: 12rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					font-size: 28rpx;
					color: #666;
					width: 160rpx;
					flex-shrink: 0;
				}
				
				.value {
					font-size: 28rpx;
					color: #333;
					flex: 1;
					
					&.highlight {
						color: #f5222d;
						font-weight: 500;
					}
				}
			}
		}
		
		.product-features {
			display: flex;
			flex-wrap: wrap;
			gap: 12rpx;
			margin-bottom: 16rpx;
			
			.feature-tag {
				font-size: 24rpx;
				color: #52c41a;
				background: #f6ffed;
				padding: 6rpx 12rpx;
				border-radius: 12rpx;
				border: 1rpx solid #b7eb8f;
			}
		}
		
		.product-desc {
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
		}
		
		.select-indicator {
			position: absolute;
			top: 32rpx;
			right: 32rpx;
			
			.check-icon {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}
}

</style> 