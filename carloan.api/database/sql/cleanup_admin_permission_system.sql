-- 清理管理后台权限系统SQL
-- 创建时间: 2024-12-25
-- 说明: 用于清理权限系统相关的数据和表（谨慎使用）

-- 警告：此脚本会删除所有权限系统相关的数据，请谨慎使用！
-- 建议在执行前备份数据库

-- 1. 清理测试数据（保留表结构）
-- 注释掉以下代码块以避免误操作

/*
-- 清理角色权限关联
DELETE FROM role_permissions WHERE role_id IN (SELECT id FROM roles WHERE code != 'super_admin');

-- 清理用户角色关联  
DELETE FROM admin_roles WHERE role_id IN (SELECT id FROM roles WHERE code != 'super_admin');

-- 清理测试用户账号
DELETE FROM channel_users WHERE username LIKE '%_admin' OR username LIKE '%_operator';
DELETE FROM finance_users WHERE username LIKE '%_admin' OR username LIKE '%_operator';
DELETE FROM admins WHERE username IN ('platform_admin', 'platform_ops');

-- 清理测试金融公司
DELETE FROM finance_companies WHERE code IN ('CITIC_BANK', 'PING_AN_BANK', 'CMB_BANK');

-- 清理测试角色（保留超级管理员）
DELETE FROM roles WHERE code != 'super_admin';

-- 清理权限数据
DELETE FROM permissions;

-- 清理菜单数据
DELETE FROM menus;
*/

-- 2. 完全清理权限系统（删除表）
-- 注释掉以下代码块以避免误操作

/*
-- 删除关联表（注意顺序）
DROP TABLE IF EXISTS `admin_roles`;
DROP TABLE IF EXISTS `role_permissions`;
DROP TABLE IF EXISTS `channel_users`;
DROP TABLE IF EXISTS `finance_users`;

-- 删除核心表
DROP TABLE IF EXISTS `permissions`;
DROP TABLE IF EXISTS `finance_companies`;
DROP TABLE IF EXISTS `roles`;
DROP TABLE IF EXISTS `menus`;
*/

-- 3. 重置自增ID（如果需要）
-- 注释掉以下代码块以避免误操作

/*
ALTER TABLE roles AUTO_INCREMENT = 1;
ALTER TABLE menus AUTO_INCREMENT = 1;
ALTER TABLE permissions AUTO_INCREMENT = 1;
ALTER TABLE role_permissions AUTO_INCREMENT = 1;
ALTER TABLE finance_companies AUTO_INCREMENT = 1;
ALTER TABLE channel_users AUTO_INCREMENT = 1;
ALTER TABLE finance_users AUTO_INCREMENT = 1;
ALTER TABLE admin_roles AUTO_INCREMENT = 1;
*/

-- 4. 验证清理结果
SELECT 
    'roles' as table_name, 
    COUNT(*) as count 
FROM roles
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'menus' as table_name, 
    COUNT(*) as count 
FROM menus
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'permissions' as table_name, 
    COUNT(*) as count 
FROM permissions
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'role_permissions' as table_name, 
    COUNT(*) as count 
FROM role_permissions
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'finance_companies' as table_name, 
    COUNT(*) as count 
FROM finance_companies
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'channel_users' as table_name, 
    COUNT(*) as count 
FROM channel_users
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'finance_users' as table_name, 
    COUNT(*) as count 
FROM finance_users
WHERE 1 = 0  -- 防止误执行，需要手动修改为 1 = 1

UNION ALL

SELECT 
    'admin_roles' as table_name, 
    COUNT(*) as count 
FROM admin_roles
WHERE 1 = 0; -- 防止误执行，需要手动修改为 1 = 1

-- 5. 使用说明
-- 
-- 如果需要清理测试数据：
-- 1. 取消第1部分的注释
-- 2. 执行SQL
--
-- 如果需要完全删除权限系统：
-- 1. 首先备份数据库
-- 2. 取消第2部分的注释  
-- 3. 执行SQL
--
-- 如果需要重置自增ID：
-- 1. 取消第3部分的注释
-- 2. 执行SQL
--
-- 验证清理结果：
-- 1. 将第4部分的 "1 = 0" 修改为 "1 = 1"
-- 2. 执行SQL查看各表记录数 