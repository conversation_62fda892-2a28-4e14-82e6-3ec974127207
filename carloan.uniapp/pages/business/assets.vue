<template>
	<!-- 资产信息 -->
	<view class="business-assets">
		<navbar title="资产信息" />
		<view class="container">
			<view class="page-title">
				<view>资产信息</view>
				<view class="desc">请如实填写您的资产状况</view>
			</view>
			
			<!-- 工作收入 -->
			<view class="basic-card">
				<view class="card-info">工作收入</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">月收入 <text class="required">*</text></view>
						<input
							class="input"
							v-model="formData.monthly_income"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入月收入（元）"
							@input="calculateData"
						/>
					</view>
					<view class="card-item">
						<view class="label">年收入</view>
						<input
							class="input"
							v-model="formData.annual_income"
							placeholderClass="placeholder"
							type="number"
							placeholder="自动计算"
							disabled
						/>
					</view>
					<view class="card-item">
						<view class="label">工作单位</view>
						<input
							class="input"
							v-model="formData.company"
							placeholderClass="placeholder"
							type="text"
							placeholder="请输入工作单位"
						/>
					</view>
					<view class="card-item">
						<view class="label">职位</view>
						<input
							class="input"
							v-model="formData.position"
							placeholderClass="placeholder"
							type="text"
							placeholder="请输入职位"
						/>
					</view>
					<view class="card-item">
						<view class="label">工作年限</view>
						<view class="select-wrapper" @click="showWorkYearsPicker">
							<input
								class="input select-input"
								v-model="formData.work_years_text"
								placeholderClass="placeholder"
								placeholder="请选择工作年限"
								disabled
							/>
							<image 
								class="arrow-icon" 
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" 
							/>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 房产信息 -->
			<view class="basic-card">
				<view class="card-info">房产信息</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">房产类型</view>
						<view class="select-wrapper" @click="showHouseTypePicker">
							<input
								class="input select-input"
								v-model="formData.house_type_text"
								placeholderClass="placeholder"
								placeholder="请选择房产类型"
								disabled
							/>
							<image 
								class="arrow-icon" 
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" 
							/>
						</view>
					</view>
					<view class="card-item" v-if="formData.house_type && formData.house_type !== 'none'">
						<view class="label">房产估值</view>
						<input
							class="input"
							v-model="formData.house_value"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入房产估值（万元）"
						/>
					</view>
					<view class="card-item" v-if="formData.house_type && formData.house_type !== 'none'">
						<view class="label">剩余房贷</view>
						<input
							class="input"
							v-model="formData.house_loan_remaining"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入剩余房贷（万元）"
						/>
					</view>
				</view>
			</view>
			
			<!-- 车辆信息 -->
			<view class="basic-card">
				<view class="card-info">现有车辆</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">车辆数量</view>
						<view class="select-wrapper" @click="showVehicleCountPicker">
							<input
								class="input select-input"
								v-model="formData.vehicle_count_text"
								placeholderClass="placeholder"
								placeholder="请选择车辆数量"
								disabled
							/>
							<image 
								class="arrow-icon" 
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" 
							/>
						</view>
					</view>
					<view class="card-item" v-if="formData.vehicle_count && formData.vehicle_count > 0">
						<view class="label">车辆总价值</view>
						<input
							class="input"
							v-model="formData.vehicle_value"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入车辆总价值（万元）"
						/>
					</view>
				</view>
			</view>
			
			<!-- 其他资产 -->
			<view class="basic-card">
				<view class="card-info">其他资产</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">银行存款</view>
						<input
							class="input"
							v-model="formData.bank_deposit"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入银行存款（万元）"
						/>
					</view>
					<view class="card-item">
						<view class="label">理财产品</view>
						<input
							class="input"
							v-model="formData.financial_products"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入理财产品价值（万元）"
						/>
					</view>
					<view class="card-item">
						<view class="label">股票基金</view>
						<input
							class="input"
							v-model="formData.stocks_funds"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入股票基金价值（万元）"
						/>
					</view>
					<view class="card-item">
						<view class="label">其他资产</view>
						<input
							class="input"
							v-model="formData.other_assets"
							placeholderClass="placeholder"
							type="number"
							placeholder="请输入其他资产价值（万元）"
						/>
					</view>
				</view>
			</view>
			
			<!-- 资产汇总 -->
			<view class="basic-card">
				<view class="card-info">资产汇总</view>
				<view class="card-container">
					<view class="summary-item">
						<view class="summary-label">年收入：</view>
						<view class="summary-value">{{ formatAmount(formData.annual_income) }}万元</view>
					</view>
					<view class="summary-item">
						<view class="summary-label">总资产：</view>
						<view class="summary-value">{{ formatAmount(totalAssets) }}万元</view>
					</view>
				</view>
			</view>

			<view style="height: 120rpx;"></view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-btn">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '保存中...' : '保存并继续' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const submitLoading = ref(false);

// 选择器选项
const workYearsOptions = ref([
	{ value: 1, text: '1年以下' },
	{ value: 2, text: '1-2年' },
	{ value: 3, text: '3-5年' },
	{ value: 5, text: '5-10年' },
	{ value: 10, text: '10年以上' }
]);

const houseTypeOptions = ref([
	{ value: 'none', text: '无房产' },
	{ value: 'mortgage', text: '按揭房' },
	{ value: 'full', text: '全款房' },
	{ value: 'rent', text: '租房' }
]);

const vehicleCountOptions = ref([
	{ value: 0, text: '无车' },
	{ value: 1, text: '1辆' },
	{ value: 2, text: '2辆' },
	{ value: 3, text: '3辆及以上' }
]);

// 表单数据
const formData = ref({
	// 收入信息
	monthly_income: '',
	annual_income: '',
	company: '',
	position: '',
	work_years: '',
	work_years_text: '',
	
	// 房产信息
	house_type: '',
	house_type_text: '',
	house_value: '',
	house_loan_remaining: '',
	
	// 车辆信息
	vehicle_count: '',
	vehicle_count_text: '',
	vehicle_value: '',
	
	// 其他资产
	bank_deposit: '',
	financial_products: '',
	stocks_funds: '',
	other_assets: ''
});

// 计算总资产
const totalAssets = computed(() => {
	const houseValue = parseFloat(formData.value.house_value) || 0;
	const houseLoan = parseFloat(formData.value.house_loan_remaining) || 0;
	const vehicleValue = parseFloat(formData.value.vehicle_value) || 0;
	const bankDeposit = parseFloat(formData.value.bank_deposit) || 0;
	const financialProducts = parseFloat(formData.value.financial_products) || 0;
	const stocksFunds = parseFloat(formData.value.stocks_funds) || 0;
	const otherAssets = parseFloat(formData.value.other_assets) || 0;
	
	const houseNet = Math.max(0, houseValue - houseLoan);
	
	return houseNet + vehicleValue + bankDeposit + financialProducts + stocksFunds + otherAssets;
});

// 计算是否可以提交
const canSubmit = computed(() => {
	return formData.value.monthly_income;
});

// 页面加载
onMounted(() => {
	loadAssetsData();
});

// 页面显示时刷新数据
onShow(() => {
	loadAssetsData();
});

// 加载资产数据
const loadAssetsData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft');
		if (businessData && businessData.assets) {
			formData.value = { ...formData.value, ...businessData.assets };
		}
	} catch (error) {
		console.error('加载资产数据失败:', error);
	}
};

// 保存资产数据
const saveAssetsData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft') || {};
		businessData.assets = formData.value;
		businessData.assets_completed = canSubmit.value;
		uni.setStorageSync('business_draft', businessData);
	} catch (error) {
		console.error('保存资产数据失败:', error);
	}
};

// 计算数据
const calculateData = () => {
	// 自动计算年收入
	if (formData.value.monthly_income) {
		formData.value.annual_income = (parseFloat(formData.value.monthly_income) * 12).toString();
	}
	saveAssetsData();
};

// 显示工作年限选择器
const showWorkYearsPicker = () => {
	const itemList = workYearsOptions.value.map(item => item.text);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = workYearsOptions.value[res.tapIndex];
			formData.value.work_years = selectedOption.value;
			formData.value.work_years_text = selectedOption.text;
			saveAssetsData();
		}
	});
};

// 显示房产类型选择器
const showHouseTypePicker = () => {
	const itemList = houseTypeOptions.value.map(item => item.text);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = houseTypeOptions.value[res.tapIndex];
			formData.value.house_type = selectedOption.value;
			formData.value.house_type_text = selectedOption.text;
			
			// 如果选择无房产，清空相关字段
			if (selectedOption.value === 'none') {
				formData.value.house_value = '';
				formData.value.house_loan_remaining = '';
			}
			
			saveAssetsData();
		}
	});
};

// 显示车辆数量选择器
const showVehicleCountPicker = () => {
	const itemList = vehicleCountOptions.value.map(item => item.text);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = vehicleCountOptions.value[res.tapIndex];
			formData.value.vehicle_count = selectedOption.value;
			formData.value.vehicle_count_text = selectedOption.text;
			
			// 如果选择无车，清空车辆价值
			if (selectedOption.value === 0) {
				formData.value.vehicle_value = '';
			}
			
			saveAssetsData();
		}
	});
};

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return 0;
	return parseFloat(amount).toFixed(1);
};

// 提交数据
const handleSubmit = async () => {
	try {
		// 验证必填项
		if (!formData.value.monthly_income) {
			return uni.showToast({
				title: '请填写月收入',
				icon: 'none'
			});
		}

		submitLoading.value = true;
		
		// 获取客户手机号
		const businessData = uni.getStorageSync('business_draft') || {};
		if (!businessData.customer_phone) {
			return uni.showToast({
				title: '请先完成客户验证',
				icon: 'none'
			});
		}
		
		// 调用后端API保存资产信息
		const response = await request.post('/business-applications/save-asset', {
			customer_phone: businessData.customer_phone,
			work_type: formData.value.work_years,
			company_name: formData.value.company,
			monthly_income: parseFloat(formData.value.monthly_income) || 0,
			annual_income: parseFloat(formData.value.annual_income) || 0,
			has_property: formData.value.house_type !== 'none',
			property_count: formData.value.house_type !== 'none' ? 1 : 0,
			property_value: parseFloat(formData.value.house_value) || 0,
			property_loan_balance: parseFloat(formData.value.house_loan_remaining) || 0,
			has_vehicle: formData.value.vehicle_count > 0,
			vehicle_count: parseInt(formData.value.vehicle_count) || 0,
			vehicle_value: parseFloat(formData.value.vehicle_value) || 0,
			vehicle_loan_balance: 0,
			other_assets: (parseFloat(formData.value.bank_deposit) || 0) + 
						 (parseFloat(formData.value.financial_products) || 0) + 
						 (parseFloat(formData.value.stocks_funds) || 0) + 
						 (parseFloat(formData.value.other_assets) || 0)
		});

		if (response.status_code === 200) {
			// 保存本地数据
			saveAssetsData();
			
			// 更新完成状态
			businessData.assets_completed = true;
			uni.setStorageSync('business_draft', businessData);

			uni.showToast({
				title: '资产信息保存成功',
				icon: 'success'
			});

			// 返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 1000);
		} else {
			uni.showToast({
				title: response.message || '保存失败',
				icon: 'none'
			});
		}
		
	} catch (error) {
		console.error('保存资产信息失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.business-assets {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 120rpx;
}

.container {
	padding: 20rpx;
}

.page-title {
	padding: 40rpx 20rpx 20rpx;
	text-align: left;
	
	view:first-child {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 12rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
	}
}

.basic-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 24rpx;
	
	.card-info {
		padding: 32rpx 32rpx 16rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.card-container {
		padding: 32rpx;
		
		.card-item {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				width: 160rpx;
				font-size: 30rpx;
				color: #333;
				flex-shrink: 0;
				
				.required {
					color: #ff4d4f;
				}
			}
			
			.input {
				flex: 1;
				height: 64rpx;
				border: 2rpx solid #d0d0d0;
				border-radius: 8rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				color: #333;
				background: #fff;
				
				&.select-input {
					color: #666;
				}
				
				&:disabled {
					background: #f5f5f5;
					color: #999;
				}
			}
			
			.select-wrapper {
				position: relative;
				flex: 1;
				
				.arrow-icon {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
		
		.summary-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.summary-label {
				font-size: 30rpx;
				color: #666;
			}
			
			.summary-value {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
}

</style> 