-- 审批工作流与业务申请系统集成指南
-- 创建时间: 2024-03-17
-- 说明: 描述新的审批工作流系统如何与现有的business_applications表集成

/*
===============================================================================
                          审批工作流系统集成说明
===============================================================================

1. 业务流程串联逻辑：
   
   A. 业务申请提交时 (BusinessApplicationController::store)
      ├── 创建 BusinessApplication 记录，状态为 'submitted'
      ├── 调用 ApprovalWorkflowService::startWorkflow()
      ├── 创建初审 ApprovalTask (step_code='initial_review')
      └── 创建相关 ApprovalCcRecord

   B. 审批任务完成时 (ApprovalWorkflowService::completeTask)
      ├── 更新 ApprovalTask 状态 (pending → approved/rejected)
      ├── 自动推进到下一审批步骤
      ├── 更新 BusinessApplication 状态
      └── 兼容原有 approval_history 字段

2. 数据表关联关系：
   
   business_applications (主表)
   ├── approval_tasks (1:N) - 审批任务
   ├── approval_cc_records (1:N) - 抄送记录
   └── approval_history (JSON) - 兼容原有历史记录

3. 状态映射关系：
   
   审批步骤代码              对应的业务申请状态
   ──────────────────      ────────────────────
   initial_review          initial_review
   final_review           final_review  
   secondary_review       secondary_review
   interview_schedule     interview_pending
   contract_review        contract_pending

4. 关键集成点：

   A. 业务申请提交
      - 文件: BusinessApplicationController.php
      - 方法: store()
      - 集成: 调用 ApprovalWorkflowService::startWorkflow()

   B. 审批操作
      - 文件: FinalReviewController.php
      - 方法: approve(), secondaryApprove()
      - 集成: 使用 ApprovalTask 和 ApprovalWorkflowService

   C. 个人中心统计
      - 文件: ProfileController.php
      - 方法: getProfileInfo()
      - 集成: 查询 ApprovalTask 和 ApprovalCcRecord

5. 兼容性保证：
   
   - 保留原有 approval_history JSON 字段
   - 同时记录到新表和原有字段
   - 渐进式迁移，不影响现有功能

6. 扩展能力：
   
   - 支持多种业务类型的审批流程
   - 支持动态配置审批步骤和人员
   - 支持自动抄送和手动抄送
   - 支持任务超时处理
   - 支持审批历史追溯

===============================================================================
*/

-- 5. 验证集成的查询示例

-- 查询某个申请的完整审批流程
SELECT 
    ba.id,
    ba.application_no,
    ba.status as current_status,
    at.step_name,
    at.approver_name,
    at.status as task_status,
    at.approve_result,
    at.approve_notes,
    at.assigned_time,
    at.completed_time
FROM business_applications ba
LEFT JOIN approval_tasks at ON ba.id = at.application_id
WHERE ba.id = 1
ORDER BY at.step_order, at.assigned_time;

-- 查询用户的待审批任务统计
SELECT 
    COUNT(*) as pending_count,
    step_name,
    step_code
FROM approval_tasks 
WHERE approver_id = 1 
AND status = 'pending'
GROUP BY step_name, step_code;

-- 查询用户的抄送记录统计
SELECT 
    COUNT(*) as total_cc,
    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_cc
FROM approval_cc_records 
WHERE cc_user_id = 1;

-- 查询用户发起的申请统计
SELECT 
    COUNT(*) as my_applications,
    status,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
FROM business_applications 
WHERE user_id = 1
GROUP BY status;

-- 6. 数据迁移脚本（如果需要）

-- 为现有业务申请创建审批任务（谨慎执行）
/*
INSERT INTO approval_tasks (
    application_id, workflow_id, task_no, step_name, step_code, 
    step_order, approver_id, approver_name, status, status_text,
    assigned_time, completed_time, approve_result, created_at, updated_at
)
SELECT 
    ba.id as application_id,
    aw.id as workflow_id,
    CONCAT('TASK', DATE_FORMAT(ba.submit_time, '%Y%m%d'), LPAD(ba.id, 4, '0')) as task_no,
    aw.step_name,
    aw.step_code,
    aw.step_order,
    2 as approver_id, -- 需要根据实际情况调整
    'System Migration' as approver_name,
    CASE 
        WHEN ba.status = 'approved' THEN 'approved'
        WHEN ba.status = 'rejected' THEN 'rejected'
        ELSE 'pending'
    END as status,
    CASE 
        WHEN ba.status = 'approved' THEN '已通过'
        WHEN ba.status = 'rejected' THEN '已拒绝'
        ELSE '待审批'
    END as status_text,
    ba.submit_time as assigned_time,
    ba.approval_time as completed_time,
    CASE 
        WHEN ba.status = 'approved' THEN 1
        WHEN ba.status = 'rejected' THEN 0
        ELSE NULL
    END as approve_result,
    NOW() as created_at,
    NOW() as updated_at
FROM business_applications ba
JOIN approval_workflows aw ON aw.business_type = 'business_application'
WHERE ba.submit_time >= '2024-01-01'  -- 只迁移指定日期后的数据
AND NOT EXISTS (
    SELECT 1 FROM approval_tasks at 
    WHERE at.application_id = ba.id
);
*/ 