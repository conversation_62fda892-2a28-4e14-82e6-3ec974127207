<template>
	<!-- 直系亲属联系人信息 -->
	<view class="business-contact">
		<navbar title="联系人信息" />
		<view class="container">
			<view class="page-title">
				<view>直系亲属联系人信息</view>
				<view class="desc">请填写至少一位直系亲属联系人</view>
			</view>
			
			<!-- 联系人列表 -->
			<view class="contact-list">
				<view 
					class="contact-item" 
					v-for="(contact, index) in contactList" 
					:key="contact.id || index"
				>
					<view class="contact-header">
						<view class="contact-title">联系人{{ index + 1 }}</view>
						<view class="contact-actions">
							<button 
								class="btn-delete" 
								@click="deleteContact(index)"
								v-if="contactList.length > 1"
							>
								删除
							</button>
						</view>
					</view>
					
					<view class="basic-card">
						<view class="card-container">
							<view class="card-item">
								<view class="label">姓名 <text class="required">*</text></view>
								<input
									class="input"
									v-model="contact.name"
									placeholderClass="placeholder"
									type="text"
									placeholder="请输入联系人姓名"
									@blur="validateContact(index)"
								/>
							</view>
							<view class="card-item">
								<view class="label">关系 <text class="required">*</text></view>
								<view class="select-wrapper" @click="showRelationPicker(index)">
									<input
										class="input select-input"
										v-model="contact.relation_text"
										placeholderClass="placeholder"
										placeholder="请选择关系"
										disabled
									/>
									<image 
										class="arrow-icon" 
										src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" 
									/>
								</view>
							</view>
							<view class="card-item">
								<view class="label">手机号 <text class="required">*</text></view>
								<input
									class="input"
									v-model="contact.phone"
									placeholderClass="placeholder"
									type="number"
									maxlength="11"
									placeholder="请输入手机号"
									@blur="validateContact(index)"
								/>
							</view>
							<view class="card-item">
								<view class="label">身份证号</view>
								<input
									class="input"
									v-model="contact.id_card"
									placeholderClass="placeholder"
									type="text"
									maxlength="18"
									placeholder="请输入身份证号（选填）"
								/>
							</view>
							<view class="card-item">
								<view class="label">工作单位</view>
								<input
									class="input"
									v-model="contact.company"
									placeholderClass="placeholder"
									type="text"
									placeholder="请输入工作单位（选填）"
								/>
							</view>
							<view class="card-item">
								<view class="label">备注</view>
								<textarea
									class="textarea"
									v-model="contact.remark"
									placeholderClass="placeholder"
									placeholder="请输入备注信息（选填）"
									maxlength="200"
								/>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 添加联系人按钮 -->
			<view class="add-contact" v-if="contactList.length < 3">
				<button class="btn btn-dashed" @click="addContact">
					<text class="plus-icon">+</text>
					添加联系人
				</button>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-btn">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '保存中...' : '保存并继续' }}
			</button>
		</view>
		
		<!-- 关系选择器 -->
		<picker
			v-if="showPicker"
			:value="currentPickerIndex"
			:range="relationOptions"
			range-key="text"
			@change="onRelationChange"
			@cancel="showPicker = false"
		>
		</picker>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const submitLoading = ref(false);
const showPicker = ref(false);
const currentPickerIndex = ref(0);
const currentContactIndex = ref(0);

// 关系选项
const relationOptions = ref([
	{ value: 'spouse', text: '配偶' },
	{ value: 'parent', text: '父亲/母亲' },
	{ value: 'child', text: '子女' },
	{ value: 'sibling', text: '兄弟/姐妹' },
	{ value: 'other', text: '其他亲属' }
]);

// 联系人列表
const contactList = ref([
	{
		id: null,
		name: '',
		relation: '',
		relation_text: '',
		phone: '',
		id_card: '',
		company: '',
		remark: ''
	}
]);

// 计算是否可以提交
const canSubmit = computed(() => {
	return contactList.value.every(contact => {
		return contact.name && 
			   contact.relation && 
			   contact.phone && 
			   /^1[3-9]\d{9}$/.test(contact.phone);
	});
});

// 页面加载
onMounted(() => {
	loadContactData();
});

// 页面显示时刷新数据
onShow(() => {
	loadContactData();
});

// 加载联系人数据
const loadContactData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft');
		if (businessData && businessData.contacts && businessData.contacts.length > 0) {
			contactList.value = businessData.contacts;
		}
		
		// 如果有客户手机号，从服务器获取最新数据
		if (businessData && businessData.customer_phone) {
			loadDraftDataFromServer(businessData.customer_phone);
		}
	} catch (error) {
		console.error('加载联系人数据失败:', error);
	}
};

// 从服务器加载草稿数据
const loadDraftDataFromServer = async (customerPhone) => {
	try {
		const response = await request.get('/business-applications/draft-data', {
			customer_phone: customerPhone
		});
		
		if (response.status_code === 200 && response.data.has_data && response.data.contacts) {
			const contacts = response.data.contacts;
			if (contacts.length > 0) {
				// 转换服务器数据格式
				contactList.value = contacts.map(contact => ({
					id: null,
					name: contact.name,
					relation: contact.relationship,
					relation_text: getRelationText(contact.relationship),
					phone: contact.phone,
					id_card: contact.id_card || '',
					company: contact.work_unit || '',
					remark: contact.address || ''
				}));
				
				// 保存到本地存储
				saveContactData();
			}
		}
	} catch (error) {
		console.error('从服务器加载联系人数据失败:', error);
	}
};

// 根据关系值获取关系文本
const getRelationText = (relationValue) => {
	const relation = relationOptions.value.find(option => option.value === relationValue);
	return relation ? relation.text : '';
};

// 保存联系人数据
const saveContactData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft') || {};
		businessData.contacts = contactList.value;
		businessData.contact_completed = canSubmit.value;
		uni.setStorageSync('business_draft', businessData);
	} catch (error) {
		console.error('保存联系人数据失败:', error);
	}
};

// 添加联系人
const addContact = () => {
	if (contactList.value.length >= 3) {
		return uni.showToast({
			title: '最多添加3个联系人',
			icon: 'none'
		});
	}
	
	contactList.value.push({
		id: null,
		name: '',
		relation: '',
		relation_text: '',
		phone: '',
		id_card: '',
		company: '',
		remark: ''
	});
};

// 删除联系人
const deleteContact = (index) => {
	if (contactList.value.length <= 1) {
		return uni.showToast({
			title: '至少保留一个联系人',
			icon: 'none'
		});
	}
	
	uni.showModal({
		title: '确认删除',
		content: '确定要删除这个联系人吗？',
		success: (res) => {
			if (res.confirm) {
				contactList.value.splice(index, 1);
				saveContactData();
			}
		}
	});
};

// 显示关系选择器
const showRelationPicker = (index) => {
	currentContactIndex.value = index;
	const contact = contactList.value[index];
	const relationIndex = relationOptions.value.findIndex(opt => opt.value === contact.relation);
	currentPickerIndex.value = relationIndex >= 0 ? relationIndex : 0;
	showPicker.value = true;
	
	// 使用uni.showActionSheet替代picker
	const itemList = relationOptions.value.map(item => item.text);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = relationOptions.value[res.tapIndex];
			const contact = contactList.value[currentContactIndex.value];
			contact.relation = selectedOption.value;
			contact.relation_text = selectedOption.text;
			saveContactData();
		}
	});
};

// 关系选择变化
const onRelationChange = (e) => {
	const selectedOption = relationOptions.value[e.detail.value];
	const contact = contactList.value[currentContactIndex.value];
	contact.relation = selectedOption.value;
	contact.relation_text = selectedOption.text;
	showPicker.value = false;
	saveContactData();
};

// 验证联系人信息
const validateContact = (index) => {
	const contact = contactList.value[index];
	
	// 验证手机号
	if (contact.phone && !/^1[3-9]\d{9}$/.test(contact.phone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return false;
	}
	
	// 验证身份证号
	if (contact.id_card && !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(contact.id_card)) {
		uni.showToast({
			title: '请输入正确的身份证号',
			icon: 'none'
		});
		return false;
	}
	
	saveContactData();
	return true;
};

// 提交数据
const handleSubmit = async () => {
	try {
		// 验证所有联系人
		let isValid = true;
		for (let i = 0; i < contactList.value.length; i++) {
			if (!validateContact(i)) {
				isValid = false;
				break;
			}
		}
		
		if (!isValid) return;
		
		// 检查必填项
		for (let i = 0; i < contactList.value.length; i++) {
			const contact = contactList.value[i];
			if (!contact.name) {
				return uni.showToast({
					title: `请填写联系人${i + 1}的姓名`,
					icon: 'none'
				});
			}
			if (!contact.relation) {
				return uni.showToast({
					title: `请选择联系人${i + 1}的关系`,
					icon: 'none'
				});
			}
			if (!contact.phone) {
				return uni.showToast({
					title: `请填写联系人${i + 1}的手机号`,
					icon: 'none'
				});
			}
		}

		submitLoading.value = true;
		
		// 获取客户手机号
		const businessData = uni.getStorageSync('business_draft') || {};
		if (!businessData.customer_phone) {
			return uni.showToast({
				title: '请先完成客户验证',
				icon: 'none'
			});
		}
		
		// 调用后端API保存联系人信息
		const contacts = contactList.value.map(contact => ({
			name: contact.name,
			relationship: contact.relation,
			phone: contact.phone,
			id_card: contact.id_card || null,
			work_unit: contact.company || null,
			address: contact.remark || null
		}));

		const response = await request.post('/business-applications/save-contact', {
			customer_phone: businessData.customer_phone,
			contacts: contacts
		});

		if (response.status_code === 200) {
			// 保存本地数据
			saveContactData();
			
			// 更新完成状态
			businessData.contact_completed = true;
			uni.setStorageSync('business_draft', businessData);

			uni.showToast({
				title: '联系人信息保存成功',
				icon: 'success'
			});

			// 返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 1000);
		} else {
			uni.showToast({
				title: response.message || '保存失败',
				icon: 'none'
			});
		}
		
	} catch (error) {
		console.error('保存联系人信息失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.business-contact {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 120rpx;
}

.container {
	padding: 20rpx;
}

.page-title {
	padding: 40rpx 20rpx 20rpx;
	text-align: left;
	
	view:first-child {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 12rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
	}
}

.contact-list {
	.contact-item {
		margin-bottom: 32rpx;
		
		.contact-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 20rpx 16rpx;
			
			.contact-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
			
			.contact-actions {
				.btn-delete {
					background: none;
					border: none;
					color: #ff4d4f;
					font-size: 28rpx;
					padding: 0;
				}
			}
		}
	}
}

.basic-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	
	.card-container {
		padding: 32rpx;
		
		.card-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 32rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				width: 160rpx;
				font-size: 30rpx;
				color: #333;
				flex-shrink: 0;
				padding-top: 16rpx;
				
				.required {
					color: #ff4d4f;
				}
			}
			
			.input, .textarea {
				flex: 1;
				height: 64rpx;
				border: 2rpx solid #d0d0d0;
				border-radius: 8rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				color: #333;
				background: #fff;
				
				&.select-input {
					color: #666;
				}
			}
			
			.textarea {
				height: 120rpx;
				padding: 16rpx 20rpx;
				resize: none;
			}
			
			.select-wrapper {
				position: relative;
				flex: 1;
				
				.arrow-icon {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
}

.add-contact {
	padding: 0 20rpx;
	margin-top: 32rpx;
	
	.btn-dashed {
		width: 100%;
		height: 80rpx;
		background: #fff;
		border: 2rpx dashed #d0d0d0;
		border-radius: 8rpx;
		color: #666;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		.plus-icon {
			font-size: 36rpx;
			margin-right: 12rpx;
		}
	}
}


</style> 