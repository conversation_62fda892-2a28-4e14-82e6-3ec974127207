-- 金融产品表DDL建表语句
-- 创建时间: 2024-03-17
-- 说明: 车贷金融产品信息表，用于还款试算功能

CREATE TABLE `financial_products` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品代码',
  `product_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `product_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'loan' COMMENT '产品类型:loan贷款,lease租赁',
  `annual_rate` decimal(6,4) NOT NULL DEFAULT '0.0000' COMMENT '年利率',
  `min_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最低贷款额度',
  `max_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最高贷款额度',
  `supported_periods` json NOT NULL COMMENT '支持的期数列表',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '产品描述',
  `features` json DEFAULT NULL COMMENT '产品特色',
  `conditions` json DEFAULT NULL COMMENT '申请条件',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `financial_products_product_code_unique` (`product_code`),
  KEY `financial_products_product_type_index` (`product_type`),
  KEY `financial_products_status_index` (`status`),
  KEY `financial_products_status_sort_index` (`status`,`sort_order`),
  KEY `financial_products_annual_rate_index` (`annual_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金融产品表';

-- 创建贷款期数表（可选，用于管理标准期数选项）
CREATE TABLE `loan_periods` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `period_months` int(11) NOT NULL COMMENT '期数（月）',
  `period_label` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '期数标签',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认期数:1是,0否',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `loan_periods_period_months_unique` (`period_months`),
  KEY `loan_periods_status_index` (`status`),
  KEY `loan_periods_is_default_index` (`is_default`),
  KEY `loan_periods_status_sort_index` (`status`,`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='贷款期数表'; 