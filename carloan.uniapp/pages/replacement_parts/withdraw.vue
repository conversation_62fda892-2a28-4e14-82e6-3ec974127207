<template>
	<!-- 撤销补件页面 -->
	<view class="withdraw-page">
		<navbar title="撤销补件" />
		
		<!-- 如果是驳回后的补件，显示驳回原因 -->
		<view class="reject-reason-section" v-if="supplementData.status === 'rejected' && supplementData.review_notes">
			<view class="section-header">
				<view class="section-title">驳回原因</view>
			</view>
			<view class="reject-reason-content">
				<text>{{ supplementData.review_notes }}</text>
			</view>
		</view>
		
		<!-- 补件基本信息 -->
		<view class="info-section">
			<view class="section-header">
				<view class="section-title">补件信息</view>
			</view>
			
			<view class="info-content">
				<view class="info-item">
					<view class="info-label">补件单号：</view>
					<view class="info-value">{{ supplementData.supplement_no }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">客户：</view>
					<view class="info-value">{{ supplementData.application?.customer_name }}</view>
					<view class="phone-action" v-if="supplementData.application?.customer_phone" @click="makePhoneCall">
						<wd-icon name="call" size="20" color="#1890ff"></wd-icon>
					</view>
				</view>
				<view class="info-item">
					<view class="info-label">申请单号：</view>
					<view class="info-value">{{ supplementData.application?.application_no }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">产品名称：</view>
					<view class="info-value">{{ supplementData.application?.product_name }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">当前状态：</view>
					<view class="info-value" :class="getStatusClass(supplementData.status)">
						{{ supplementData.status_text }}
					</view>
				</view>
				<view class="info-item">
					<view class="info-label">提交时间：</view>
					<view class="info-value">{{ supplementData.created_time }}</view>
				</view>
				<view class="info-item" v-if="supplementData.deadline">
					<view class="info-label">截止时间：</view>
					<view class="info-value" :class="{ 'text-danger': supplementData.is_overdue }">
						{{ supplementData.deadline }}
						<text v-if="supplementData.remaining_days !== null" class="remaining-time">
							({{ supplementData.is_overdue ? '已逾期' : `剩余${supplementData.remaining_days}天` }})
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 撤销原因输入 -->
		<view class="reason-section">
			<view class="section-header">
				<view class="section-title">撤销原因</view>
				<view class="section-subtitle">请说明撤销此补件的原因</view>
			</view>
			
			<view class="reason-content">
				<textarea 
					v-model="withdrawReason"
					placeholder="请输入撤销原因..." 
					maxlength="500"
					class="reason-textarea"
				></textarea>
				<view class="char-count">{{ withdrawReason.length }}/500</view>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button 
				class="btn btn-primary-block" 
				@click="onConfirmWithdraw"
				:disabled="!withdrawReason.trim() || submitting"
			>
				{{ submitting ? '提交中...' : '确认撤销' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 路由参数
const supplementId = ref('')

// 响应式数据
const supplementData = ref({})
const withdrawReason = ref('')
const submitting = ref(false)

// 页面加载时获取补件详情
onMounted(() => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	supplementId.value = currentPage.options.id
	
	if (supplementId.value) {
		fetchSupplementDetail()
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	}
})

// 获取补件详情
const fetchSupplementDetail = async () => {
	try {
		uni.showLoading({ title: '加载中...' })
		
		const response = await request.get(`/supplements/${supplementId.value}`)
		
		if (response && response.status_code === 200) {
			supplementData.value = response.data
		} else {
			throw new Error(response.message || '获取补件详情失败')
		}
	} catch (error) {
		console.error('获取补件详情失败:', error)
		uni.showToast({
			title: error.message || '获取补件详情失败',
			icon: 'none'
		})
		
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	} finally {
		uni.hideLoading()
	}
}

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		'pending': 'status-warning',
		'submitted': 'status-info', 
		'approved': 'status-success',
		'rejected': 'status-error'
	}
	return classMap[status] || 'status-default'
}

// 拨打电话
const makePhoneCall = () => {
	const phoneNumber = supplementData.value.application?.customer_phone
	if (!phoneNumber) {
		uni.showToast({
			title: '电话号码不存在',
			icon: 'none'
		})
		return
	}
	
	uni.makePhoneCall({
		phoneNumber: phoneNumber,
		success: () => {
			console.log('拨打电话成功')
		},
		fail: (error) => {
			console.error('拨打电话失败:', error)
			uni.showToast({
				title: '拨打电话失败',
				icon: 'none'
			})
		}
	})
}

// 确认撤销
const onConfirmWithdraw = async () => {
	if (!withdrawReason.value.trim()) {
		uni.showToast({
			title: '请输入撤销原因',
			icon: 'none'
		})
		return
	}
	
	// 二次确认
	try {
		const result = await uni.showModal({
			title: '确认撤销',
			content: '确定要撤销此补件吗？撤销后无法恢复。',
			confirmText: '确认撤销',
			cancelText: '我再想想'
		})
		
		if (!result.confirm) {
			return
		}
	} catch (error) {
		console.error('弹窗错误:', error)
		return
	}
	
	try {
		submitting.value = true
		
		const response = await request.post(`/supplements/${supplementId.value}/withdraw`, {
			withdraw_reason: withdrawReason.value.trim()
		})
		
		if (response && response.status_code === 200) {
			uni.showToast({
				title: '撤销成功',
				icon: 'success'
			})
			
			setTimeout(() => {
				// 返回列表页并刷新
				uni.navigateBack({
					success: () => {
						// 通知列表页刷新
						uni.$emit('refreshSupplementList')
					}
				})
			}, 1500)
		} else {
			throw new Error(response.message || '撤销失败')
		}
	} catch (error) {
		console.error('撤销补件失败:', error)
		uni.showToast({
			title: error.message || '撤销失败，请重试',
			icon: 'none'
		})
	} finally {
		submitting.value = false
	}
}
</script>

<style lang="scss" scoped>
.withdraw-page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.reject-reason-section {
	background: #fff2f0;
	border-left: 6rpx solid #ff4d4f;
	margin: 0;
	border-radius: 0;
	
	.section-header {
		padding: 30rpx 30rpx 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #ff4d4f;
		}
	}
	
	.reject-reason-content {
		padding: 0 30rpx 30rpx;
		
		text {
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}
	}
}

.info-section,
.reason-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 12rpx;
	
	.section-header {
		padding: 30rpx 30rpx 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
		
		.section-subtitle {
			font-size: 26rpx;
			color: #666;
			margin-top: 10rpx;
		}
	}
}

.info-content {
	padding: 20rpx 30rpx 30rpx;
	
	.info-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.info-label {
			width: 160rpx;
			font-size: 28rpx;
			color: #666;
			flex-shrink: 0;
		}
		
		.info-value {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			
			&.text-danger {
				color: #ff4d4f;
			}
			
			&.status-warning {
				color: #fa8c16;
			}
			
			&.status-info {
				color: #1890ff;
			}
			
			&.status-success {
				color: #52c41a;
			}
			
			&.status-error {
				color: #ff4d4f;
			}
			
			.remaining-time {
				font-size: 24rpx;
				color: #999;
			}
		}
		
		.phone-action {
			margin-left: 20rpx;
			padding: 10rpx;
			border-radius: 50%;
			background: rgba(24, 144, 255, 0.1);
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			
			&:active {
				background: rgba(24, 144, 255, 0.2);
			}
		}
	}
}

.reason-content {
	padding: 20rpx 30rpx 30rpx;
	
	.reason-textarea {
		width: 100%;
		min-height: 200rpx;
		padding: 20rpx;
		border: 2rpx solid #e8e8e8;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
		background: #fafafa;
		resize: none;
		box-sizing: border-box;
		
		&:focus {
			border-color: #1890ff;
			background: #fff;
		}
	}
	
	.char-count {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	
	.btn {
		width: 100%;
		
		&.btn-primary-block {
			background: #000000;
			color: #ffffff;
			border-radius: 12rpx;
			border: none;
			padding: 12rpx 0;
			font-size: 32rpx;
			font-weight: 600;
			
			&:disabled {
				background: #ccc;
				color: #999;
			}
		}
	}
}
</style> 