<?php

namespace App\Services;

use App\Models\ThirdPartyApiLog;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ThirdPartyApiLogService
{
    /**
     * 记录第三方接口调用
     *
     * @param string $serviceName 服务名称
     * @param string $apiName 接口名称
     * @param string $method 请求方法
     * @param string $url 请求地址
     * @param array|null $headers 请求头
     * @param array|null $params 请求参数
     * @param int|null $responseStatus 响应状态码
     * @param array|null $responseHeaders 响应头
     * @param mixed $responseBody 响应内容
     * @param int|null $duration 请求耗时(毫秒)
     * @param string|null $errorMessage 错误信息
     * @param bool $isSuccess 是否成功
     * @param int|null $businessId 业务ID
     * @param string|null $businessType 业务类型
     * @return ThirdPartyApiLog
     */
    public static function log(
        string $serviceName,
        string $apiName,
        string $method,
        string $url,
        ?array $headers = null,
        ?array $params = null,
        ?int $responseStatus = null,
        ?array $responseHeaders = null,
        $responseBody = null,
        ?int $duration = null,
        ?string $errorMessage = null,
        bool $isSuccess = false,
        ?int $businessId = null,
        ?string $businessType = null
    ): ThirdPartyApiLog {
        try {
            // 对敏感信息进行脱敏处理
            $sanitizedHeaders = self::sanitizeHeaders($headers);
            $sanitizedParams = self::sanitizeParams($params);
            $sanitizedResponseBody = self::sanitizeResponseBody($responseBody);

            $logData = [
                'service_name' => $serviceName,
                'api_name' => $apiName,
                'method' => strtoupper($method),
                'request_url' => $url,
                'request_headers' => $sanitizedHeaders,
                'request_params' => $sanitizedParams,
                'response_status' => $responseStatus,
                'response_headers' => $responseHeaders,
                'response_body' => $sanitizedResponseBody,
                'duration_ms' => $duration,
                'error_message' => $errorMessage,
                'is_success' => $isSuccess,
                'business_id' => $businessId,
                'business_type' => $businessType,
                'request_time' => Carbon::now()
            ];

            return ThirdPartyApiLog::create($logData);
        } catch (\Exception $e) {
            // 记录日志失败不应该影响业务流程
            Log::error('记录第三方接口调用日志失败: ' . $e->getMessage(), [
                'service_name' => $serviceName,
                'api_name' => $apiName,
                'error' => $e->getMessage()
            ]);
            
            // 返回一个空的模型实例
            return new ThirdPartyApiLog();
        }
    }

    /**
     * 脱敏请求头信息
     */
    private static function sanitizeHeaders(?array $headers): ?array
    {
        if (!$headers) {
            return null;
        }

        $sensitiveKeys = [
            'authorization',
            'x-tsign-open-token',
            'x-api-key',
            'cookie',
            'set-cookie'
        ];

        $sanitized = [];
        foreach ($headers as $key => $value) {
            $lowerKey = strtolower($key);
            if (in_array($lowerKey, $sensitiveKeys)) {
                $sanitized[$key] = self::maskSensitiveData($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * 脱敏请求参数
     */
    private static function sanitizeParams(?array $params): ?array
    {
        if (!$params) {
            return null;
        }

        $sensitiveKeys = [
            'password',
            'secret',
            'token',
            'key',
            'appSecret',
            'app_secret',
            'appCode', // 车300应用码
            'idNumber',
            'id_number',
            'cert_no',
            'mobile',
            'phone',
            'email',
            'vin', // 车架号
            'VIN', // 车架号
            'attachment', // 图片base64数据
            'driver_url' // 行驶证图片URL
        ];

        return self::maskSensitiveArray($params, $sensitiveKeys);
    }

    /**
     * 脱敏响应内容
     */
    private static function sanitizeResponseBody($responseBody)
    {
        if (!$responseBody) {
            return null;
        }

        // 如果是数组，进行脱敏处理
        if (is_array($responseBody)) {
            $sensitiveKeys = [
                'token',
                'accessToken',
                'access_token',
                'idNumber',
                'id_number',
                'cert_no',
                'mobile',
                'phone',
                'email'
            ];

            return self::maskSensitiveArray($responseBody, $sensitiveKeys);
        }

        // 如果是字符串，检查是否过长
        if (is_string($responseBody) && strlen($responseBody) > 10000) {
            return substr($responseBody, 0, 10000) . '...[truncated]';
        }

        return $responseBody;
    }

    /**
     * 递归脱敏数组中的敏感信息
     */
    private static function maskSensitiveArray(array $data, array $sensitiveKeys): array
    {
        $result = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = self::maskSensitiveArray($value, $sensitiveKeys);
            } elseif (in_array($key, $sensitiveKeys)) {
                $result[$key] = self::maskSensitiveData($value);
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    /**
     * 脱敏敏感数据
     */
    private static function maskSensitiveData($value): string
    {
        if (!$value) {
            return '';
        }

        $strValue = (string)$value;
        $length = strlen($strValue);

        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        // 显示前2位和后2位，中间用*号替代
        return substr($strValue, 0, 2) . str_repeat('*', $length - 4) . substr($strValue, -2);
    }

    /**
     * 获取指定服务的调用统计
     */
    public static function getServiceStats(string $serviceName, ?string $startDate = null, ?string $endDate = null): array
    {
        $query = ThirdPartyApiLog::byService($serviceName);

        if ($startDate && $endDate) {
            $query->byTimeRange($startDate, $endDate);
        }

        $total = $query->count();
        $success = $query->bySuccess(true)->count();
        $failed = $total - $success;
        $successRate = $total > 0 ? round(($success / $total) * 100, 2) : 0;

        return [
            'service_name' => $serviceName,
            'total_calls' => $total,
            'success_calls' => $success,
            'failed_calls' => $failed,
            'success_rate' => $successRate . '%',
            'period' => $startDate && $endDate ? "{$startDate} ~ {$endDate}" : '全部时间'
        ];
    }

    /**
     * 获取失败的调用记录
     */
    public static function getFailedCalls(string $serviceName, int $limit = 10): array
    {
        return ThirdPartyApiLog::byService($serviceName)
            ->bySuccess(false)
            ->orderBy('request_time', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }
} 