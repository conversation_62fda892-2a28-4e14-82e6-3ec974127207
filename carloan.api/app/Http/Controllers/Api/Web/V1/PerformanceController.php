<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\BusinessApplication;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PerformanceController extends BaseController
{
    /**
     * 获取业绩统计数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取月份参数，默认为当前月
            $month = $request->input('month', date('Y-m'));
            
            // 验证月份格式
            if (!preg_match('/^\d{4}-\d{2}$/', $month)) {
                return $this->errorHandler('月份格式不正确', 400);
            }

            // 解析年月
            $year = substr($month, 0, 4);
            $monthNum = substr($month, 5, 2);
            
            // 获取本月开始和结束时间
            $startOfMonth = Carbon::createFromDate($year, $monthNum, 1)->startOfMonth();
            $endOfMonth = Carbon::createFromDate($year, $monthNum, 1)->endOfMonth();

            // 获取业绩统计数据
            $performanceData = $this->calculatePerformance($user->id, $startOfMonth, $endOfMonth);
            
            return $this->successHandler('获取业绩统计成功', 200, $performanceData);

        } catch (\Exception $e) {
            \Log::error('获取业绩统计失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取业绩统计失败', 500);
        }
    }

    /**
     * 计算业绩数据
     * 
     * @param int $userId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function calculatePerformance($userId, $startDate, $endDate)
    {
        // 获取当月业务申请
        $monthlyApplications = BusinessApplication::where('user_id', $userId)
            ->whereBetween('submit_time', [$startDate, $endDate])
            ->get();

        // 获取全部业务申请
        $allApplications = BusinessApplication::where('user_id', $userId)
            ->whereNotNull('submit_time')
            ->get();

        // 计算当月业绩（贷款金额总和）
        $monthlyPerformance = $monthlyApplications->sum('loan_amount');
        
        // 计算全部业绩（贷款金额总和）
        $totalPerformance = $allApplications->sum('loan_amount');

        // 按业务类型分别统计当月业绩
        $monthlyCarLoanPerformance = $monthlyApplications
            ->where('business_type', BusinessApplication::BUSINESS_TYPE_CAR_LOAN)
            ->sum('loan_amount');
        
        $monthlyLeaseToOwnPerformance = $monthlyApplications
            ->where('business_type', BusinessApplication::BUSINESS_TYPE_LEASE_TO_OWN)
            ->sum('loan_amount');

        // 获取新增客户数（当月首次提交业务的客户）
        $newCustomers = $this->getNewCustomersCount($userId, $startDate, $endDate);

        // 获取新增业务数（当月提交的业务数量）
        $newBusiness = $monthlyApplications->count();

        // 按业务类型统计新增业务数
        $newCarLoanBusiness = $monthlyApplications
            ->where('business_type', BusinessApplication::BUSINESS_TYPE_CAR_LOAN)
            ->count();
        
        $newLeaseToOwnBusiness = $monthlyApplications
            ->where('business_type', BusinessApplication::BUSINESS_TYPE_LEASE_TO_OWN)
            ->count();

        // 计算业务通过率
        $passRate = $this->calculatePassRate($monthlyApplications);

        // 获取用户的业绩目标（从用户表的monthly_performance_target字段获取，转换为万元）
        $user = \App\Models\User::find($userId);
        $performanceTarget = $user ? round($user->monthly_performance_target / 10000, 2) : 100; // 默认100万元

        return [
            'performance' => [
                'monthly' => round($monthlyPerformance / 10000, 2), // 转换为万元
                'total' => round($totalPerformance / 10000, 2),     // 转换为万元
                'target' => $performanceTarget,                      // 业绩目标（万元）
                'completion_rate' => $performanceTarget > 0 ? round(($monthlyPerformance / 10000) / $performanceTarget * 100, 1) : 0,
                
                // 按业务类型分别统计
                'by_business_type' => [
                    'car_loan' => [
                        'performance' => round($monthlyCarLoanPerformance / 10000, 2),
                        'business_count' => $newCarLoanBusiness
                    ],
                    'lease_to_own' => [
                        'performance' => round($monthlyLeaseToOwnPerformance / 10000, 2),
                        'business_count' => $newLeaseToOwnBusiness
                    ]
                ]
            ],
            'business' => [
                'new_customers' => $newCustomers,
                'new_business' => $newBusiness,
                'pass_rate' => $passRate,
                
                // 按业务类型分别统计
                'by_business_type' => [
                    'car_loan_count' => $newCarLoanBusiness,
                    'lease_to_own_count' => $newLeaseToOwnBusiness
                ]
            ],
            'month' => $startDate->format('Y-m'),
            'period' => $startDate->format('Y年n月')
        ];
    }

    /**
     * 获取新增客户数
     * 
     * @param int $userId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return int
     */
    private function getNewCustomersCount($userId, $startDate, $endDate)
    {
        // 获取当月期间该业务员首次提交业务的客户
        $newCustomers = DB::table('business_applications')
            ->select('customer_phone')
            ->where('user_id', $userId)
            ->whereBetween('submit_time', [$startDate, $endDate])
            ->whereNotIn('customer_phone', function($query) use ($userId, $startDate) {
                $query->select('customer_phone')
                    ->from('business_applications')
                    ->where('user_id', $userId)
                    ->where('submit_time', '<', $startDate);
            })
            ->distinct()
            ->count();

        return $newCustomers;
    }

    /**
     * 计算业务通过率
     * 
     * @param \Illuminate\Database\Eloquent\Collection $applications
     * @return int
     */
    private function calculatePassRate($applications)
    {
        if ($applications->count() == 0) {
            return 0;
        }

        // 已通过的业务状态
        $passedStatuses = [
            BusinessApplication::STATUS_APPROVED,
            BusinessApplication::STATUS_CONTRACT_COMPLETED,
            BusinessApplication::STATUS_CONTRACT_PROCESSING,
            BusinessApplication::STATUS_CONTRACT_PENDING
        ];

        $passedCount = $applications->whereIn('status', $passedStatuses)->count();
        $totalCount = $applications->count();

        return round(($passedCount / $totalCount) * 100);
    }

    /**
     * 获取可选择的月份列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableMonths(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取用户第一笔业务的时间
            $firstApplication = BusinessApplication::where('user_id', $user->id)
                ->whereNotNull('submit_time')
                ->orderBy('submit_time', 'asc')
                ->first();

            if (!$firstApplication) {
                // 如果没有业务记录，返回当前月份
                $months = [
                    [
                        'value' => date('Y-m'),
                        'text' => date('n') . '月',
                        'full_text' => date('Y年n月')
                    ]
                ];
            } else {
                $startDate = Carbon::parse($firstApplication->submit_time);
                $endDate = Carbon::now();
                
                $months = [];
                $current = $startDate->copy()->startOfMonth();
                
                while ($current <= $endDate) {
                    $months[] = [
                        'value' => $current->format('Y-m'),
                        'text' => $current->format('n') . '月',
                        'full_text' => $current->format('Y年n月')
                    ];
                    $current->addMonth();
                }
                
                // 倒序排列，最新的月份在前面
                $months = array_reverse($months);
            }

            return $this->successHandler('获取月份列表成功', 200, [
                'months' => $months,
                'current_month' => date('Y-m')
            ]);

        } catch (\Exception $e) {
            \Log::error('获取月份列表失败: ' . $e->getMessage());
            return $this->errorHandler('获取月份列表失败', 500);
        }
    }
} 