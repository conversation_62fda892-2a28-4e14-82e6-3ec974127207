<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerAsset extends Model
{
    protected $table = 'customer_assets';

    protected $fillable = [
        'customer_id',
        'business_application_id',
        'work_type',
        'company_name',
        'monthly_income',
        'annual_income',
        'has_property',
        'property_count',
        'property_value',
        'property_loan_balance',
        'has_vehicle',
        'vehicle_count',
        'vehicle_value',
        'vehicle_loan_balance',
        'other_assets',
        'total_assets'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'business_application_id' => 'integer',
        'monthly_income' => 'decimal:2',
        'annual_income' => 'decimal:2',
        'has_property' => 'boolean',
        'property_count' => 'integer',
        'property_value' => 'decimal:2',
        'property_loan_balance' => 'decimal:2',
        'has_vehicle' => 'boolean',
        'vehicle_count' => 'integer',
        'vehicle_value' => 'decimal:2',
        'vehicle_loan_balance' => 'decimal:2',
        'other_assets' => 'decimal:2',
        'total_assets' => 'decimal:2'
    ];

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'business_application_id');
    }

    /**
     * 计算总资产
     */
    public function calculateTotalAssets()
    {
        $total = 0;
        
        if ($this->property_value) {
            $total += $this->property_value - ($this->property_loan_balance ?? 0);
        }
        
        if ($this->vehicle_value) {
            $total += $this->vehicle_value - ($this->vehicle_loan_balance ?? 0);
        }
        
        if ($this->other_assets) {
            $total += $this->other_assets;
        }
        
        $this->total_assets = $total;
        return $total;
    }

    /**
     * 计算净房产价值
     */
    public function getNetPropertyValueAttribute()
    {
        return $this->property_value - ($this->property_loan_balance ?? 0);
    }

    /**
     * 计算净车辆价值
     */
    public function getNetVehicleValueAttribute()
    {
        return $this->vehicle_value - ($this->vehicle_loan_balance ?? 0);
    }

    /**
     * 作用域：按客户ID筛选
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * 作用域：按业务申请ID筛选
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('business_application_id', $applicationId);
    }

    /**
     * 作用域：有房产的客户
     */
    public function scopeWithProperty($query)
    {
        return $query->where('has_property', true);
    }

    /**
     * 作用域：有车辆的客户
     */
    public function scopeWithVehicle($query)
    {
        return $query->where('has_vehicle', true);
    }
} 