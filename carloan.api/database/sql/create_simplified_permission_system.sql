-- 简化的管理后台权限系统
-- 创建时间: 2024-12-25
-- 说明: 基于现有users和admins表的简化权限系统，权限控制采用权限码方式

-- 1. 角色表（简化设计）
CREATE TABLE IF NOT EXISTS `roles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色类型:admin管理员,user业务员,finance金融',
  `permissions` json DEFAULT NULL COMMENT '权限码列表(JSON数组)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_code_unique` (`code`),
  KEY `roles_type_index` (`type`),
  KEY `roles_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 2. 为users表添加角色关联和后台登录权限字段
-- 检查并添加role_id字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'role_id') > 0,
    'SELECT "role_id column already exists" as msg',
    'ALTER TABLE `users` ADD COLUMN `role_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT ''角色ID'' AFTER `channel_code`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加can_login_admin字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'can_login_admin') > 0,
    'SELECT "can_login_admin column already exists" as msg',
    'ALTER TABLE `users` ADD COLUMN `can_login_admin` tinyint(1) NOT NULL DEFAULT ''0'' COMMENT ''是否可以登录后台:1是,0否'' AFTER `role_id`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加admin_permissions字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'admin_permissions') > 0,
    'SELECT "admin_permissions column already exists" as msg',
    'ALTER TABLE `users` ADD COLUMN `admin_permissions` json DEFAULT NULL COMMENT ''额外的管理权限(JSON数组)'' AFTER `can_login_admin`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加users表的索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_role_id') > 0,
    'SELECT "idx_role_id index already exists" as msg',
    'ALTER TABLE `users` ADD INDEX `idx_role_id` (`role_id`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_can_login_admin') > 0,
    'SELECT "idx_can_login_admin index already exists" as msg',
    'ALTER TABLE `users` ADD INDEX `idx_can_login_admin` (`can_login_admin`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为admins表添加角色关联
-- 检查并添加role_id字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admins' 
     AND COLUMN_NAME = 'role_id') > 0,
    'SELECT "role_id column already exists in admins" as msg',
    'ALTER TABLE `admins` ADD COLUMN `role_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT ''角色ID'' AFTER `phone`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加admin_permissions字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admins' 
     AND COLUMN_NAME = 'admin_permissions') > 0,
    'SELECT "admin_permissions column already exists in admins" as msg',
    'ALTER TABLE `admins` ADD COLUMN `admin_permissions` json DEFAULT NULL COMMENT ''额外的管理权限(JSON数组)'' AFTER `role_id`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加admins表的索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admins' 
     AND INDEX_NAME = 'idx_role_id') > 0,
    'SELECT "idx_role_id index already exists in admins" as msg',
    'ALTER TABLE `admins` ADD INDEX `idx_role_id` (`role_id`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 插入基础角色数据
INSERT IGNORE INTO `roles` (`name`, `code`, `description`, `type`, `permissions`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
-- 管理员角色
(
  '超级管理员', 
  'super_admin', 
  '系统超级管理员，拥有所有权限', 
  'admin',
  JSON_ARRAY('*'),
  1, 1, NOW(), NOW()
),
(
  '平台管理员', 
  'platform_admin', 
  '平台管理员，负责平台整体运营管理', 
  'admin',
  JSON_ARRAY(
    'dashboard', 'business.*', 'customer.*', 'approval.*', 
    'channel.*', 'report.*', 'setting.basic'
  ),
  1, 2, NOW(), NOW()
),
(
  '财务管理员', 
  'finance_admin', 
  '金融/财务管理员，负责审批和风控', 
  'admin',
  JSON_ARRAY(
    'dashboard', 'business.view', 'business.approve', 'customer.view', 
    'customer.risk', 'approval.*', 'report.business', 'report.finance'
  ),
  1, 3, NOW(), NOW()
),

-- 业务员角色（可选择性登录后台）
(
  '渠道管理员', 
  'channel_manager', 
  '渠道管理员，可以登录后台查看业务数据', 
  'user',
  JSON_ARRAY(
    'dashboard', 'business.view', 'business.create', 'business.edit',
    'customer.view', 'customer.create', 'customer.leads',
    'report.channel', 'report.personal'
  ),
  1, 4, NOW(), NOW()
),
(
  '高级业务员', 
  'senior_sales', 
  '高级业务员，可以登录后台查看数据', 
  'user',
  JSON_ARRAY(
    'dashboard', 'business.view', 'business.create', 
    'customer.view', 'customer.create', 'report.personal'
  ),
  1, 5, NOW(), NOW()
),
(
  '普通业务员', 
  'sales', 
  '普通业务员，只能使用APP', 
  'user',
  JSON_ARRAY(),
  1, 6, NOW(), NOW()
);

-- 5. 创建默认管理员账号
UPDATE `admins` SET 
  `role_id` = (SELECT id FROM roles WHERE code = 'super_admin'),
  `admin_permissions` = JSON_ARRAY('*')
WHERE `name` = '超级管理员';

-- 如果admin账号不存在，则创建
INSERT IGNORE INTO `admins` (`name`, `password`, `phone`, `role_id`, `admin_permissions`, `status`, `created_at`, `updated_at`)
VALUES (
  '超级管理员', 
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
  '***********',
  (SELECT id FROM roles WHERE code = 'super_admin'),
  JSON_ARRAY('*'),
  1, NOW(), NOW()
);

-- 6. 创建几个示例管理员账号
INSERT IGNORE INTO `admins` (`name`, `password`, `phone`, `role_id`, `status`, `created_at`, `updated_at`) VALUES
(
  '平台管理员', 
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
  '13800138001',
  (SELECT id FROM roles WHERE code = 'platform_admin'),
  1, NOW(), NOW()
),
(
  '财务管理员', 
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
  '13800138002',
  (SELECT id FROM roles WHERE code = 'finance_admin'),
  1, NOW(), NOW()
);

-- 7. 设置部分users为可以登录后台的渠道管理员
UPDATE `users` SET 
  `role_id` = (SELECT id FROM roles WHERE code = 'channel_manager'),
  `can_login_admin` = 1
WHERE `id` IN (1, 2) -- 假设ID为1,2的用户是渠道管理员
AND EXISTS (SELECT 1 FROM roles WHERE code = 'channel_manager');

-- 8. 权限码说明表（用于文档和前端参考）
CREATE TABLE IF NOT EXISTS `permission_codes` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限码',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `group_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限分组',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限描述',
  `frontend_route` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对应的前端路由',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_codes_code_unique` (`code`),
  KEY `permission_codes_group_name_index` (`group_name`),
  KEY `permission_codes_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限码说明表';

-- 9. 插入权限码说明数据
INSERT IGNORE INTO `permission_codes` (`code`, `name`, `group_name`, `description`, `frontend_route`, `status`, `created_at`, `updated_at`) VALUES
-- 通用权限
('*', '所有权限', '系统', '超级管理员权限，包含所有功能', '*', 1, NOW(), NOW()),
('dashboard', '首页', '通用', '访问后台首页', '/dashboard', 1, NOW(), NOW()),

-- 业务管理权限
('business.*', '业务管理(全部)', '业务管理', '业务管理所有权限', '/business/*', 1, NOW(), NOW()),
('business.view', '查看业务', '业务管理', '查看业务申请列表', '/business', 1, NOW(), NOW()),
('business.create', '创建业务', '业务管理', '创建业务申请', '/business/create', 1, NOW(), NOW()),
('business.edit', '编辑业务', '业务管理', '编辑业务申请', '/business/edit', 1, NOW(), NOW()),
('business.delete', '删除业务', '业务管理', '删除业务申请', NULL, 1, NOW(), NOW()),
('business.approve', '审批业务', '业务管理', '审批业务申请', '/business/approve', 1, NOW(), NOW()),
('business.contract', '合同管理', '业务管理', '管理业务合同', '/business/contract', 1, NOW(), NOW()),

-- 客户管理权限
('customer.*', '客户管理(全部)', '客户管理', '客户管理所有权限', '/customer/*', 1, NOW(), NOW()),
('customer.view', '查看客户', '客户管理', '查看客户列表', '/customer', 1, NOW(), NOW()),
('customer.create', '创建客户', '客户管理', '创建客户资料', '/customer/create', 1, NOW(), NOW()),
('customer.edit', '编辑客户', '客户管理', '编辑客户资料', '/customer/edit', 1, NOW(), NOW()),
('customer.delete', '删除客户', '客户管理', '删除客户资料', NULL, 1, NOW(), NOW()),
('customer.risk', '风险查询', '客户管理', '查询客户风险信息', '/customer/risk', 1, NOW(), NOW()),
('customer.leads', '销售线索', '客户管理', '管理销售线索', '/customer/leads', 1, NOW(), NOW()),

-- 审批管理权限
('approval.*', '审批管理(全部)', '审批管理', '审批管理所有权限', '/approval/*', 1, NOW(), NOW()),
('approval.pending', '待我审批', '审批管理', '查看待我审批的业务', '/approval/pending', 1, NOW(), NOW()),
('approval.cc', '抄送我的', '审批管理', '查看抄送我的业务', '/approval/cc', 1, NOW(), NOW()),
('approval.initiated', '我发起的', '审批管理', '查看我发起的业务', '/approval/initiated', 1, NOW(), NOW()),
('approval.workflow', '审批流程', '审批管理', '管理审批流程', '/approval/workflow', 1, NOW(), NOW()),

-- 渠道管理权限
('channel.*', '渠道管理(全部)', '渠道管理', '渠道管理所有权限', '/channel/*', 1, NOW(), NOW()),
('channel.view', '查看渠道', '渠道管理', '查看渠道列表', '/channel', 1, NOW(), NOW()),
('channel.create', '创建渠道', '渠道管理', '创建渠道', '/channel/create', 1, NOW(), NOW()),
('channel.edit', '编辑渠道', '渠道管理', '编辑渠道信息', '/channel/edit', 1, NOW(), NOW()),
('channel.users', '渠道用户', '渠道管理', '管理渠道用户', '/channel/users', 1, NOW(), NOW()),

-- 报表权限
('report.*', '报表统计(全部)', '报表统计', '所有报表权限', '/report/*', 1, NOW(), NOW()),
('report.business', '业务报表', '报表统计', '查看业务相关报表', '/report/business', 1, NOW(), NOW()),
('report.customer', '客户报表', '报表统计', '查看客户相关报表', '/report/customer', 1, NOW(), NOW()),
('report.channel', '渠道报表', '报表统计', '查看渠道相关报表', '/report/channel', 1, NOW(), NOW()),
('report.finance', '财务报表', '报表统计', '查看财务相关报表', '/report/finance', 1, NOW(), NOW()),
('report.personal', '个人报表', '报表统计', '查看个人业绩报表', '/report/personal', 1, NOW(), NOW()),

-- 系统设置权限
('setting.*', '系统设置(全部)', '系统设置', '系统设置所有权限', '/setting/*', 1, NOW(), NOW()),
('setting.basic', '基础设置', '系统设置', '系统基础设置', '/setting/basic', 1, NOW(), NOW()),
('setting.users', '用户管理', '系统设置', '管理系统用户', '/setting/users', 1, NOW(), NOW()),
('setting.roles', '角色管理', '系统设置', '管理系统角色', '/setting/roles', 1, NOW(), NOW());

-- 10. 验证数据
SELECT 
    '角色表' as table_name, 
    COUNT(*) as count 
FROM roles
UNION ALL
SELECT 
    '权限码表' as table_name, 
    COUNT(*) as count 
FROM permission_codes
UNION ALL
SELECT 
    '管理员' as table_name, 
    COUNT(*) as count 
FROM admins
UNION ALL
SELECT 
    '可登录后台的用户' as table_name, 
    COUNT(*) as count 
FROM users 
WHERE can_login_admin = 1; 