<template>
	<!-- 客户管理表单 -->
	<view class="customer-form">
		<navbar :title="isEditMode ? '编辑客户' : '新增客户'" />
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text>加载中...</text>
		</view>
		
		<view class="container" v-else>
			<view class="form-section">
				<view class="form-item">
					<view class="label">手机号 <text class="required">*</text></view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.phone"
							placeholder="请输入手机号"
							type="number"
							maxlength="11"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">姓名</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.name"
							placeholder="请输入客户姓名"
							maxlength="20"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">身份证号</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.id_card"
							placeholder="请输入身份证号"
							maxlength="18"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">性别</view>
					<picker 
						:value="genderIndex" 
						:range="genderOptions" 
						range-key="label"
						@change="handleGenderChange"
					>
						<view class="picker-item">
							<text>{{ genderOptions[genderIndex].label }}</text>
							<text class="arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<view class="label">民族</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.nation"
							placeholder="请输入民族"
							maxlength="10"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">地址</view>
					<view class="input-container">
						<textarea
							class="form-textarea"
							v-model="formData.address"
							placeholder="请输入地址"
							maxlength="200"
							:auto-height="true"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">签发机关</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.authority"
							placeholder="请输入签发机关"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">有效期开始日期</view>
					<picker
						mode="date"
						:value="formData.valid_start"
						@change="handleValidStartChange"
					>
						<view class="picker-item">
							<text>{{ formData.valid_start || '请选择开始日期' }}</text>
							<text class="arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<view class="label">有效期结束日期</view>
					<picker
						mode="date"
						:value="formData.valid_end"
						@change="handleValidEndChange"
					>
						<view class="picker-item">
							<text>{{ formData.valid_end || '请选择结束日期（长期有效可不填）' }}</text>
							<text class="arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<view class="label">状态</view>
					<view class="switch-container">
						<picker 
							:value="statusIndex" 
							:range="statusOptions" 
							range-key="label"
							@change="handleStatusChange"
						>
							<view class="picker-item">
								<text>{{ statusOptions[statusIndex].label }}</text>
								<text class="arrow">▼</text>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<view class="btn-container" v-if="!loading">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? (isEditMode ? '保存中...' : '提交中...') : (isEditMode ? '保存修改' : '确认新增') }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 页面状态
const customerId = ref('');
const isEditMode = ref(false);
const loading = ref(false);

// 表单数据
const formData = ref({
	phone: '',
	name: '',
	id_card: '',
	gender: 0,
	nation: '',
	address: '',
	authority: '',
	valid_start: '',
	valid_end: '',
	status: 'active'
});

const submitLoading = ref(false);

// 性别选项
const genderOptions = ref([
	{ label: '未设置', value: 0 },
	{ label: '男', value: 1 },
	{ label: '女', value: 2 }
]);
const genderIndex = ref(0);

// 状态选项
const statusOptions = ref([
	{ label: '正常', value: 'active' },
	{ label: '停用', value: 'inactive' },
	{ label: '黑名单', value: 'blacklist' }
]);
const statusIndex = ref(0);

// 计算是否可以提交
const canSubmit = computed(() => {
	return formData.value.phone.trim();
});

// 页面加载时获取参数
onMounted(() => {
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage.options;
	
	if (options.id) {
		// 编辑模式
		customerId.value = options.id;
		isEditMode.value = true;
		loadCustomerDetail();
	} else {
		// 创建模式
		isEditMode.value = false;
		loading.value = false;
	}
});

// 加载客户详情（编辑模式）
const loadCustomerDetail = async () => {
	try {
		loading.value = true;
		const response = await request.get(`/customers/${customerId.value}`);
		
		if (response.status_code === 200) {
			const data = response.data;
			formData.value = {
				phone: data.phone || '',
				name: data.name || '',
				id_card: data.id_card || '',
				gender: data.gender || 0,
				nation: data.nation || '',
				address: data.address || '',
				authority: data.authority || '',
				valid_start: data.valid_start || '',
				valid_end: data.valid_end || '',
				status: data.status || 'active'
			};
			
			// 设置选择器的索引
			genderIndex.value = genderOptions.value.findIndex(item => item.value === data.gender) || 0;
			statusIndex.value = statusOptions.value.findIndex(item => item.value === data.status) || 0;
		} else {
			uni.showToast({
				title: '获取客户信息失败',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	} catch (error) {
		console.error('获取客户详情失败:', error);
		uni.showToast({
			title: '网络错误',
			icon: 'none'
		});
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	} finally {
		loading.value = false;
	}
};

// 性别切换处理
const handleGenderChange = (e) => {
	genderIndex.value = e.detail.value;
	formData.value.gender = genderOptions.value[e.detail.value].value;
};

// 状态切换处理
const handleStatusChange = (e) => {
	statusIndex.value = e.detail.value;
	formData.value.status = statusOptions.value[e.detail.value].value;
};

// 有效期开始日期处理
const handleValidStartChange = (e) => {
	formData.value.valid_start = e.detail.value;
};

// 有效期结束日期处理
const handleValidEndChange = (e) => {
	formData.value.valid_end = e.detail.value;
};

// 提交表单
const handleSubmit = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请完善必填信息',
			icon: 'none'
		});
		return;
	}
	
	// 验证手机号格式
	const phoneRegex = /^1[3-9]\d{9}$/;
	if (!phoneRegex.test(formData.value.phone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return;
	}
	
	// 验证身份证号格式（如果填写了）
	if (formData.value.id_card) {
		const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
		if (!idCardRegex.test(formData.value.id_card)) {
			uni.showToast({
				title: '请输入正确的身份证号',
				icon: 'none'
			});
			return;
		}
	}
	
	submitLoading.value = true;
	
	try {
		const requestData = {
			phone: formData.value.phone.trim(),
			name: formData.value.name.trim(),
			id_card: formData.value.id_card.trim(),
			gender: formData.value.gender,
			nation: formData.value.nation.trim(),
			address: formData.value.address.trim(),
			authority: formData.value.authority.trim(),
			valid_start: formData.value.valid_start,
			valid_end: formData.value.valid_end,
			status: formData.value.status
		};
		
		let response;
		if (isEditMode.value) {
			// 编辑模式
			response = await request.put(`/customers/${customerId.value}`, requestData);
		} else {
			// 创建模式
			response = await request.post('/customers', requestData);
		}
		
		if (response.status_code === 200) {
			uni.showToast({
				title: isEditMode.value ? '编辑成功' : '新增成功',
				icon: 'success'
			});
			
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		} else {
			uni.showToast({
				title: response.message || (isEditMode.value ? '编辑失败' : '新增失败'),
				icon: 'none'
			});
		}
	} catch (error) {
		console.error(isEditMode.value ? '编辑客户失败:' : '新增客户失败:', error);
		uni.showToast({
			title: '操作失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.customer-form {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300rpx;
	font-size: 28rpx;
	color: #666;
}

.container {
	padding: 20rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	
	.form-item {
		margin-bottom: 40rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 16rpx;
			font-weight: 500;
			
			.required {
				color: #FF4D4F;
				margin-left: 4rpx;
			}
		}
		
		.input-container {
			.form-input, .form-textarea {
				width: 100%;
				padding: 28rpx 24rpx;
				background: #F8F8F8;
				border-radius: 12rpx;
				font-size: 28rpx;
				color: #333;
				border: 1px solid transparent;
				box-sizing: border-box;
				min-height: 80rpx;
				
				&:focus {
					border-color: #007AFF;
					background: #fff;
				}
				
				&::placeholder {
					color: #999;
				}
			}
			
			.form-textarea {
				min-height: 120rpx;
				resize: none;
			}
		}
		
		.picker-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 28rpx 24rpx;
			background: #F8F8F8;
			border-radius: 12rpx;
			font-size: 28rpx;
			color: #333;
			min-height: 80rpx;
			
			.arrow {
				color: #999;
				font-size: 20rpx;
			}
		}
		
		.switch-container {
			.picker-item {
				background: #F8F8F8;
			}
		}
	}
}

.btn-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: #fff;
	border-top: 1px solid #f0f0f0;
	
	.btn {
		width: 100%;
		
		&.btn-primary {
			background: #000000;
			color: #ffffff;
			border-radius: 12rpx;
			border: none;
			padding: 32rpx 0;
			font-size: 32rpx;
			font-weight: 600;
			
			&:disabled {
				background: #ccc;
				color: #999;
			}
		}
	}
}
</style> 