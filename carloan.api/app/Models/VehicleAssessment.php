<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleAssessment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'vin',
        'model_name',
        'condition',
        'condition_text',
        'dealer_price',
        'individual_price',
        'dealer_buy_price',
        'individual_low_price',
        'dealer_low_buy_price',
        'dealer_high_sold_price',
        'dealer_low_sold_price',
        'assessment_params',
        'raw_data'
    ];

    protected $casts = [
        'assessment_params' => 'array',
        'raw_data' => 'array'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联渠道商
     */
    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }

    /**
     * 格式化价格显示
     */
    protected $appends = ['formatted_prices'];

    public function getFormattedPricesAttribute()
    {
        return [
            'dealer_price' => number_format($this->dealer_price / 10000, 2),
            'individual_price' => number_format($this->individual_price / 10000, 2),
            'dealer_buy_price' => number_format($this->dealer_buy_price / 10000, 2),
            'individual_low_price' => number_format($this->individual_low_price / 10000, 2),
            'dealer_low_buy_price' => number_format($this->dealer_low_buy_price / 10000, 2),
            'dealer_high_sold_price' => number_format($this->dealer_high_sold_price / 10000, 2),
            'dealer_low_sold_price' => number_format($this->dealer_low_sold_price / 10000, 2),
        ];
    }
}