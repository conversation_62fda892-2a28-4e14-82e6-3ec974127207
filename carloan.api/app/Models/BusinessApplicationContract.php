<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class BusinessApplicationContract extends BaseModel
{
    use SoftDeletes;

    protected $table = 'business_application_contracts';

    protected $fillable = [
        'application_id',
        'esign_flow_id',
        'esign_account_id',
        'esign_file_id',
        'sign_url',
        'contract_type',
        'contract_title',
        'contract_status',
        'generated_at',
        'sign_started_at',
        'signed_at',
        'expired_at',
        'esign_callback_data',
        'contract_data',
        'remarks'
    ];

    protected $casts = [
        'application_id' => 'integer',
        'esign_callback_data' => 'array',
        'contract_data' => 'array',
        'generated_at' => 'datetime',
        'sign_started_at' => 'datetime',
        'signed_at' => 'datetime',
        'expired_at' => 'datetime'
    ];

    protected $dates = [
        'generated_at',
        'sign_started_at',
        'signed_at',
        'expired_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 合同状态常量
    const STATUS_DRAFT = 'draft';               // 草稿
    const STATUS_GENERATED = 'generated';       // 已生成
    const STATUS_SIGNING = 'signing';           // 签署中
    const STATUS_SIGNED = 'signed';             // 已签署
    const STATUS_FAILED = 'failed';             // 失败
    const STATUS_REVOKED = 'revoked';           // 已撤销

    // 合同类型常量
    const TYPE_STANDARD = 'standard';           // 标准合同
    const TYPE_TABLE = 'table';                 // 表格合同

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'application_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusTexts = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_GENERATED => '已生成',
            self::STATUS_SIGNING => '签署中',
            self::STATUS_SIGNED => '已签署',
            self::STATUS_FAILED => '失败',
            self::STATUS_REVOKED => '已撤销'
        ];

        return $statusTexts[$this->contract_status] ?? '未知状态';
    }

    /**
     * 获取合同类型文本
     */
    public function getTypeTextAttribute()
    {
        $typeTexts = [
            self::TYPE_STANDARD => '标准合同',
            self::TYPE_TABLE => '表格合同'
        ];

        return $typeTexts[$this->contract_type] ?? '未知类型';
    }

    /**
     * 检查是否可以签署
     */
    public function canSign()
    {
        return in_array($this->contract_status, [self::STATUS_GENERATED, self::STATUS_SIGNING]);
    }

    /**
     * 检查签署链接是否过期
     */
    public function isSignUrlExpired()
    {
        return $this->expired_at && $this->expired_at->isPast();
    }

    /**
     * 检查是否已签署
     */
    public function isSigned()
    {
        return $this->contract_status === self::STATUS_SIGNED;
    }

    /**
     * 更新为生成状态
     */
    public function markAsGenerated($flowId, $accountId, $fileId, $signUrl)
    {
        return $this->update([
            'esign_flow_id' => $flowId,
            'esign_account_id' => $accountId,
            'esign_file_id' => $fileId,
            'sign_url' => $signUrl,
            'contract_status' => self::STATUS_GENERATED,
            'generated_at' => now(),
            'expired_at' => now()->addDays(7) // 签署链接7天有效期
        ]);
    }

    /**
     * 更新为签署中状态
     */
    public function markAsSigning()
    {
        return $this->update([
            'contract_status' => self::STATUS_SIGNING,
            'sign_started_at' => now()
        ]);
    }

    /**
     * 更新为已签署状态
     */
    public function markAsSigned()
    {
        return $this->update([
            'contract_status' => self::STATUS_SIGNED,
            'signed_at' => now()
        ]);
    }

    /**
     * 更新为失败状态
     */
    public function markAsFailed($remarks = null)
    {
        return $this->update([
            'contract_status' => self::STATUS_FAILED,
            'remarks' => $remarks
        ]);
    }

    /**
     * 更新为撤销状态
     */
    public function markAsRevoked($remarks = null)
    {
        return $this->update([
            'contract_status' => self::STATUS_REVOKED,
            'remarks' => $remarks,
            'sign_url' => null
        ]);
    }

    /**
     * 刷新签署链接
     */
    public function refreshSignUrl($newSignUrl)
    {
        return $this->update([
            'sign_url' => $newSignUrl,
            'expired_at' => now()->addDays(7)
        ]);
    }

    /**
     * 作用域：按申请ID查询
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('application_id', $applicationId);
    }

    /**
     * 作用域：按状态查询
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('contract_status', $status);
    }

    /**
     * 作用域：按合同类型查询
     */
    public function scopeWithType($query, $type)
    {
        return $query->where('contract_type', $type);
    }

    /**
     * 作用域：可签署的合同
     */
    public function scopeCanSign($query)
    {
        return $query->whereIn('contract_status', [self::STATUS_GENERATED, self::STATUS_SIGNING]);
    }

    /**
     * 作用域：已签署的合同
     */
    public function scopeSigned($query)
    {
        return $query->where('contract_status', self::STATUS_SIGNED);
    }
} 