-- 为 business_applications 表添加新车业务字段
-- 执行时间：2024-12-26
-- 说明：增加是否新车业务字段，用于标识业务类型的子分类

-- 1. 添加新车业务字段
ALTER TABLE `business_applications` 
ADD COLUMN `is_new_car_business` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否新车业务:0否,1是' AFTER `business_type`;

-- 2. 添加索引以优化查询性能
ALTER TABLE `business_applications` 
ADD INDEX `idx_is_new_car_business` (`is_new_car_business`);

-- 3. 添加复合索引：业务类型 + 是否新车业务
ALTER TABLE `business_applications` 
ADD INDEX `idx_business_type_new_car` (`business_type`, `is_new_car_business`);

-- 4. 添加复合索引：业务员 + 是否新车业务
ALTER TABLE `business_applications` 
ADD INDEX `idx_user_new_car` (`user_id`, `is_new_car_business`);

-- 5. 添加复合索引：状态 + 是否新车业务
ALTER TABLE `business_applications` 
ADD INDEX `idx_status_new_car` (`status`, `is_new_car_business`);

-- 6. 验证字段是否添加成功
SELECT 
    column_name, 
    data_type, 
    column_default, 
    column_comment,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'business_applications' 
AND column_name = 'is_new_car_business';

-- 7. 验证索引是否创建成功
SHOW INDEX FROM `business_applications` WHERE Column_name = 'is_new_car_business';

-- 8. 统计新车业务数据
SELECT 
    CASE business_type
        WHEN 1 THEN '车抵贷'
        WHEN 2 THEN '以租代购'
        ELSE '未知类型'
    END as business_type_name,
    CASE is_new_car_business
        WHEN 1 THEN '新车业务'
        WHEN 0 THEN '非新车业务'
        ELSE '未知'
    END as new_car_status,
    COUNT(*) as count
FROM `business_applications`
GROUP BY business_type, is_new_car_business
ORDER BY business_type, is_new_car_business;

-- 9. 回滚脚本（备用，慎用）
-- ALTER TABLE `business_applications` DROP INDEX `idx_status_new_car`;
-- ALTER TABLE `business_applications` DROP INDEX `idx_user_new_car`;
-- ALTER TABLE `business_applications` DROP INDEX `idx_business_type_new_car`;
-- ALTER TABLE `business_applications` DROP INDEX `idx_is_new_car_business`;
-- ALTER TABLE `business_applications` DROP COLUMN `is_new_car_business`; 