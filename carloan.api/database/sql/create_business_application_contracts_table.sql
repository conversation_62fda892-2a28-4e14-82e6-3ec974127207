-- 创建业务申请合同表
CREATE TABLE IF NOT EXISTS `business_application_contracts` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `application_id` bigint(20) unsigned NOT NULL COMMENT '业务申请ID',
    
    -- e签宝相关字段
    `esign_flow_id` varchar(255) DEFAULT NULL COMMENT 'e签宝签署流程ID',
    `esign_account_id` varchar(255) DEFAULT NULL COMMENT 'e签宝客户账户ID',
    `esign_file_id` varchar(255) DEFAULT NULL COMMENT 'e签宝合同文件ID',
    `sign_url` text DEFAULT NULL COMMENT '签署链接',
    
    -- 合同信息
    `contract_type` varchar(50) NOT NULL DEFAULT 'standard' COMMENT '合同类型: standard(标准合同), table(表格合同)',
    `contract_title` varchar(255) DEFAULT NULL COMMENT '合同标题',
    `contract_status` varchar(50) NOT NULL DEFAULT 'draft' COMMENT '合同状态: draft(草稿), generated(已生成), signing(签署中), signed(已签署), failed(失败), revoked(已撤销)',
    
    -- 时间字段
    `generated_at` timestamp NULL DEFAULT NULL COMMENT '合同生成时间',
    `sign_started_at` timestamp NULL DEFAULT NULL COMMENT '签署开始时间',
    `signed_at` timestamp NULL DEFAULT NULL COMMENT '签署完成时间',
    `expired_at` timestamp NULL DEFAULT NULL COMMENT '签署链接过期时间',
    
    -- 回调和扩展数据
    `esign_callback_data` json DEFAULT NULL COMMENT 'e签宝回调数据',
    `contract_data` json DEFAULT NULL COMMENT '合同生成数据快照',
    `remarks` text DEFAULT NULL COMMENT '备注',
    
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    
    PRIMARY KEY (`id`),
    KEY `idx_application_id` (`application_id`),
    KEY `idx_esign_flow_id` (`esign_flow_id`),
    KEY `idx_esign_account_id` (`esign_account_id`),
    KEY `idx_contract_type` (`contract_type`),
    KEY `idx_contract_status` (`contract_status`),
    KEY `idx_generated_at` (`generated_at`),
    
    CONSTRAINT `fk_contracts_application_id` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务申请合同表';

-- 如果之前已经将e签宝字段添加到business_applications表，需要移除这些字段
-- 请根据实际情况决定是否执行以下语句

-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `esign_flow_id`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `esign_account_id`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `esign_file_id`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `sign_url`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `contract_type`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `contract_generated_at`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `contract_signed_at`;
-- ALTER TABLE `business_applications` DROP COLUMN IF EXISTS `esign_callback_data`;