<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerContact extends Model
{
    protected $table = 'customer_contacts';

    protected $fillable = [
        'customer_id',
        'business_application_id',
        'name',
        'relationship',
        'phone',
        'id_card',
        'address',
        'work_unit'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'business_application_id' => 'integer'
    ];

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'business_application_id');
    }

    /**
     * 作用域：按客户ID筛选
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * 作用域：按业务申请ID筛选
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('business_application_id', $applicationId);
    }

    /**
     * 作用域：按关系筛选
     */
    public function scopeWithRelationship($query, $relationship)
    {
        return $query->where('relationship', $relationship);
    }
} 