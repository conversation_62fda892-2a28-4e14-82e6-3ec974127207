<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\FinancialProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FinancialProductController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 获取金融产品列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $products = FinancialProduct::where('status', 1)
                ->orderBy('sort_order', 'asc')
                ->orderBy('created_at', 'desc')
                ->get([
                    'id',
                    'product_name as name',
                    'description', 
                    'min_amount',
                    'max_amount',
                    'supported_periods',
                    'annual_rate',
                    'features',
                    'conditions as requirements'
                ]);

            return $this->successHandler('获取成功', 200, $products);

        } catch (\Exception $e) {
            \Log::error('获取金融产品列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorHandler('获取失败，请重试', 500);
        }
    }

    /**
     * 获取金融产品详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $product = FinancialProduct::where('status', 1)
                ->find($id);

            if (!$product) {
                return $this->errorHandler('产品不存在', 404);
            }

            return $this->successHandler('获取成功', 200, $product);

        } catch (\Exception $e) {
            \Log::error('获取金融产品详情失败', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorHandler('获取失败，请重试', 500);
        }
    }

    /**
     * 计算贷款方案
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculate(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|exists:financial_products,id',
                'loan_amount' => 'required|numeric|min:10000|max:10000000',
                'loan_period' => 'required|integer|min:6|max:60',
            ], [
                'product_id.required' => '请选择金融产品',
                'product_id.exists' => '产品不存在',
                'loan_amount.required' => '请输入贷款金额',
                'loan_amount.numeric' => '贷款金额必须为数字',
                'loan_amount.min' => '贷款金额最少1万元',
                'loan_amount.max' => '贷款金额最多1000万元',
                'loan_period.required' => '请输入贷款期限',
                'loan_period.integer' => '贷款期限必须为整数',
                'loan_period.min' => '贷款期限最少6个月',
                'loan_period.max' => '贷款期限最多60个月',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            $productId = $request->input('product_id');
            $loanAmount = $request->input('loan_amount');
            $loanPeriod = $request->input('loan_period');

            // 获取产品信息
            $product = FinancialProduct::find($productId);
            
            // 验证贷款金额和期限是否在产品范围内
            if ($loanAmount < $product->min_amount || $loanAmount > $product->max_amount) {
                return $this->errorHandler("贷款金额应在{$product->min_amount}-{$product->max_amount}元之间", 400);
            }
            
            // 检查期限是否在支持的期数列表中
            $supportedPeriods = json_decode($product->supported_periods, true);
            if (!in_array($loanPeriod, $supportedPeriods)) {
                return $this->errorHandler("贷款期限不在支持的期数范围内", 400);
            }

            // 计算月利率
            $annualRate = $product->annual_rate;
            $monthlyRate = $annualRate / 12;

            // 等额本息计算
            $monthlyPayment = $loanAmount * $monthlyRate * pow(1 + $monthlyRate, $loanPeriod) / 
                            (pow(1 + $monthlyRate, $loanPeriod) - 1);
            
            // 总利息
            $totalInterest = $monthlyPayment * $loanPeriod - $loanAmount;
            
            // 总还款额
            $totalPayment = $loanAmount + $totalInterest;

            return $this->successHandler('计算成功', 200, [
                'product_name' => $product->product_name,
                'loan_amount' => $loanAmount,
                'loan_period' => $loanPeriod,
                'annual_rate' => $annualRate * 100,
                'monthly_rate' => $monthlyRate * 100,
                'monthly_payment' => round($monthlyPayment, 2),
                'total_interest' => round($totalInterest, 2),
                'total_payment' => round($totalPayment, 2),
            ]);

        } catch (\Exception $e) {
            \Log::error('贷款方案计算失败', [
                'request' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorHandler('计算失败，请重试', 500);
        }
    }
} 