<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\BusinessApplication;
use App\Models\InterviewAppointment;

class CreateTestDataForInterviews extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 插入测试业务申请数据（面审相关）
        $applications = [
            [
                'application_no' => 'APP20240316001',
                'user_id' => 1,
                'channel_code' => 'DEFAULT',
                'customer_name' => '张三',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199001011234',
                'product_name' => '24年以租代售计划',
                'loan_amount' => 150000.00,
                'loan_period' => 24,
                'interest_rate' => 0.0650,
                'vehicle_brand' => '丰田',
                'vehicle_model' => '凯美瑞',
                'vehicle_vin' => 'JTDKN3DU5E0123456',
                'vehicle_year' => 2023,
                'vehicle_price' => 200000.00,
                'status' => BusinessApplication::STATUS_INTERVIEW_PENDING,
                'status_text' => '待面审',
                'submit_time' => now()->subDays(2),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(1)
            ],
            [
                'application_no' => 'APP20240316002',
                'user_id' => 1,
                'channel_code' => 'DEFAULT',
                'customer_name' => '李四',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199002021234',
                'product_name' => '36年车贷计划',
                'loan_amount' => 200000.00,
                'loan_period' => 36,
                'interest_rate' => 0.0720,
                'vehicle_brand' => '本田',
                'vehicle_model' => '雅阁',
                'vehicle_vin' => 'JHMCR6F70EC123456',
                'vehicle_year' => 2024,
                'vehicle_price' => 250000.00,
                'status' => BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                'status_text' => '已预约面审',
                'submit_time' => now()->subDays(3),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(1)
            ],
            [
                'application_no' => 'APP20240316003',
                'user_id' => 1,
                'channel_code' => 'DEFAULT',
                'customer_name' => '王五',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199003031234',
                'product_name' => '12年短期贷款',
                'loan_amount' => 80000.00,
                'loan_period' => 12,
                'interest_rate' => 0.0580,
                'vehicle_brand' => '大众',
                'vehicle_model' => '帕萨特',
                'vehicle_vin' => 'WVWZZZ3CZ9E123456',
                'vehicle_year' => 2022,
                'vehicle_price' => 180000.00,
                'status' => BusinessApplication::STATUS_INTERVIEW_PENDING,
                'status_text' => '待面审',
                'submit_time' => now()->subDays(1),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(2)
            ]
        ];

        foreach ($applications as $appData) {
            $app = BusinessApplication::create($appData);
            
            // 为已预约面审的业务创建预约记录
            if ($app->status === BusinessApplication::STATUS_INTERVIEW_SCHEDULED) {
                InterviewAppointment::create([
                    'application_id' => $app->id,
                    'user_id' => $app->user_id,
                    'appointment_no' => 'INT' . date('YmdHis') . rand(1000, 9999),
                    'status' => InterviewAppointment::STATUS_SCHEDULED,
                    'status_text' => '已预约',
                    'appointment_time' => now()->addDays(1)->setTime(14, 30, 0),
                    'appointment_location' => '上海市浦东新区金融中心大厦12楼面审室',
                    'appointment_notes' => '请携带身份证原件及复印件',
                    'created_time' => now()->subDays(2),
                    'scheduled_time' => now()->subDays(1),
                    'operation_log' => [
                        [
                            'action' => 'create',
                            'description' => '创建面审预约',
                            'user_id' => 1,
                            'created_at' => now()->subDays(2)->toDateTimeString()
                        ],
                        [
                            'action' => 'schedule',
                            'description' => '预约面审',
                            'user_id' => 1,
                            'created_at' => now()->subDays(1)->toDateTimeString()
                        ]
                    ],
                    'created_at' => now()->subDays(2),
                    'updated_at' => now()->subDays(1)
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除测试数据
        BusinessApplication::whereIn('application_no', [
            'APP20240316001',
            'APP20240316002', 
            'APP20240316003'
        ])->delete();
        
        InterviewAppointment::whereIn('appointment_no', [
            'INT' . date('YmdHis') . '1001',
            'INT' . date('YmdHis') . '1002',
            'INT' . date('YmdHis') . '1003'
        ])->delete();
    }
} 