<?php

namespace App\Http\Controllers\Api\Admin;

use App\Models\BusinessApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class BusinessApplicationController extends BaseController
{
    /**
     * 获取业务申请列表（管理后台）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('per_page', 20);
            $search = $request->get('search', ''); // 搜索关键词
            $status = $request->get('status', ''); // 状态筛选
            $startDate = $request->get('start_date', ''); // 开始日期
            $endDate = $request->get('end_date', ''); // 结束日期
            $userId = $request->get('user_id', ''); // 业务员筛选
            $channelCode = $request->get('channel_code', ''); // 渠道筛选
            $businessType = $request->get('business_type', ''); // 业务类型筛选
            $isNewCarBusiness = $request->get('is_new_car_business', ''); // 是否新车业务筛选

            // 构建查询
            $query = BusinessApplication::with(['user', 'channel'])
                ->orderBy('submit_time', 'desc');

            // 搜索条件：客户手机、客户姓名、申请单号
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('customer_phone', 'like', "%{$search}%")
                      ->orWhere('customer_name', 'like', "%{$search}%")
                      ->orWhere('application_no', 'like', "%{$search}%");
                });
            }

            // 状态筛选
            if (!empty($status)) {
                if ($status === 'initial_review') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_SUBMITTED,
                        BusinessApplication::STATUS_INITIAL_REVIEW
                    ]);
                } elseif ($status === 'interview') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_INTERVIEW_PENDING,
                        BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                        BusinessApplication::STATUS_INTERVIEW_COMPLETED
                    ]);
                } elseif ($status === 'final_review') {
                    $query->where('status', BusinessApplication::STATUS_FINAL_REVIEW);
                } elseif ($status === 'secondary_review') {
                    $query->where('status', BusinessApplication::STATUS_SECONDARY_REVIEW);
                } elseif ($status === 'completed') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_COMPLETED,
                        BusinessApplication::STATUS_APPROVED
                    ]);
                } elseif ($status === 'rejected') {
                    $query->where('status', BusinessApplication::STATUS_REJECTED);
                } elseif ($status === 'supplement') {
                    $query->where('status', BusinessApplication::STATUS_SUPPLEMENT_REQUIRED);
                } elseif ($status === 'contract') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_PENDING,
                        BusinessApplication::STATUS_CONTRACT_PROCESSING
                    ]);
                } else {
                    $query->where('status', $status);
                }
            }

            // 业务员筛选
            if (!empty($userId)) {
                $query->where('user_id', $userId);
            }

            // 渠道筛选
            if (!empty($channelCode)) {
                $query->where('channel_code', $channelCode);
            }

            // 业务类型筛选
            if (!empty($businessType)) {
                $query->where('business_type', $businessType);
            }

            // 是否新车业务筛选
            if ($isNewCarBusiness !== '') {
                $query->where('is_new_car_business', (bool)$isNewCarBusiness);
            }

            // 日期筛选
            if (!empty($startDate)) {
                $query->whereDate('submit_time', '>=', $startDate);
            }
            if (!empty($endDate)) {
                $query->whereDate('submit_time', '<=', $endDate);
            }

            // 分页查询
            $applications = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $list = $applications->getCollection()->map(function ($application) {
                return $this->formatApplicationData($application);
            });

            // 获取各状态统计
            $statistics = $this->getStatusStatistics();

            return response()->json([
                'status_code' => 200,
                'message' => '获取业务申请列表成功',
                'data' => $list,
                'meta' => [
                    'pagination' => [
                        'current_page' => $applications->currentPage(),
                        'last_page' => $applications->lastPage(),
                        'per_page' => $applications->perPage(),
                        'total' => $applications->total(),
                        'total_pages' => $applications->lastPage()
                    ],
                    'statistics' => $statistics
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('获取业务申请列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status_code' => 500,
                'message' => '获取业务申请列表失败'
            ], 500);
        }
    }

    /**
     * 获取业务申请详情（管理后台）
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $application = BusinessApplication::with(['user', 'channel'])
                ->findOrFail($id);

            $data = $this->formatApplicationDetail($application);

            return response()->json([
                'status_code' => 200,
                'message' => '获取业务申请详情成功',
                'data' => $data
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'status_code' => 404,
                'message' => '业务申请不存在'
            ], 404);
        } catch (\Exception $e) {
            \Log::error('获取业务申请详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status_code' => 500,
                'message' => '获取业务申请详情失败'
            ], 500);
        }
    }

    /**
     * 格式化业务申请数据
     * 
     * @param BusinessApplication $application
     * @return array
     */
    private function formatApplicationData(BusinessApplication $application)
    {
        return [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'business_type' => $application->business_type,
            'business_type_text' => $application->getBusinessTypeTextAttribute(),
            'is_new_car_business' => $application->is_new_car_business,
            'new_car_business_text' => $application->getNewCarBusinessTextAttribute(),
            'customer_name' => $application->customer_name,
            'customer_phone' => $application->customer_phone,
            'customer_id_card' => $application->customer_id_card ? 
                substr($application->customer_id_card, 0, 6) . '******' . substr($application->customer_id_card, -4) : '',
            'product_name' => $application->product_name,
            'loan_amount' => $application->loan_amount,
            'loan_period' => $application->loan_period,
            'interest_rate' => $application->interest_rate,
            'vehicle_brand' => $application->vehicle_brand,
            'vehicle_model' => $application->vehicle_model,
            'vehicle_price' => $application->vehicle_price,
            'status' => $application->status,
            'status_text' => $application->status_text,
            'need_supplement' => $application->need_supplement,
            'supplement_reason' => $application->supplement_reason,
            'submit_time' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : null,
            'approval_time' => $application->approval_time ? $application->approval_time->format('Y-m-d H:i:s') : null,
            'created_at' => $application->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $application->updated_at->format('Y-m-d H:i:s'),
            'user' => [
                'id' => $application->user ? $application->user->id : null,
                'name' => $application->user ? $application->user->name : '',
                'phone' => $application->user ? $application->user->phone : '',
            ],
            'channel' => [
                'code' => $application->channel_code,
                'name' => $application->channel ? $application->channel->name : '',
            ]
        ];
    }

    /**
     * 格式化业务申请详情数据
     * 
     * @param BusinessApplication $application
     * @return array
     */
    private function formatApplicationDetail(BusinessApplication $application)
    {
        $data = $this->formatApplicationData($application);
        
        // 添加详细信息
        $data['customer_data'] = $application->customer_data;
        $data['risk_assessment'] = $application->risk_assessment;
        $data['attachments'] = $application->attachments;
        $data['approval_notes'] = $application->approval_notes;
        $data['approval_history'] = $application->approval_history;
        $data['supplement_deadline'] = $application->supplement_deadline ? 
            $application->supplement_deadline->format('Y-m-d H:i:s') : null;

        return $data;
    }

    /**
     * 获取各状态统计数据
     * 
     * @return array
     */
    private function getStatusStatistics()
    {
        try {
            $total = BusinessApplication::count();
            
            $statistics = [
                'total' => $total,
                'submitted' => BusinessApplication::where('status', BusinessApplication::STATUS_SUBMITTED)->count(),
                'initial_review' => BusinessApplication::whereIn('status', [
                    BusinessApplication::STATUS_SUBMITTED,
                    BusinessApplication::STATUS_INITIAL_REVIEW
                ])->count(),
                'interview' => BusinessApplication::whereIn('status', [
                    BusinessApplication::STATUS_INTERVIEW_PENDING,
                    BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                    BusinessApplication::STATUS_INTERVIEW_COMPLETED
                ])->count(),
                'final_review' => BusinessApplication::where('status', BusinessApplication::STATUS_FINAL_REVIEW)->count(),
                'secondary_review' => BusinessApplication::where('status', BusinessApplication::STATUS_SECONDARY_REVIEW)->count(),
                'contract' => BusinessApplication::whereIn('status', [
                    BusinessApplication::STATUS_CONTRACT_PENDING,
                    BusinessApplication::STATUS_CONTRACT_PROCESSING
                ])->count(),
                'completed' => BusinessApplication::whereIn('status', [
                    BusinessApplication::STATUS_CONTRACT_COMPLETED,
                    BusinessApplication::STATUS_APPROVED
                ])->count(),
                'rejected' => BusinessApplication::where('status', BusinessApplication::STATUS_REJECTED)->count(),
                'supplement' => BusinessApplication::where('status', BusinessApplication::STATUS_SUPPLEMENT_REQUIRED)->count(),
                
                // 按业务类型统计
                'business_types' => [
                    'car_loan' => BusinessApplication::where('business_type', BusinessApplication::BUSINESS_TYPE_CAR_LOAN)->count(),
                    'lease_to_own' => BusinessApplication::where('business_type', BusinessApplication::BUSINESS_TYPE_LEASE_TO_OWN)->count(),
                ],
                
                // 按新车业务统计
                'new_car_business' => [
                    'new_car' => BusinessApplication::where('is_new_car_business', true)->count(),
                    'not_new_car' => BusinessApplication::where('is_new_car_business', false)->count(),
                ]
            ];

            return $statistics;

        } catch (\Exception $e) {
            \Log::error('获取状态统计失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 导出业务申请列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            // 获取查询参数
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $businessType = $request->get('business_type', '');
            $startDate = $request->get('start_date', '');
            $endDate = $request->get('end_date', '');
            $userId = $request->get('user_id', '');
            $channelCode = $request->get('channel_code', '');

            // 构建查询 - 复用列表查询逻辑
            $query = BusinessApplication::with(['user', 'channel'])
                ->orderBy('submit_time', 'desc');

            // 应用相同的筛选条件
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('customer_phone', 'like', "%{$search}%")
                      ->orWhere('customer_name', 'like', "%{$search}%")
                      ->orWhere('application_no', 'like', "%{$search}%");
                });
            }

            if (!empty($businessType)) {
                $query->where('business_type', $businessType);
            }

            if (!empty($status)) {
                if ($status === 'initial_review') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_SUBMITTED,
                        BusinessApplication::STATUS_INITIAL_REVIEW
                    ]);
                } elseif ($status === 'interview') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_INTERVIEW_PENDING,
                        BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                        BusinessApplication::STATUS_INTERVIEW_COMPLETED
                    ]);
                } elseif ($status === 'final_review') {
                    $query->where('status', BusinessApplication::STATUS_FINAL_REVIEW);
                } elseif ($status === 'secondary_review') {
                    $query->where('status', BusinessApplication::STATUS_SECONDARY_REVIEW);
                } elseif ($status === 'completed') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_COMPLETED,
                        BusinessApplication::STATUS_APPROVED
                    ]);
                } elseif ($status === 'rejected') {
                    $query->where('status', BusinessApplication::STATUS_REJECTED);
                } elseif ($status === 'supplement') {
                    $query->where('status', BusinessApplication::STATUS_SUPPLEMENT_REQUIRED);
                } elseif ($status === 'contract') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_PENDING,
                        BusinessApplication::STATUS_CONTRACT_PROCESSING
                    ]);
                } else {
                    $query->where('status', $status);
                }
            }

            if (!empty($userId)) {
                $query->where('user_id', $userId);
            }

            if (!empty($channelCode)) {
                $query->where('channel_code', $channelCode);
            }

            if (!empty($startDate)) {
                $query->whereDate('submit_time', '>=', $startDate);
            }
            if (!empty($endDate)) {
                $query->whereDate('submit_time', '<=', $endDate);
            }

            // 获取数据（限制最大导出数量）
            $applications = $query->limit(5000)->get();

            // 格式化导出数据
            $exportData = $applications->map(function ($application) {
                return [
                    '申请单号' => $application->application_no,
                    '业务类型' => $application->getBusinessTypeTextAttribute(),
                    '客户姓名' => $application->customer_name,
                    '客户手机号' => $application->customer_phone,
                    '客户身份证号' => $application->customer_id_card,
                    '产品名称' => $application->product_name,
                    '贷款金额' => $application->loan_amount,
                    '贷款期限' => $application->loan_period . '个月',
                    '利率' => $application->interest_rate,
                    '车辆品牌' => $application->vehicle_brand,
                    '车辆型号' => $application->vehicle_model,
                    '车辆价格' => $application->vehicle_price,
                    '业务状态' => $application->status_text,
                    '提交时间' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : '',
                    '审批时间' => $application->approval_time ? $application->approval_time->format('Y-m-d H:i:s') : '',
                    '业务员' => $application->user ? $application->user->name : '',
                    '渠道' => $application->channel ? $application->channel->name : '',
                    '创建时间' => $application->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'status_code' => 200,
                'message' => '导出数据获取成功',
                'data' => [
                    'list' => $exportData,
                    'total' => $exportData->count(),
                    'export_time' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('导出业务申请失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status_code' => 500,
                'message' => '导出失败'
            ], 500);
        }
    }
} 