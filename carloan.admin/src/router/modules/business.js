import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const businessRouter = {
  path: '/business',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Business',
  meta: {
    title: '业务管理',
    icon: 'el-icon-s-order'
  },
  children: [
    {
      path: 'applications',
      component: () => import('@/views/business/ApplicationList'),
      name: 'BusinessApplications',
      meta: { title: '业务申请列表' }
    },
    {
      path: 'approval',
      component: PlaceholderPage,
      name: 'BusinessApproval',
      meta: { title: '业务审批中心' }
    },
    {
      path: 'workflow',
      component: PlaceholderPage,
      name: 'BusinessWorkflow',
      meta: { title: '业务流程配置' }
    },
    {
      path: 'applications/detail/:id',
      component: PlaceholderPage,
      name: 'BusinessApplicationDetail',
      meta: { title: '业务详情', hidden: true }
    }
  ]
}

export default businessRouter
