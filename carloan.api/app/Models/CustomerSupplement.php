<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CustomerSupplement extends BaseModel
{
    use SoftDeletes;

    protected $table = 'customer_supplements';

    protected $fillable = [
        'application_id',
        'user_id',
        'supplement_no',
        'reason',
        'requirements',
        'required_documents',
        'status',
        'status_text',
        'created_time',
        'deadline',
        'submitted_time',
        'reviewed_time',
        'submitted_documents',
        'customer_notes',
        'reviewer_id',
        'review_notes',
        'review_result',
        'operation_log',
        'withdrawn_time',
        'withdraw_reason',
        'withdrawn_by'
    ];

    protected $casts = [
        'required_documents' => 'array',
        'submitted_documents' => 'array',
        'operation_log' => 'array',
        'review_result' => 'boolean',
        'created_time' => 'datetime',
        'deadline' => 'datetime',
        'submitted_time' => 'datetime',
        'reviewed_time' => 'datetime',
        'withdrawn_time' => 'datetime'
    ];

    protected $dates = [
        'created_time',
        'deadline',
        'submitted_time',
        'reviewed_time',
        'withdrawn_time',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 补件状态常量
    const STATUS_PENDING = 'pending';       // 待补件
    const STATUS_SUBMITTED = 'submitted';   // 已提交
    const STATUS_APPROVED = 'approved';     // 已审核通过
    const STATUS_REJECTED = 'rejected';     // 已驳回
    const STATUS_WITHDRAWN = 'withdrawn';   // 已撤销

    /**
     * 关联业务申请
     */
    public function application()
    {
        return $this->belongsTo(BusinessApplication::class, 'application_id');
    }

    /**
     * 关联业务员
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联审核人
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    /**
     * 生成补件单号
     */
    public static function generateSupplementNo()
    {
        return 'SUP' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute($value)
    {
        if ($value) {
            return $value;
        }

        $statusMap = [
            self::STATUS_PENDING => '待补件',
            self::STATUS_SUBMITTED => '已提交',
            self::STATUS_APPROVED => '已审核',
            self::STATUS_REJECTED => '已驳回',
            self::STATUS_WITHDRAWN => '已撤销'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 检查是否逾期
     */
    public function isOverdue()
    {
        if (!$this->deadline) {
            return false;
        }

        return $this->deadline->isPast() && in_array($this->status, [self::STATUS_PENDING]);
    }

    /**
     * 获取剩余时间（天数）
     */
    public function getRemainingDays()
    {
        if (!$this->deadline) {
            return null;
        }

        $days = Carbon::now()->diffInDays($this->deadline, false);
        return $days >= 0 ? $days : 0;
    }

    /**
     * 客户提交补件
     */
    public function submitByCustomer($moduleStatus, $notes = null)
    {
        $this->update([
            'status' => self::STATUS_SUBMITTED,
            'status_text' => '已提交',
            'submitted_time' => Carbon::now(),
            'submitted_documents' => $moduleStatus, // 现在存储模块状态而不是文档
            'customer_notes' => $notes
        ]);

        $this->logOperation('customer_submit', '客户提交补充资料');

        // 更新业务申请状态
        $this->application->update([
            'status' => BusinessApplication::STATUS_INITIAL_REVIEW,
            'status_text' => '初审中'
        ]);
    }

    /**
     * 审核补件
     */
    public function review($result, $notes = null, $reviewerId = null)
    {
        $status = $result ? self::STATUS_APPROVED : self::STATUS_REJECTED;
        $statusText = $result ? '已审核' : '已驳回';

        $this->update([
            'status' => $status,
            'status_text' => $statusText,
            'reviewed_time' => Carbon::now(),
            'review_result' => $result,
            'review_notes' => $notes,
            'reviewer_id' => $reviewerId
        ]);

        $this->logOperation('review', $result ? '补件审核通过' : '补件审核驳回');

        // 更新业务申请状态
        if ($result) {
            $this->application->update([
                'need_supplement' => false,
                'status' => BusinessApplication::STATUS_PRE_APPROVAL,
                'status_text' => '预审通过'
            ]);
        } else {
            $this->application->update([
                'status' => BusinessApplication::STATUS_SUPPLEMENT_REQUIRED,
                'status_text' => '需要补件'
            ]);
        }
    }

    /**
     * 撤销补件
     */
    public function withdraw($reason = null, $withdrawnBy = null)
    {
        $this->update([
            'status' => self::STATUS_WITHDRAWN,
            'status_text' => '已撤销',
            'withdrawn_time' => Carbon::now(),
            'withdraw_reason' => $reason,
            'withdrawn_by' => $withdrawnBy ?: Auth::id()
        ]);

        $this->logOperation('withdraw', $reason ?: '撤销补件');

        // 更新业务申请状态
        $this->application->update([
            'need_supplement' => false,
            'status' => BusinessApplication::STATUS_INITIAL_REVIEW,
            'status_text' => '初审中'
        ]);
    }

    /**
     * 记录操作日志
     */
    public function logOperation($action, $description, $data = [])
    {
        $log = $this->operation_log ?: [];
        $log[] = [
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'user_id' => Auth::id(),
            'created_at' => Carbon::now()->toDateTimeString()
        ];

        $this->update(['operation_log' => $log]);
    }

    /**
     * 范围查询：待补件
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 范围查询：已提交
     */
    public function scopeSubmitted($query)
    {
        return $query->where('status', self::STATUS_SUBMITTED);
    }

    /**
     * 范围查询：已驳回
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * 范围查询：已撤销
     */
    public function scopeWithdrawn($query)
    {
        return $query->where('status', self::STATUS_WITHDRAWN);
    }

    /**
     * 范围查询：逾期的
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', self::STATUS_PENDING)
            ->where('deadline', '<', Carbon::now());
    }

    /**
     * 范围查询：指定用户的补件
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：即将到期的（3天内）
     */
    public function scopeExpiringSoon($query)
    {
        return $query->where('status', self::STATUS_PENDING)
            ->whereBetween('deadline', [Carbon::now(), Carbon::now()->addDays(3)]);
    }
} 