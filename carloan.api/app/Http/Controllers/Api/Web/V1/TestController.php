<?php

namespace App\Http\Controllers\Api\Web\V1;

use Illuminate\Http\Request;

class TestController extends BaseController
{
    /**
     * 测试接口
     */
    public function test()
    {
        return $this->successHandler('测试成功', 200, [
            'message' => 'Test controller is working!',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 测试统计接口
     */
    public function statistics()
    {
        return $this->successHandler('测试统计接口成功', 200, [
            'test_count' => 1,
            'status' => 'ok'
        ]);
    }
} 