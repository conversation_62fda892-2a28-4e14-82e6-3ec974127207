<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateBusinessApplicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('business_applications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('application_no', 32)->unique()->comment('申请单号');
            
            // 关联信息
            $table->unsignedBigInteger('user_id')->index()->comment('业务员ID');
            $table->string('channel_code', 20)->default('')->index()->comment('渠道码');
            
            // 客户信息
            $table->string('customer_name', 50)->default('')->comment('客户姓名');
            $table->string('customer_phone', 20)->default('')->index()->comment('客户手机号');
            $table->string('customer_id_card', 18)->default('')->index()->comment('客户身份证号');
            
            // 产品信息
            $table->string('product_name', 100)->default('')->comment('产品名称');
            $table->decimal('loan_amount', 12, 2)->default(0)->comment('贷款金额');
            $table->integer('loan_period')->default(0)->comment('贷款期限(月)');
            $table->decimal('interest_rate', 5, 4)->default(0)->comment('利率');
            
            // 车辆信息
            $table->string('vehicle_brand', 50)->default('')->comment('车辆品牌');
            $table->string('vehicle_model', 100)->default('')->comment('车辆型号');
            $table->string('vehicle_vin', 50)->default('')->comment('车架号');
            $table->year('vehicle_year')->nullable()->comment('车辆年份');
            $table->decimal('vehicle_price', 12, 2)->default(0)->comment('车辆价格');
            
            // 业务状态
            $table->string('status', 30)->default('submitted')->index()->comment('业务状态');
            $table->string('status_text', 50)->default('已提交')->comment('状态文本');
            
            // 审批信息
            $table->text('approval_notes')->nullable()->comment('审批备注');
            $table->json('approval_history')->nullable()->comment('审批历史');
            $table->timestamp('submit_time')->nullable()->comment('提交时间');
            $table->timestamp('approval_time')->nullable()->comment('审批时间');
            
            // 补件相关
            $table->tinyInteger('need_supplement')->default(0)->comment('是否需要补件:0否,1是');
            $table->text('supplement_reason')->nullable()->comment('补件原因');
            $table->timestamp('supplement_deadline')->nullable()->comment('补件截止时间');
            
            // 附加信息
            $table->json('customer_data')->nullable()->comment('客户详细数据');
            $table->json('risk_assessment')->nullable()->comment('风险评估结果');
            $table->json('attachments')->nullable()->comment('附件列表');
            
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['status', 'user_id']);
            $table->index(['customer_phone', 'customer_id_card']);
            $table->index('submit_time');
            $table->index('need_supplement');
        });

        DB::statement("ALTER TABLE `business_applications` comment '业务申请表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('business_applications');
    }
} 