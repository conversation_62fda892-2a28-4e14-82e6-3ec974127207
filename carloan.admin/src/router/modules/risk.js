import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const riskRouter = {
  path: '/risk',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Risk',
  meta: {
    title: '风控管理',
    icon: 'el-icon-lock'
  },
  children: [
    {
      path: 'rules',
      component: PlaceholderPage,
      name: 'RiskRules',
      meta: { title: '风控规则' }
    },
    {
      path: 'blacklist',
      component: PlaceholderPage,
      name: 'RiskBlacklist',
      meta: { title: '黑名单管理' }
    },
    {
      path: 'datasource',
      component: PlaceholderPage,
      name: 'RiskDatasource',
      meta: { title: '数据源管理' }
    },
    {
      path: 'assessment',
      component: PlaceholderPage,
      name: 'RiskAssessment',
      meta: { title: '风险评估' }
    },
    {
      path: 'rules/create',
      component: PlaceholderPage,
      name: 'RiskRuleCreate',
      meta: { title: '新增规则', hidden: true }
    },
    {
      path: 'rules/edit/:id',
      component: PlaceholderPage,
      name: 'RiskRuleEdit',
      meta: { title: '编辑规则', hidden: true }
    }
  ]
}

export default riskRouter
