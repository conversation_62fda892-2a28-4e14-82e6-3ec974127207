<template>
  <div class="permission-codes">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="w200 mr10 mb10"
        placeholder="权限码/名称/描述"
        clearable
      />
      <el-select
        v-model="listQuery.group_name"
        class="w200 mr10 mb10"
        placeholder="权限组"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option
          v-for="group in permissionGroups"
          :key="group"
          :label="group"
          :value="group"
        />
      </el-select>
      <el-select
        v-model="listQuery.status"
        class="w150 mr10 mb10"
        placeholder="状态"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option label="启用" value="1" />
        <el-option label="禁用" value="0" />
      </el-select>
      <el-button
        class="filter-item mb10 mr10"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item mb10 mr10"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增权限码
      </el-button>
      <el-button
        class="filter-item mb10"
        type="success"
        icon="el-icon-upload2"
        @click="handleBatchImport"
      >
        批量导入
      </el-button>
    </div>

    <el-table
      :key="'permission-codes'"
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="权限码" prop="code" align="center" min-width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="权限名称" prop="name" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="权限组" prop="group_name" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.group_name" size="small">
            {{ scope.row.group_name }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="描述" prop="description" align="center" min-width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.description || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="前端路由" prop="frontend_route" align="center" min-width="150">
        <template slot-scope="scope">
          <code v-if="scope.row.frontend_route" style="font-size: 12px;">
            {{ scope.row.frontend_route }}
          </code>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="180"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <!-- 权限码表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="100px"
        style="padding: 0 10px;"
      >
        <el-form-item label="权限码" prop="code">
          <el-input v-model="temp.code" placeholder="请输入权限码" />
          <div class="form-tip">权限码格式: module.action 或 module.* (如: user.view, user.*)</div>
        </el-form-item>

        <el-form-item label="权限名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入权限名称" />
        </el-form-item>

        <el-form-item label="权限组" prop="group_name">
          <el-select
            v-model="temp.group_name"
            placeholder="请选择权限组"
            allow-create
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="group in permissionGroups"
              :key="group"
              :label="group"
              :value="group"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>

        <el-form-item label="前端路由" prop="frontend_route">
          <el-input v-model="temp.frontend_route" placeholder="请输入前端路由 (可选)" />
          <div class="form-tip">前端路由格式: /path/to/page 或 /path/*</div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      title="批量导入权限码"
      :visible.sync="importDialogVisible"
      width="800px"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 15px;"
        >
          <template slot="default">
            <p>1. 请按照以下JSON格式准备数据：</p>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">
[
  {
    "code": "user.view",
    "name": "查看用户",
    "group_name": "用户管理",
    "description": "查看用户列表和详情",
    "frontend_route": "/user"
  }
]</pre>
            <p>2. 重复的权限码将被跳过</p>
          </template>
        </el-alert>
      </div>

      <el-form label-width="100px">
        <el-form-item label="导入数据">
          <el-input
            v-model="importData"
            type="textarea"
            :rows="10"
            placeholder="请粘贴JSON格式的权限码数据"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmBatchImport">
          导入
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchPermissionCodeList,
  createPermissionCode,
  updatePermissionCode,
  deletePermissionCode,
  fetchPermissionGroups,
  togglePermissionCodeStatus,
  batchImportPermissionCodes
} from '@/api/permission'
import Pagination from '@/components/Pagination'

export default {
  name: 'PermissionCodes',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      loading: true,
      listQuery: {
        page: 1,
        page_size: 20,
        keyword: '',
        group_name: '',
        status: ''
      },
      permissionGroups: [],
      dialogVisible: false,
      dialogStatus: '',
      dialogTitle: '',
      temp: {
        id: undefined,
        code: '',
        name: '',
        group_name: '',
        description: '',
        frontend_route: '',
        status: 1
      },
      rules: {
        code: [
          { required: true, message: '权限码不能为空', trigger: 'blur' },
          { min: 2, max: 100, message: '权限码长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '权限名称不能为空', trigger: 'blur' },
          { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        group_name: [
          { max: 50, message: '权限组长度不能超过 50 个字符', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
        ],
        frontend_route: [
          { max: 100, message: '前端路由长度不能超过 100 个字符', trigger: 'blur' }
        ]
      },
      importDialogVisible: false,
      importData: ''
    }
  },
  created() {
    this.getList()
    this.getPermissionGroups()
  },
  methods: {
    getList() {
      this.loading = true
      fetchPermissionCodeList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    getPermissionGroups() {
      fetchPermissionGroups().then(response => {
        this.permissionGroups = response.data
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        code: '',
        name: '',
        group_name: '',
        description: '',
        frontend_route: '',
        status: 1
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogTitle = '创建权限码'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createPermissionCode(this.temp).then(response => {
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            // 重新获取列表以确保数据完整性
            this.getList()
            this.getPermissionGroups()
          })
        }
      })
    },
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogTitle = '编辑权限码'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updatePermissionCode(tempData.id, tempData).then((response) => {
            // 使用API返回的数据更新列表项，或者重新获取列表
            if (response.data) {
              const index = this.list.findIndex(v => v.id === this.temp.id)
              this.list.splice(index, 1, response.data)
            } else {
              // 如果API没有返回数据，重新获取列表
              this.getList()
            }
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
            this.getPermissionGroups()
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该权限码, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePermissionCode(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleStatusChange(row) {
      togglePermissionCodeStatus(row.id, row.status).then(() => {
        this.$notify({
          title: '成功',
          message: row.status ? '启用成功' : '禁用成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        // 恢复状态
        row.status = row.status === 1 ? 0 : 1
      })
    },
    handleBatchImport() {
      this.importData = ''
      this.importDialogVisible = true
    },
    confirmBatchImport() {
      if (!this.importData.trim()) {
        this.$message.error('请输入要导入的数据')
        return
      }

      try {
        const data = JSON.parse(this.importData)
        if (!Array.isArray(data)) {
          this.$message.error('数据格式错误，请输入JSON数组')
          return
        }

        batchImportPermissionCodes({ permission_codes: data }).then(response => {
          this.importDialogVisible = false
          this.$notify({
            title: '导入完成',
            message: response.message,
            type: 'success',
            duration: 3000
          })
          if (response.data.errors && response.data.errors.length > 0) {
            this.$alert(response.data.errors.join('\n'), '导入错误详情', {
              confirmButtonText: '确定',
              type: 'warning'
            })
          }
          this.getList()
          this.getPermissionGroups()
        })
      } catch (e) {
        this.$message.error('JSON格式错误，请检查数据格式')
      }
    },
    resetForm() {
      this.$refs['dataForm'] && this.$refs['dataForm'].resetFields()
    }
  }
}
</script>

<style scoped>
.permission-codes {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
