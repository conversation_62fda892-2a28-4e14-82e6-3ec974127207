<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class IdCardOcrResult extends Model
{
    use SoftDeletes;

    protected $table = 'id_card_ocr_results';

    protected $fillable = [
        'risk_query_id',
        'user_id',
        'name',
        'cert_no',
        'gender',
        'nationality',
        'birth_date',
        'address',
        'issue_authority',
        'valid_from',
        'valid_to',
        'front_img',
        'back_img',
        'face_img',
        'status',
        'error_msg'
    ];

    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_FAILED = 0;
    const STATUS_SUCCESS = 1;

    /**
     * 关联风险查询记录
     */
    public function riskQuery()
    {
        return $this->belongsTo(RiskQuery::class, 'risk_query_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status === self::STATUS_SUCCESS ? '成功' : '失败';
    }

    /**
     * 是否成功
     */
    public function getIsSuccessAttribute()
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * 获取完整姓名和身份证信息
     */
    public function getIdentityInfoAttribute()
    {
        return $this->name . '（' . $this->cert_no . '）';
    }

    /**
     * 获取有效期信息
     */
    public function getValidPeriodAttribute()
    {
        if (empty($this->valid_from) || empty($this->valid_to)) {
            return '';
        }
        return $this->valid_from . ' - ' . $this->valid_to;
    }
} 