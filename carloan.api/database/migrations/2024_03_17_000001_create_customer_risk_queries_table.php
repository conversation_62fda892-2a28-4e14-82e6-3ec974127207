<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateCustomerRiskQueriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_risk_queries', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // 关联信息
            $table->unsignedBigInteger('user_id')->index()->comment('业务员ID');
            
            // 客户信息
            $table->string('customer_name', 50)->comment('客户姓名');
            $table->string('customer_phone', 20)->index()->comment('客户手机号');
            $table->string('customer_id_card', 18)->index()->comment('客户身份证号');
            
            // 查询状态
            $table->string('status', 20)->default('querying')->index()->comment('查询状态:querying查询中,completed已完成,failed查询失败');
            
            // 风险评估结果
            $table->integer('risk_score')->nullable()->comment('风险评分(0-100)');
            $table->string('risk_level', 20)->nullable()->comment('风险等级:low低风险,medium中风险,high高风险');
            
            // 查询时间
            $table->timestamp('query_time')->nullable()->comment('查询时间');
            
            // 查询结果详情
            $table->json('query_results')->nullable()->comment('查询结果详情');
            
            // 备注
            $table->text('notes')->nullable()->comment('备注信息');
            
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['customer_phone', 'customer_id_card']);
            $table->index('query_time');
            $table->index('created_at');
        });

        DB::statement("ALTER TABLE `customer_risk_queries` comment '客户风险查询表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_risk_queries');
    }
} 