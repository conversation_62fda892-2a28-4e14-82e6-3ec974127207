<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateCustomerSupplementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_supplements', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // 关联信息
            $table->unsignedBigInteger('application_id')->index()->comment('业务申请ID');
            $table->unsignedBigInteger('user_id')->index()->comment('业务员ID');
            
            // 补件基本信息
            $table->string('supplement_no', 32)->unique()->comment('补件单号');
            $table->text('reason')->comment('补件原因');
            $table->text('requirements')->comment('补件要求');
            $table->json('required_documents')->nullable()->comment('需要补充的文档列表');
            
            // 状态信息
            $table->string('status', 20)->default('pending')->index()->comment('补件状态:pending待补件,submitted已提交,approved已审核,rejected已驳回');
            $table->string('status_text', 50)->default('待补件')->comment('状态文本');
            
            // 时间信息
            $table->timestamp('created_time')->nullable()->comment('创建时间');
            $table->timestamp('deadline')->nullable()->comment('补件截止时间');
            $table->timestamp('submitted_time')->nullable()->comment('客户提交时间');
            $table->timestamp('reviewed_time')->nullable()->comment('审核时间');
            
            // 提交内容
            $table->json('submitted_documents')->nullable()->comment('客户提交的文档');
            $table->text('customer_notes')->nullable()->comment('客户备注');
            
            // 审核信息
            $table->unsignedBigInteger('reviewer_id')->nullable()->comment('审核人ID');
            $table->text('review_notes')->nullable()->comment('审核备注');
            $table->tinyInteger('review_result')->nullable()->comment('审核结果:1通过,0驳回');
            
            // 操作记录
            $table->json('operation_log')->nullable()->comment('操作日志');
            
            $table->timestamps();
            $table->softDeletes();
            
            // 外键约束
            $table->foreign('application_id')
                ->references('id')
                ->on('business_applications')
                ->onDelete('cascade');
                
            // 索引
            $table->index(['status', 'user_id']);
            $table->index('deadline');
            $table->index('submitted_time');
        });

        DB::statement("ALTER TABLE `customer_supplements` comment '客户补件表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_supplements');
    }
} 