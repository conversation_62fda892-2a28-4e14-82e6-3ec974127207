<?php

namespace App\Http\Controllers\Api\Admin;

use App\Models\Role;
use App\Models\PermissionCode;
use App\Models\Admin;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RoleController extends BaseController
{
    /**
     * 获取角色列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Role::query();

        // 按角色类型筛选
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // 按状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }

        // 分页
        $pageSize = $request->get('page_size', 20);
        $roles = $query->orderBy('sort_order', 'asc')
                      ->orderBy('id', 'desc')
                      ->paginate($pageSize);

        // 为每个角色添加用户统计
        foreach ($roles as $role) {
            $role->admin_count = Admin::where('role_id', $role->id)->count();
            $role->user_count = User::where('role_id', $role->id)->count();
            $role->total_count = $role->admin_count + $role->user_count;
        }

        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $roles
        ]);
    }

    /**
     * 获取角色详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $role = Role::findOrFail($id);
        
        // 获取角色的权限码详情
        $permissions = $role->permissions ?? [];
        $permissionDetails = [];
        
        if (!empty($permissions)) {
            if (in_array('*', $permissions)) {
                $permissionDetails = PermissionCode::where('status', 1)->get();
            } else {
                $permissionDetails = PermissionCode::whereIn('code', $permissions)
                                                  ->where('status', 1)
                                                  ->get();
            }
        }

        $role->permission_details = $permissionDetails;
        $role->admin_count = Admin::where('role_id', $role->id)->count();
        $role->user_count = User::where('role_id', $role->id)->count();
        
        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $role
        ]);
    }

    /**
     * 创建角色
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:roles,code',
            'description' => 'nullable|string|max:200',
            'type' => 'required|in:admin,user,finance',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string',
            'status' => 'required|in:0,1',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        // 验证权限码
        if (!empty($request->permissions)) {
            $invalidPermissions = [];
            foreach ($request->permissions as $permission) {
                if ($permission !== '*' && !PermissionCode::where('code', $permission)->where('status', 1)->exists()) {
                    $invalidPermissions[] = $permission;
                }
            }
            if (!empty($invalidPermissions)) {
                return $this->response->array([
                    'code' => -1,
                    'message' => '无效的权限码: ' . implode(', ', $invalidPermissions)
                ]);
            }
        }

        $role = Role::create([
            'name' => $request->name,
            'code' => $request->code,
            'description' => $request->description,
            'type' => $request->type,
            'permissions' => $request->permissions ?? [],
            'status' => $request->status,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return $this->response->array([
            'code' => 0,
            'message' => '角色创建成功',
            'data' => $role
        ]);
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $role = Role::findOrFail($id);

        // 不能修改超级管理员角色
        if ($role->code === 'super_admin') {
            return $this->response->array([
                'code' => -1,
                'message' => '不能修改超级管理员角色'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:roles,code,' . $id,
            'description' => 'nullable|string|max:200',
            'type' => 'required|in:admin,user,finance',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string',
            'status' => 'required|in:0,1',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        // 验证权限码
        if (!empty($request->permissions)) {
            $invalidPermissions = [];
            foreach ($request->permissions as $permission) {
                if ($permission !== '*' && !PermissionCode::where('code', $permission)->where('status', 1)->exists()) {
                    $invalidPermissions[] = $permission;
                }
            }
            if (!empty($invalidPermissions)) {
                return $this->response->array([
                    'code' => -1,
                    'message' => '无效的权限码: ' . implode(', ', $invalidPermissions)
                ]);
            }
        }

        $role->update([
            'name' => $request->name,
            'code' => $request->code,
            'description' => $request->description,
            'type' => $request->type,
            'permissions' => $request->permissions ?? [],
            'status' => $request->status,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return $this->response->array([
            'code' => 0,
            'message' => '角色更新成功',
            'data' => $role
        ]);
    }

    /**
     * 删除角色
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $role = Role::findOrFail($id);

        // 不能删除超级管理员角色
        if ($role->code === 'super_admin') {
            return $this->response->array([
                'code' => -1,
                'message' => '不能删除超级管理员角色'
            ]);
        }

        // 检查是否有用户使用该角色
        $adminCount = Admin::where('role_id', $role->id)->count();
        $userCount = User::where('role_id', $role->id)->count();
        
        if ($adminCount > 0 || $userCount > 0) {
            return $this->response->array([
                'code' => -1,
                'message' => '该角色正在被使用，无法删除'
            ]);
        }

        $role->delete();

        return $this->response->array([
            'code' => 0,
            'message' => '角色删除成功'
        ]);
    }

    /**
     * 获取权限码列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function permissions()
    {
        $permissions = PermissionCode::where('status', 1)
                                   ->orderBy('group_name', 'asc')
                                   ->orderBy('code', 'asc')
                                   ->get();

        // 按组分类
        $groupedPermissions = [];
        foreach ($permissions as $permission) {
            $group = $permission->group_name ?? '其他';
            if (!isset($groupedPermissions[$group])) {
                $groupedPermissions[$group] = [];
            }
            $groupedPermissions[$group][] = $permission;
        }

        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'permissions' => $permissions,
                'grouped_permissions' => $groupedPermissions
            ]
        ]);
    }

    /**
     * 获取角色类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {
        $types = [
            ['value' => 'admin', 'label' => '管理员'],
            ['value' => 'user', 'label' => '业务员'], 
            ['value' => 'finance', 'label' => '财务']
        ];

        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $types
        ]);
    }

    /**
     * 启用/禁用角色
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatus(Request $request, $id)
    {
        $role = Role::findOrFail($id);

        // 不能禁用超级管理员角色
        if ($role->code === 'super_admin' && $request->status == 0) {
            return $this->response->array([
                'code' => -1,
                'message' => '不能禁用超级管理员角色'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $role->update(['status' => $request->status]);

        $statusText = $request->status ? '启用' : '禁用';
        return $this->response->array([
            'code' => 0,
            'message' => "角色{$statusText}成功",
            'data' => $role
        ]);
    }

    /**
     * 复制角色
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function copy(Request $request, $id)
    {
        $originalRole = Role::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:roles,code'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $newRole = Role::create([
            'name' => $request->name,
            'code' => $request->code,
            'description' => $originalRole->description,
            'type' => $originalRole->type,
            'permissions' => $originalRole->permissions,
            'status' => 1,
            'sort_order' => $originalRole->sort_order
        ]);

        return $this->response->array([
            'code' => 0,
            'message' => '角色复制成功',
            'data' => $newRole
        ]);
    }
} 