<template>
	<!-- 预约面审 -->
	<view class="interview-schedule">
		<navbar :title="pageTitle" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 表单内容 -->
		<view class="content" v-else-if="detail">
			<!-- 申请信息 -->
			<view class="section">
				<view class="section-title">申请信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">业务单号：</view>
						<view class="info-value">{{ detail.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">客户姓名：</view>
						<view class="info-value">{{ detail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">产品名称：</view>
						<view class="info-value">{{ detail.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">申请金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_amount) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 预约表单 -->
			<view class="section">
				<view class="section-title">预约信息</view>
				<view class="section-content">
					<!-- 面审时间 -->
					<view class="form-item">
						<view class="form-label">
							<text class="required">*</text>
							面审时间：
						</view>
						<view class="form-control">
							<picker 
								mode="multiSelector" 
								:value="dateTimeIndex" 
								:range="dateTimeRange"
								@change="onDateTimeChange"
								@columnchange="onDateTimeColumnChange"
							>
								<view class="picker-input" :class="{ placeholder: !formData.appointment_time }">
									{{ formData.appointment_time || '请选择面审时间' }}
								</view>
							</picker>
						</view>
					</view>
					
					<!-- 面审地点 -->
					<view class="form-item">
						<view class="form-label">
							<text class="required">*</text>
							面审地点：
						</view>
						<view class="form-control">
							<picker 
								mode="selector" 
								:value="locationIndex" 
								:range="locationOptions"
								range-key="label"
								@change="onLocationChange"
							>
								<view class="picker-input" :class="{ placeholder: !formData.appointment_location }">
									{{ formData.appointment_location || '请选择面审地点' }}
								</view>
							</picker>
						</view>
					</view>
					
					<!-- 备注信息 -->
					<view class="form-item">
						<view class="form-label">备注信息：</view>
						<view class="form-control">
							<textarea 
								v-model="formData.appointment_notes"
								placeholder="请输入预约备注（选填）"
								maxlength="500"
								:show-confirm-bar="false"
								auto-height
							/>
							<view class="textarea-count">{{ formData.appointment_notes.length }}/500</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 温馨提示 -->
			<view class="section">
				<view class="section-title">温馨提示</view>
				<view class="section-content">
					<view class="tips">
						<view class="tip-item">• 请至少提前1小时预约面审时间</view>
						<view class="tip-item">• 面审当天请准时到达，避免影响审核进度</view>
						<view class="tip-item">• 请携带身份证原件、驾驶证等相关证件</view>
						<view class="tip-item">• 如需调整时间，请及时联系客服</view>
					</view>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button class="btn btn-outline" @click="onCancel">取消</button>
				<button 
					class="btn btn-primary" 
					@click="onSubmit"
					:disabled="!canSubmit"
				>
					{{ isEdit ? '确认修改' : '确认预约' }}
				</button>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else>
			<view class="error-icon">❌</view>
			<view class="error-text">{{ errorMessage || '数据加载失败' }}</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"
import { useInterviewStore } from "@/stores/interview"

// 响应式数据
const loading = ref(true)
const detail = ref(null)
const errorMessage = ref('')
const interviewId = ref('')
const isEdit = ref(false)

// 状态管理
const interviewStore = useInterviewStore()

// 表单数据
const formData = reactive({
	appointment_time: '',
	appointment_location: '',
	appointment_notes: ''
})

// 日期时间选择器数据
const dateTimeIndex = ref([0, 0])
const dateTimeRange = ref([[], []])

// 地点选择器数据
const locationIndex = ref(0)
const locationOptions = ref([
	{ label: '上海市浦东新区金融中心大厦12楼面审室', value: '上海市浦东新区金融中心大厦12楼面审室' },
	{ label: '上海市黄浦区人民广场办事处3楼', value: '上海市黄浦区人民广场办事处3楼' },
	{ label: '上海市徐汇区漕河泾开发区A栋5楼', value: '上海市徐汇区漕河泾开发区A栋5楼' },
	{ label: '上海市杨浦区五角场万达广场6楼', value: '上海市杨浦区五角场万达广场6楼' }
])

// 计算属性
const pageTitle = computed(() => {
	return isEdit.value ? '修改预约' : '预约面审'
})

const canSubmit = computed(() => {
	return formData.appointment_time && formData.appointment_location
})

// 页面加载
onLoad((options) => {
	if (options.id) {
		interviewId.value = options.id
		isEdit.value = options.mode === 'edit'
		initDateTimeRange()
		loadDetail()
	} else {
		errorMessage.value = '参数错误'
		loading.value = false
	}
})

// 初始化日期时间选择器
const initDateTimeRange = () => {
	const dates = []
	const times = []
	
	// 生成未来7天的日期
	for (let i = 1; i <= 7; i++) {
		const date = new Date()
		date.setDate(date.getDate() + i)
		const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
		const dateStr = `${date.getMonth() + 1}月${date.getDate()}日 (周${weekDay})`
		dates.push(dateStr)
	}
	
	// 生成时间段（上午9点到下午5点，每小时一个时间段）
	for (let hour = 9; hour <= 17; hour++) {
		times.push(`${hour.toString().padStart(2, '0')}:00`)
		if (hour < 17) {
			times.push(`${hour.toString().padStart(2, '0')}:30`)
		}
	}
	
	dateTimeRange.value = [dates, times]
}

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		errorMessage.value = ''
		
		const response = await request.get(`/interviews/${interviewId.value}`)
		
		console.log('面审详情API Response:', response)
		
		if (response && response.status_code === 200) {
			detail.value = response.data
			
			// 如果是编辑模式且有预约信息，填充表单
			if (isEdit.value && detail.value.appointment) {
				const appointment = detail.value.appointment
				formData.appointment_location = appointment.appointment_location || ''
				formData.appointment_notes = appointment.appointment_notes || ''
				
				// 设置预约时间
				if (appointment.appointment_time) {
					const appointmentDate = new Date(appointment.appointment_time)
					const dateStr = `${appointmentDate.getMonth() + 1}月${appointmentDate.getDate()}日`
					const timeStr = `${appointmentDate.getHours().toString().padStart(2, '0')}:${appointmentDate.getMinutes().toString().padStart(2, '0')}`
					
					formData.appointment_time = `${dateStr} ${timeStr}`
					
					// 设置选择器索引
					const dateIndex = dateTimeRange.value[0].findIndex(d => d.includes(dateStr))
					const timeIndex = dateTimeRange.value[1].findIndex(t => t === timeStr)
					if (dateIndex >= 0 && timeIndex >= 0) {
						dateTimeIndex.value = [dateIndex, timeIndex]
					}
				}
				
				// 设置地点索引
				const locIndex = locationOptions.value.findIndex(loc => loc.value === formData.appointment_location)
				if (locIndex >= 0) {
					locationIndex.value = locIndex
				}
			}
		} else {
			errorMessage.value = response?.message || '获取数据失败'
		}
	} catch (error) {
		console.error('获取面审详情失败:', error)
		errorMessage.value = '网络错误，请重试'
	} finally {
		loading.value = false
	}
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return Number(amount).toLocaleString()
}

// 日期时间选择器变化
const onDateTimeChange = (e) => {
	const [dateIndex, timeIndex] = e.detail.value
	dateTimeIndex.value = [dateIndex, timeIndex]
	
	const dateStr = dateTimeRange.value[0][dateIndex]
	const timeStr = dateTimeRange.value[1][timeIndex]
	formData.appointment_time = `${dateStr} ${timeStr}`
}

// 日期时间选择器列变化
const onDateTimeColumnChange = (e) => {
	// 可以在这里处理联动逻辑，比如根据日期筛选可用时间
}

// 地点选择器变化
const onLocationChange = (e) => {
	const index = e.detail.value
	locationIndex.value = index
	formData.appointment_location = locationOptions.value[index].value
}

// 取消操作
const onCancel = () => {
	uni.navigateBack()
}

// 提交表单
const onSubmit = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请完善预约信息',
			icon: 'none'
		})
		return
	}
	
	try {
		// 转换时间格式为后端需要的格式
		const appointmentDateTime = convertToDateTime(formData.appointment_time)
		
		uni.showLoading({ title: isEdit.value ? '修改中...' : '预约中...' })
		
		const requestData = {
			appointment_time: appointmentDateTime,
			appointment_location: formData.appointment_location,
			appointment_notes: formData.appointment_notes
		}
		
		const response = await request.post(`/interviews/${interviewId.value}/schedule`, requestData)
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: isEdit.value ? '修改预约成功' : '预约成功',
				icon: 'success'
			})
			
			// 更新 store 状态
			const appointmentData = {
				appointment_no: response.data?.appointment_no || '',
				appointment_time: appointmentDateTime,
				appointment_location: formData.appointment_location,
				appointment_notes: formData.appointment_notes,
				status: 'scheduled',
				status_text: '已预约'
			}
			
			if (isEdit.value) {
				interviewStore.modifyAppointment(interviewId.value, appointmentData)
			} else {
				interviewStore.scheduleAppointment(interviewId.value, appointmentData)
			}
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			const errorMessage = response.message || (isEdit.value ? '修改预约失败' : '预约失败')
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('预约面审失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}

// 转换时间格式
const convertToDateTime = (timeStr) => {
	try {
		// 解析 "3月17日 (周日) 14:30" 格式
		const match = timeStr.match(/(\d+)月(\d+)日.*?(\d{2}):(\d{2})/)
		if (match) {
			const [, month, day, hour, minute] = match
			const now = new Date()
			const year = now.getFullYear()
			const date = new Date(year, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute))
			return date.toISOString().slice(0, 19).replace('T', ' ')
		}
		return null
	} catch (e) {
		console.error('时间格式转换失败:', e)
		return null
	}
}
</script>

<style lang="scss" scoped>
.interview-schedule {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		background: #f8f9fa;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.section-content {
		padding: 30rpx;
	}
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.info-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
		flex-shrink: 0;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		
		&.amount {
			color: #e6a23c;
			font-weight: 500;
		}
	}
}

.form-item {
	margin-bottom: 32rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 16rpx;
		
		.required {
			color: #ff4d4f;
			margin-right: 4rpx;
		}
	}
	
	.form-control {
		position: relative;
		
		.picker-input {
			min-height: 80rpx;
			padding: 20rpx 24rpx;
			background: #f8f9fa;
			border: 1rpx solid #e5e5e5;
			border-radius: 12rpx;
			font-size: 28rpx;
			color: #333;
			display: flex;
			align-items: center;
			
			&.placeholder {
				color: #999;
			}
			
			&:after {
				content: '';
				width: 12rpx;
				height: 12rpx;
				border-right: 2rpx solid #999;
				border-bottom: 2rpx solid #999;
				transform: rotate(45deg);
				position: absolute;
				right: 24rpx;
				top: 50%;
				margin-top: -6rpx;
			}
		}
		
		textarea {
			width: 100%;
			min-height: 120rpx;
			padding: 20rpx 24rpx;
			background: #f8f9fa;
			border: 1rpx solid #e5e5e5;
			border-radius: 12rpx;
			font-size: 28rpx;
			color: #333;
			box-sizing: border-box;
			resize: none;
		}
		
		.textarea-count {
			text-align: right;
			font-size: 24rpx;
			color: #999;
			margin-top: 8rpx;
		}
	}
}

.tips {
	.tip-item {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 12rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

.action-buttons {
	padding: 40rpx 20rpx;
	display: flex;
	gap: 20rpx;
	
	.btn {
		flex: 1;
		height: 88rpx;
		border-radius: 16rpx;
		border: none;
		font-size: 32rpx;
		font-weight: 500;
		
		&.btn-primary {
			background: #007aff;
			color: #fff;
			
			&:disabled {
				background: #ccc;
				color: #999;
			}
		}
		
		&.btn-outline {
			background: #fff;
			color: #666;
			border: 2rpx solid #ddd;
		}
		
		&:active:not(:disabled) {
			opacity: 0.8;
		}
	}
}

.error-state {
	padding: 200rpx 40rpx;
	text-align: center;
	
	.error-icon {
		font-size: 100rpx;
		margin-bottom: 40rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 60rpx;
	}
	
	.btn {
		width: 200rpx;
		height: 80rpx;
		background: #007aff;
		color: #fff;
		border: none;
		border-radius: 12rpx;
		font-size: 28rpx;
	}
}
</style>

<style lang="scss">
page {
	background: #f5f5f5;
}
</style> 