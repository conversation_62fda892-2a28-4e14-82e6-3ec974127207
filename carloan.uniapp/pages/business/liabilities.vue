<template>
	<!-- 负债信息 -->
	<view class="business-liabilities">
		<navbar title="负债信息" />
		<view class="container">
			<view class="page-title">
				<view>负债信息</view>
				<view class="desc">请如实填写您的负债情况</view>
			</view>
			
			<!-- 信用卡负债 -->
			<view class="basic-card">
				<view class="card-info">信用卡负债</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">信用卡数量</view>
						<input 
							class="input"
							type="number"
							placeholder="请输入信用卡数量"
							v-model="formData.credit_card_count"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">信用卡总额度</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入总额度(万元)"
							v-model="formData.credit_card_limit"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">已使用额度</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入已使用额度(万元)"
							v-model="formData.credit_card_used"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">月还款金额</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入月还款金额(元)"
							v-model="formData.credit_card_monthly"
							@input="saveFormData"
						/>
					</view>
				</view>
			</view>

			<!-- 银行贷款 -->
			<view class="basic-card">
				<view class="card-info">银行贷款</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">是否有银行贷款</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getBankLoanLabel(formData.has_bank_loan)"
								readonly
								placeholder="请选择"
								@click="showBankLoanPicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
					<template v-if="formData.has_bank_loan === 1">
						<view class="card-item">
							<view class="label">贷款银行</view>
							<input 
								class="input"
								type="text"
								placeholder="请输入贷款银行"
								v-model="formData.bank_name"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">贷款总额</view>
							<input 
								class="input"
								type="digit"
								placeholder="请输入贷款总额(万元)"
								v-model="formData.bank_loan_amount"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">剩余未还</view>
							<input 
								class="input"
								type="digit"
								placeholder="请输入剩余未还(万元)"
								v-model="formData.bank_loan_remaining"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">月还款金额</view>
							<input 
								class="input"
								type="digit"
								placeholder="请输入月还款金额(元)"
								v-model="formData.bank_loan_monthly"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">贷款用途</view>
							<input 
								class="input"
								type="text"
								placeholder="请输入贷款用途"
								v-model="formData.bank_loan_purpose"
								@input="saveFormData"
							/>
						</view>
					</template>
				</view>
			</view>

			<!-- 其他负债 -->
			<view class="basic-card">
				<view class="card-info">其他负债</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">是否有其他负债</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getOtherDebtLabel(formData.has_other_debt)"
								readonly
								placeholder="请选择"
								@click="showOtherDebtPicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
					<template v-if="formData.has_other_debt === 1">
						<view class="card-item">
							<view class="label">负债类型</view>
							<input 
								class="input"
								type="text"
								placeholder="如：网贷、民间借贷等"
								v-model="formData.other_debt_type"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">负债金额</view>
							<input 
								class="input"
								type="digit"
								placeholder="请输入负债金额(万元)"
								v-model="formData.other_debt_amount"
								@input="saveFormData"
							/>
						</view>
						<view class="card-item">
							<view class="label">月还款金额</view>
							<input 
								class="input"
								type="digit"
								placeholder="请输入月还款金额(元)"
								v-model="formData.other_debt_monthly"
								@input="saveFormData"
							/>
						</view>
					</template>
				</view>
			</view>

			<!-- 负债汇总 -->
			<view class="basic-card">
				<view class="card-info">负债汇总</view>
				<view class="card-container">
					<view class="summary-item">
						<view class="summary-label">总负债金额</view>
						<view class="summary-value">{{ formatAmount(totalDebtAmount) }}万元</view>
					</view>
					<view class="summary-item">
						<view class="summary-label">月还款总额</view>
						<view class="summary-value">{{ formatAmount(totalMonthlyPayment) }}元</view>
					</view>
					<view class="summary-item">
						<view class="summary-label">资产负债率</view>
						<view class="summary-value">{{ debtToAssetRatio }}%</view>
					</view>
				</view>
			</view>

			<view style="height: 120rpx;"></view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-btn">
			<button 
				class="btn btn-primary" 
				:disabled="!isFormValid || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '保存中...' : '保存并继续' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 数据
const formData = ref({
	credit_card_count: '',          // 信用卡数量
	credit_card_limit: '',          // 信用卡总额度
	credit_card_used: '',           // 已使用额度
	credit_card_monthly: '',        // 信用卡月还款

	has_bank_loan: '',              // 是否有银行贷款
	bank_name: '',                  // 贷款银行
	bank_loan_amount: '',           // 贷款总额
	bank_loan_remaining: '',        // 剩余未还
	bank_loan_monthly: '',          // 银行贷款月还款
	bank_loan_purpose: '',          // 贷款用途

	has_other_debt: '',             // 是否有其他负债
	other_debt_type: '',            // 其他负债类型
	other_debt_amount: '',          // 其他负债金额
	other_debt_monthly: '',         // 其他负债月还款
});

// 状态
const submitLoading = ref(false);

// 选项
const bankLoanOptions = [
	{ name: '无银行贷款', value: 0 },
	{ name: '有银行贷款', value: 1 }
];

const otherDebtOptions = [
	{ name: '无其他负债', value: 0 },
	{ name: '有其他负债', value: 1 }
];

// 计算属性
const totalDebtAmount = computed(() => {
	let total = 0;
	
	// 信用卡已使用额度
	if (formData.value.credit_card_used) {
		total += parseFloat(formData.value.credit_card_used) || 0;
	}
	
	// 银行贷款剩余
	if (formData.value.has_bank_loan === 1 && formData.value.bank_loan_remaining) {
		total += parseFloat(formData.value.bank_loan_remaining) || 0;
	}
	
	// 其他负债
	if (formData.value.has_other_debt === 1 && formData.value.other_debt_amount) {
		total += parseFloat(formData.value.other_debt_amount) || 0;
	}
	
	return total;
});

const totalMonthlyPayment = computed(() => {
	let total = 0;
	
	// 信用卡月还款
	if (formData.value.credit_card_monthly) {
		total += parseFloat(formData.value.credit_card_monthly) || 0;
	}
	
	// 银行贷款月还款
	if (formData.value.has_bank_loan === 1 && formData.value.bank_loan_monthly) {
		total += parseFloat(formData.value.bank_loan_monthly) || 0;
	}
	
	// 其他负债月还款
	if (formData.value.has_other_debt === 1 && formData.value.other_debt_monthly) {
		total += parseFloat(formData.value.other_debt_monthly) || 0;
	}
	
	return total;
});

const debtToAssetRatio = computed(() => {
	// 从localStorage获取资产信息
	const businessData = uni.getStorageSync('business_draft_data') || {};
	const assetsData = businessData.assets || {};
	
	// 计算总资产（万元）
	let totalAssets = 0;
	if (assetsData.property_value) totalAssets += parseFloat(assetsData.property_value) || 0;
	if (assetsData.vehicle_value) totalAssets += parseFloat(assetsData.vehicle_value) || 0;
	if (assetsData.other_assets) totalAssets += parseFloat(assetsData.other_assets) || 0;
	
	if (totalAssets === 0) return '0.0';
	
	const ratio = (totalDebtAmount.value / totalAssets) * 100;
	return ratio.toFixed(1);
});

const isFormValid = computed(() => {
	// 基本验证：至少要有负债信息的选择
	return formData.value.has_bank_loan !== '' && formData.value.has_other_debt !== '';
});

// 页面生命周期
onMounted(() => {
	loadFormData();
});

onShow(() => {
	loadFormData();
});

// 方法
const goBack = () => {
	uni.navigateBack();
};

// 加载表单数据
const loadFormData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft_data') || {};
		if (businessData.liabilities) {
			formData.value = { ...formData.value, ...businessData.liabilities };
		}
	} catch (error) {
		console.error('加载负债信息失败:', error);
	}
};

// 保存表单数据
const saveFormData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft_data') || {};
		businessData.liabilities = formData.value;
		uni.setStorageSync('business_draft_data', businessData);
	} catch (error) {
		console.error('保存负债信息失败:', error);
	}
};

// 银行贷款选择器
const showBankLoanPicker = () => {
	const itemList = bankLoanOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = bankLoanOptions[res.tapIndex];
			formData.value.has_bank_loan = selectedOption.value;
			
			// 如果选择无银行贷款，清空相关字段
			if (selectedOption.value === 0) {
				formData.value.bank_name = '';
				formData.value.bank_loan_amount = '';
				formData.value.bank_loan_remaining = '';
				formData.value.bank_loan_monthly = '';
				formData.value.bank_loan_purpose = '';
			}
			
			saveFormData();
		}
	});
};

const getBankLoanLabel = (value) => {
	const option = bankLoanOptions.find(item => item.value === value);
	return option ? option.name : '';
};



// 其他负债选择器
const showOtherDebtPicker = () => {
	const itemList = otherDebtOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = otherDebtOptions[res.tapIndex];
			formData.value.has_other_debt = selectedOption.value;
			
			// 如果选择无其他负债，清空相关字段
			if (selectedOption.value === 0) {
				formData.value.other_debt_type = '';
				formData.value.other_debt_amount = '';
				formData.value.other_debt_monthly = '';
			}
			
			saveFormData();
		}
	});
};

const getOtherDebtLabel = (value) => {
	const option = otherDebtOptions.find(item => item.value === value);
	return option ? option.name : '';
};



// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0.0';
	return parseFloat(amount).toFixed(1);
};

// 提交数据
const handleSubmit = async () => {
	try {
		submitLoading.value = true;
		
		// 获取客户手机号
		const businessData = uni.getStorageSync('business_draft') || {};
		if (!businessData.customer_phone) {
			return uni.showToast({
				title: '请先完成客户验证',
				icon: 'none'
			});
		}
		
		// 调用后端API保存负债信息
		const response = await request.post('/business-applications/save-liability', {
			customer_phone: businessData.customer_phone,
			has_credit_card: formData.value.has_credit_card === 1,
			credit_card_limit: parseFloat(formData.value.credit_limit) || 0,
			credit_card_used: parseFloat(formData.value.credit_used) || 0,
			has_bank_loan: formData.value.has_bank_loan === 1,
			bank_loan_balance: parseFloat(formData.value.bank_loan_amount) || 0,
			bank_loan_monthly: parseFloat(formData.value.bank_loan_monthly) || 0,
			has_other_debt: formData.value.has_other_debt === 1,
			other_debt_balance: parseFloat(formData.value.other_debt_amount) || 0,
			other_debt_monthly: parseFloat(formData.value.other_debt_monthly) || 0
		});

		if (response.status_code === 200) {
			// 保存本地数据
			saveFormData();
			
			// 更新完成状态
			businessData.liabilities_completed = true;
			uni.setStorageSync('business_draft', businessData);

			uni.showToast({
				title: '负债信息保存成功',
				icon: 'success'
			});

			// 跳转到车辆信息页面
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/business/vehicle'
				});
			}, 1000);
		} else {
			uni.showToast({
				title: response.message || '保存失败',
				icon: 'none'
			});
		}
		
	} catch (error) {
		console.error('保存负债信息失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.business-liabilities {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 120rpx;
}

.container {
	padding: 20rpx;
}

.page-title {
	padding: 40rpx 20rpx 20rpx;
	text-align: left;
	
	view:first-child {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 12rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
	}
}

.basic-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 24rpx;
	
	.card-info {
		padding: 32rpx 32rpx 16rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.card-container {
		padding: 32rpx;
		
		.card-item {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				width: 160rpx;
				font-size: 30rpx;
				color: #333;
				flex-shrink: 0;
			}
			
			.input {
				flex: 1;
				height: 64rpx;
				border: 2rpx solid #d0d0d0;
				border-radius: 8rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				color: #333;
				background: #fff;
				
				&.select-input {
					color: #666;
				}
			}
			
			.select-wrapper {
				position: relative;
				flex: 1;
				
				.arrow-icon {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
		
		.summary-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.summary-label {
				font-size: 30rpx;
				color: #666;
			}
			
			.summary-value {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
}


</style> 