<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ApprovalCcRecord extends BaseModel
{
    use SoftDeletes;

    protected $table = 'approval_cc_records';

    protected $fillable = [
        'application_id',
        'task_id',
        'cc_user_id',
        'cc_user_name',
        'cc_type',
        'step_name',
        'step_code',
        'cc_reason',
        'is_read',
        'read_time',
        'cc_time'
    ];

    protected $casts = [
        'application_id' => 'integer',
        'task_id' => 'integer',
        'cc_user_id' => 'integer',
        'is_read' => 'boolean',
        'read_time' => 'datetime',
        'cc_time' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // 抄送类型常量
    const CC_TYPE_WORKFLOW = 'workflow';
    const CC_TYPE_MANUAL = 'manual';

    /**
     * 关联业务申请
     */
    public function application()
    {
        return $this->belongsTo(BusinessApplication::class, 'application_id');
    }

    /**
     * 关联审批任务
     */
    public function task()
    {
        return $this->belongsTo(ApprovalTask::class, 'task_id');
    }

    /**
     * 关联抄送人
     */
    public function ccUser()
    {
        return $this->belongsTo(User::class, 'cc_user_id');
    }

    /**
     * 创建抄送记录
     */
    public static function createCcRecord($applicationId, $ccUserId, $ccUserName, $ccType = self::CC_TYPE_WORKFLOW, $stepName = null, $stepCode = null, $ccReason = null, $taskId = null)
    {
        return static::create([
            'application_id' => $applicationId,
            'task_id' => $taskId,
            'cc_user_id' => $ccUserId,
            'cc_user_name' => $ccUserName,
            'cc_type' => $ccType,
            'step_name' => $stepName,
            'step_code' => $stepCode,
            'cc_reason' => $ccReason,
            'is_read' => false,
            'cc_time' => Carbon::now()
        ]);
    }

    /**
     * 标记为已读
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_time' => Carbon::now()
            ]);
        }

        return $this;
    }

    /**
     * 获取用户的未读抄送数量
     */
    public static function getUnreadCcCount($userId)
    {
        return static::where('cc_user_id', $userId)
            ->where('is_read', false)
            ->count();
    }

    /**
     * 获取用户的抄送记录
     */
    public static function getCcRecords($userId, $limit = 20, $isRead = null)
    {
        $query = static::with(['application', 'task'])
            ->where('cc_user_id', $userId)
            ->orderBy('cc_time', 'desc');

        if ($isRead !== null) {
            $query->where('is_read', $isRead);
        }

        return $query->limit($limit)->get();
    }

    /**
     * 批量标记为已读
     */
    public static function batchMarkAsRead($userId, $recordIds = null)
    {
        $query = static::where('cc_user_id', $userId)
            ->where('is_read', false);

        if ($recordIds) {
            $query->whereIn('id', $recordIds);
        }

        return $query->update([
            'is_read' => true,
            'read_time' => Carbon::now()
        ]);
    }

    /**
     * 作用域：按抄送人筛选
     */
    public function scopeForCcUser($query, $userId)
    {
        return $query->where('cc_user_id', $userId);
    }

    /**
     * 作用域：按已读状态筛选
     */
    public function scopeByReadStatus($query, $isRead)
    {
        return $query->where('is_read', $isRead);
    }

    /**
     * 作用域：未读
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * 作用域：按抄送类型筛选
     */
    public function scopeByCcType($query, $ccType)
    {
        return $query->where('cc_type', $ccType);
    }

    /**
     * 作用域：按步骤筛选
     */
    public function scopeByStep($query, $stepCode)
    {
        return $query->where('step_code', $stepCode);
    }
} 