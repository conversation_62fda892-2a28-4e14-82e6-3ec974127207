<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\BusinessApplication;
use App\Models\ApprovalTask;
use App\Services\ApprovalWorkflowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class FinalReviewController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 获取终审列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 10);

            // 查询终审中和复审中的业务申请
            $query = BusinessApplication::with(['user'])
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_FINAL_REVIEW,
                    BusinessApplication::STATUS_SECONDARY_REVIEW
                ])
                ->orderBy('submit_time', 'desc');

            // 分页查询
            $applications = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $data = [
                'list' => $applications->items() ? array_map(function ($application) {
                    return $this->formatReviewData($application);
                }, $applications->items()) : [],
                'pagination' => [
                    'total' => $applications->total(),
                    'per_page' => $applications->perPage(),
                    'current_page' => $applications->currentPage(),
                    'last_page' => $applications->lastPage(),
                    'has_more' => $applications->hasMorePages()
                ]
            ];

            return $this->successHandler('获取终审列表成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取终审列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取终审列表失败', 500);
        }
    }

    /**
     * 获取终审详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::with(['user'])
                ->where('id', $id)
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_FINAL_REVIEW,
                    BusinessApplication::STATUS_SECONDARY_REVIEW
                ])
                ->first();

            if (!$application) {
                return $this->errorHandler('终审记录不存在', 404);
            }

            $data = $this->formatReviewData($application, true);

            return $this->successHandler('获取终审详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取终审详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取终审详情失败', 500);
        }
    }

    /**
     * 终审审批（管理员功能）
     * 注：实际项目中这个功能应该在管理后台实现，这里提供API供参考
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 权限检查（这里简化处理，实际应该检查用户角色）
            // if (!$user->hasRole('manager') && !$user->hasRole('admin')) {
            //     return $this->errorHandler('无权限操作', 403);
            // }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'approve_result' => 'required|boolean',
                'approve_notes' => 'nullable|string|max:1000'
            ], [
                'approve_result.required' => '请选择审批结果',
                'approve_result.boolean' => '审批结果格式错误',
                'approve_notes.max' => '审批备注不能超过1000个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->where('status', BusinessApplication::STATUS_FINAL_REVIEW)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            // 查找当前用户的待审批任务
            $currentTask = ApprovalTask::where('application_id', $application->id)
                ->where('approver_id', $user->id)
                ->where('status', ApprovalTask::STATUS_PENDING)
                ->where('step_code', 'final_review')
                ->first();

            if (!$currentTask) {
                return $this->errorHandler('未找到您的待审批任务', 404);
            }

            DB::beginTransaction();

            $approveResult = $request->input('approve_result');
            $approveNotes = $request->input('approve_notes');

            // 使用审批工作流服务完成任务
            $workflowService = new ApprovalWorkflowService();
            $completed = $workflowService->completeTask($currentTask, $approveResult, $approveNotes);

            if (!$completed) {
                throw new \Exception('审批任务完成失败');
            }

            // 兼容原有的审批历史记录
            $stage = $approveResult ? 'final_review' : 'final_review_rejected';
            $status = $approveResult ? 'approved' : 'rejected';
            $this->recordApprovalHistory($application, $stage, $status, $approveNotes);

            $message = $approveResult ? '终审通过' : '终审已拒绝';

            DB::commit();

            return $this->successHandler($message, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('终审审批失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('终审审批失败', 500);
        }
    }

    /**
     * 复审审批
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function secondaryApprove(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'approve_result' => 'required|boolean',
                'approve_notes' => 'nullable|string|max:1000'
            ], [
                'approve_result.required' => '请选择审批结果',
                'approve_result.boolean' => '审批结果格式错误',
                'approve_notes.max' => '审批备注不能超过1000个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->where('status', BusinessApplication::STATUS_SECONDARY_REVIEW)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            DB::beginTransaction();

            $approveResult = $request->input('approve_result');
            $approveNotes = $request->input('approve_notes');

            if ($approveResult) {
                // 复审通过，进入签约阶段
                $this->advanceToContract($application, $approveNotes);
                $message = '复审通过，可以发起签约';
            } else {
                // 复审拒绝
                $this->rejectApplication($application, $approveNotes);
                $message = '复审已拒绝';
            }

            DB::commit();

            return $this->successHandler($message, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('复审审批失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('复审审批失败', 500);
        }
    }

    /**
     * 进入复审阶段
     */
    private function advanceToSecondaryReview(BusinessApplication $application, $notes = null)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_SECONDARY_REVIEW,
            'status_text' => '复审中',
            'approval_notes' => $notes
        ]);

        $this->recordApprovalHistory($application, 'final_review', 'approved', $notes);
    }

    /**
     * 进入签约阶段
     */
    private function advanceToContract(BusinessApplication $application, $notes = null)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_CONTRACT_PENDING,
            'status_text' => '待签约',
            'approval_notes' => $notes,
            'approval_time' => Carbon::now()
        ]);

        $this->recordApprovalHistory($application, 'secondary_review', 'approved', $notes);
    }

    /**
     * 拒绝申请
     */
    private function rejectApplication(BusinessApplication $application, $notes = null)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_REJECTED,
            'status_text' => '审批拒绝',
            'approval_notes' => $notes,
            'approval_time' => Carbon::now()
        ]);

        $currentStage = $application->status === BusinessApplication::STATUS_FINAL_REVIEW ? 'final_review' : 'secondary_review';
        $this->recordApprovalHistory($application, $currentStage, 'rejected', $notes);
    }

    /**
     * 记录审批历史
     */
    private function recordApprovalHistory(BusinessApplication $application, $stage, $status, $notes = null)
    {
        $approvalHistory = $application->approval_history ?: [];
        $approvalHistory[] = [
            'stage' => $stage,
            'status' => $status,
            'operator_id' => Auth::id(),
            'operator_name' => Auth::user()->name,
            'notes' => $notes,
            'created_at' => Carbon::now()->toDateTimeString()
        ];
        $application->update(['approval_history' => $approvalHistory]);
    }

    /**
     * 格式化终审数据
     * 
     * @param BusinessApplication $application
     * @param bool $detail
     * @return array
     */
    private function formatReviewData(BusinessApplication $application, $detail = false)
    {
        $data = [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'customer_name' => $application->customer_name,
            'customer_phone' => $application->customer_phone,
            'product_name' => $application->product_name,
            'loan_amount' => $application->loan_amount,
            'status' => $application->status,
            'status_text' => $application->status_text,
            'submit_time' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : null,
            'created_time' => $application->created_at->format('Y-m-d H:i:s'),
            
            // 操作权限
            'can_approve' => in_array($application->status, [
                BusinessApplication::STATUS_FINAL_REVIEW,
                BusinessApplication::STATUS_SECONDARY_REVIEW
            ])
        ];

        if ($detail) {
            $data = array_merge($data, [
                'customer_id_card' => $application->customer_id_card,
                'loan_period' => $application->loan_period,
                'interest_rate' => $application->interest_rate,
                'vehicle_brand' => $application->vehicle_brand,
                'vehicle_model' => $application->vehicle_model,
                'vehicle_vin' => $application->vehicle_vin,
                'vehicle_year' => $application->vehicle_year,
                'vehicle_price' => $application->vehicle_price,
                'approval_notes' => $application->approval_notes,
                'approval_history' => $application->approval_history,
                'customer_data' => $application->customer_data,
                'risk_assessment' => $application->risk_assessment
            ]);
        }

        return $data;
    }
} 