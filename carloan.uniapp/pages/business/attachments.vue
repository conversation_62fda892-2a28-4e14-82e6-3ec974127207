<template>
	<!-- 添加附件 -->
	<view class="business-attachments">
		<navbar title="添加附件" />
		<view class="container">

			<!-- 车辆照片 -->
			<view class="basic-card">
				<view class="card-info">
					<text class="required">*</text>
					<text class="title">车辆照片</text>
				</view>
				<view class="card-desc">请上传清晰的车辆照片</view>
				<view class="card-container">
					<view class="upload-item single">
						<view class="upload-area" @click="chooseImage('vehicle_photo')">
							<image
								v-if="attachments.vehicle_photo"
								:src="attachments.vehicle_photo"
								class="upload-image"
								mode="aspectFill"
								@click.stop="previewImage(attachments.vehicle_photo)"
							/>
							<view v-else class="upload-placeholder">
								<wd-icon name="cloud-upload" size="40rpx" color="#999" />
								<text>上传照片</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 驾驶证照片 -->
			<view class="basic-card">
				<view class="card-info">
					<text class="required">*</text>
					<text class="title">驾驶证照片</text>
				</view>
				<view class="card-desc">请上传驾驶证正反面</view>
				<view class="card-container">
					<view class="upload-row">
						<view class="upload-item">
							<view class="upload-area" @click="chooseImage('driver_license_front')">
								<image
									v-if="attachments.driver_license_front"
									:src="attachments.driver_license_front"
									class="upload-image"
									mode="aspectFill"
									@click.stop="previewImage(attachments.driver_license_front)"
								/>
								<view v-else class="upload-placeholder">
									<wd-icon name="cloud-upload" size="40rpx" color="#999" />
									<text>上传照片</text>
								</view>
							</view>
						</view>
						<view class="upload-item">
							<view class="upload-area" @click="chooseImage('driver_license_back')">
								<image
									v-if="attachments.driver_license_back"
									:src="attachments.driver_license_back"
									class="upload-image"
									mode="aspectFill"
									@click.stop="previewImage(attachments.driver_license_back)"
								/>
								<view v-else class="upload-placeholder">
									<wd-icon name="cloud-upload" size="40rpx" color="#999" />
									<text>上传照片</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 银行卡照片 -->
			<view class="basic-card">
				<view class="card-info">
					<text class="required">*</text>
					<text class="title">银行卡照片</text>
				</view>
				<view class="card-desc">请上传银行卡正反面</view>
				<view class="card-container">
					<view class="upload-row">
						<view class="upload-item">
							<view class="upload-area" @click="chooseImage('bank_card_front')">
								<image
									v-if="attachments.bank_card_front"
									:src="attachments.bank_card_front"
									class="upload-image"
									mode="aspectFill"
									@click.stop="previewImage(attachments.bank_card_front)"
								/>
								<view v-else class="upload-placeholder">
									<wd-icon name="cloud-upload" size="40rpx" color="#999" />
									<text>上传照片</text>
								</view>
							</view>
						</view>
						<view class="upload-item">
							<view class="upload-area" @click="chooseImage('bank_card_back')">
								<image
									v-if="attachments.bank_card_back"
									:src="attachments.bank_card_back"
									class="upload-image"
									mode="aspectFill"
									@click.stop="previewImage(attachments.bank_card_back)"
								/>
								<view v-else class="upload-placeholder">
									<wd-icon name="cloud-upload" size="40rpx" color="#999" />
									<text>上传照片</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 征信报告 -->
			<view class="basic-card">
				<view class="card-info">
					<text class="required">*</text>
					<text class="title">征信报告</text>
				</view>
				<view class="card-desc">请上传征信报告</view>
				<view class="card-container">
					<view class="upload-item single">
						<view class="upload-area" @click="chooseImage('credit_report')">
							<image
								v-if="attachments.credit_report"
								:src="attachments.credit_report"
								class="upload-image"
								mode="aspectFill"
								@click.stop="previewImage(attachments.credit_report)"
							/>
							<view v-else class="upload-placeholder">
								<wd-icon name="cloud-upload" size="40rpx" color="#999" />
								<text>上传照片</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他 -->
			<view class="basic-card">
				<view class="card-info">
					<text class="title">其他</text>
				</view>
				<view class="card-desc">（资产证明、工作证明、社保记录、车行购买微信等）</view>
				<view class="card-container">
					<view class="upload-row">
						<!-- 已上传的其他文档 -->
						<view
							v-for="(doc, index) in attachments.other_documents"
							:key="index"
							class="upload-item"
						>
							<view class="upload-area">
								<image
									:src="doc"
									class="upload-image"
									mode="aspectFill"
									@click="previewImage(doc)"
								/>
								<view class="delete-btn" @click="removeOtherDocument(index)">
									<wd-icon name="error-fill" size="20rpx" color="#fff" />
								</view>
							</view>
						</view>
						<!-- 添加更多按钮 -->
						<view class="upload-item">
							<view class="upload-area" @click="chooseImage('other_documents')">
								<view class="upload-placeholder">
									<wd-icon name="cloud-upload" size="40rpx" color="#999" />
									<text>上传照片</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view style="height: 120rpx;"></view>
			
		</view>

		<!-- 底部按钮 -->
		<view class="footer-btn">
			<button 
				class="btn btn-primary" 
				:disabled="!isFormValid || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '提交中...' : '确认提交' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";
import { uploadImage } from "@/utils/image";

// 数据
const attachments = ref({
	vehicle_photo: '',              // 车辆照片
	driver_license_front: '',       // 驾驶证正面
	driver_license_back: '',        // 驾驶证反面
	bank_card_front: '',           // 银行卡正面
	bank_card_back: '',            // 银行卡反面
	credit_report: '',             // 征信报告
	other_documents: []            // 其他证明（改为数组支持多张图片）
});

// 状态
const submitLoading = ref(false);
const currentUploadType = ref('');

// 计算属性
const isFormValid = computed(() => {
	// 必须上传：车辆照片、驾驶证正反面、银行卡正反面、征信报告
	return attachments.value.vehicle_photo && 
		   attachments.value.driver_license_front && 
		   attachments.value.driver_license_back &&
		   attachments.value.bank_card_front &&
		   attachments.value.bank_card_back &&
		   attachments.value.credit_report;
});

// 页面生命周期
onShow(() => {
	loadAttachments();
});

// 方法
const loadAttachments = () => {
	try {
		const businessData = uni.getStorageSync('business_draft') || {};
		if (businessData.attachments) {
			attachments.value = { ...attachments.value, ...businessData.attachments };
		}
	} catch (error) {
		console.error('加载附件信息失败:', error);
	}
};

// 保存附件数据
const saveAttachments = () => {
	try {
		const businessData = uni.getStorageSync('business_draft') || {};
		businessData.attachments = attachments.value;
		uni.setStorageSync('business_draft', businessData);
	} catch (error) {
		console.error('保存附件信息失败:', error);
	}
};

// 选择图片
const chooseImage = (type) => {
	currentUploadType.value = type;

	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			const tempFilePath = res.tempFilePaths[0];

			// 检查文件大小
			uni.getFileInfo({
				filePath: tempFilePath,
				success: (fileInfo) => {
					if (fileInfo.size > 10 * 1024 * 1024) { // 10MB
						uni.showToast({
							title: '图片大小不能超过10MB',
							icon: 'none'
						});
						return;
					}

					// 上传图片
					uploadImageFile(tempFilePath, type);
				}
			});
		}
	});
};

// 删除其他文档
const removeOtherDocument = (index) => {
	attachments.value.other_documents.splice(index, 1);
	saveAttachments();
};

// 上传图片
const uploadImageFile = async (filePath, type) => {
	try {
		uni.showLoading({
			title: '上传中...'
		});

		// 调用通用上传方法
		const imageUrl = await uploadImage(filePath, "upload/uniapp/attachments");

		// 更新图片显示
		if (type === 'other_documents') {
			// 其他文档支持多张图片
			attachments.value.other_documents.push(imageUrl);
		} else {
			// 其他类型只支持单张图片
			attachments.value[type] = imageUrl;
		}
		saveAttachments();

		uni.hideLoading();
		uni.showToast({
			title: '上传成功',
			icon: 'success'
		});

	} catch (error) {
		uni.hideLoading();
		console.error('上传图片失败:', error);
		uni.showToast({
			title: '上传失败，请重试',
			icon: 'none'
		});
	}
};

// 预览图片
const previewImage = (url) => {
	// 如果是其他文档的图片，支持预览所有图片
	if (attachments.value.other_documents.includes(url)) {
		uni.previewImage({
			urls: attachments.value.other_documents,
			current: url
		});
	} else {
		uni.previewImage({
			urls: [url],
			current: url
		});
	}
};

// 提交申请
const handleSubmit = async () => {
	try {
		// 验证必填项
		if (!attachments.value.vehicle_photo) {
			return uni.showToast({
				title: '请上传车辆照片',
				icon: 'none'
			});
		}
		
		if (!attachments.value.driver_license_front) {
			return uni.showToast({
				title: '请上传驾驶证正面',
				icon: 'none'
			});
		}
		
		if (!attachments.value.driver_license_back) {
			return uni.showToast({
				title: '请上传驾驶证反面',
				icon: 'none'
			});
		}

		if (!attachments.value.bank_card_front) {
			return uni.showToast({
				title: '请上传银行卡正面',
				icon: 'none'
			});
		}

		if (!attachments.value.bank_card_back) {
			return uni.showToast({
				title: '请上传银行卡反面',
				icon: 'none'
			});
		}

		if (!attachments.value.credit_report) {
			return uni.showToast({
				title: '请上传征信报告',
				icon: 'none'
			});
		}

		submitLoading.value = true;
		
		// 获取业务数据
		const businessData = uni.getStorageSync('business_draft') || {};
		if (!businessData.customer_phone) {
			return uni.showToast({
				title: '请先完成客户信息',
				icon: 'none'
			});
		}
		
		// 准备附件数据
		const attachmentList = [];
		
		// 车辆照片
		if (attachments.value.vehicle_photo) {
			attachmentList.push({
				file_name: '车辆照片.jpg',
				file_url: attachments.value.vehicle_photo,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'vehicle_photo',
				is_required: true
			});
		}
		
		// 驾驶证正面
		if (attachments.value.driver_license_front) {
			attachmentList.push({
				file_name: '驾驶证正面.jpg',
				file_url: attachments.value.driver_license_front,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'driver_license_front',
				is_required: true
			});
		}
		
		// 驾驶证反面
		if (attachments.value.driver_license_back) {
			attachmentList.push({
				file_name: '驾驶证反面.jpg',
				file_url: attachments.value.driver_license_back,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'driver_license_back',
				is_required: true
			});
		}
		
		// 银行卡正面
		if (attachments.value.bank_card_front) {
			attachmentList.push({
				file_name: '银行卡正面.jpg',
				file_url: attachments.value.bank_card_front,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'bank_card_front',
				is_required: true
			});
		}
		
		// 银行卡反面
		if (attachments.value.bank_card_back) {
			attachmentList.push({
				file_name: '银行卡反面.jpg',
				file_url: attachments.value.bank_card_back,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'bank_card_back',
				is_required: true
			});
		}
		
		// 征信报告
		if (attachments.value.credit_report) {
			attachmentList.push({
				file_name: '征信报告.jpg',
				file_url: attachments.value.credit_report,
				file_type: 'image/jpeg',
				file_size: 1024000,
				category: 'credit_report',
				is_required: true
			});
		}
		
		// 其他证明（支持多张图片）
		if (attachments.value.other_documents && attachments.value.other_documents.length > 0) {
			attachments.value.other_documents.forEach((url, index) => {
				attachmentList.push({
					file_name: `其他证明${index + 1}.jpg`,
					file_url: url,
					file_type: 'image/jpeg',
					file_size: 1024000,
					category: 'other_documents',
					is_required: false
				});
			});
		}
		
		// 调用后端API保存附件信息
		const response = await request.post('/business-applications/save-attachment', {
			customer_phone: businessData.customer_phone,
			attachments: attachmentList
		});

		if (response.status_code === 200) {
			// 保存本地数据
			saveAttachments();
			
			// 更新完成状态（车辆评估模式）
			if (businessData.assessment_data) {
				businessData.assessment_data.attachments_completed = true;
			} else {
				businessData.attachments_completed = true;
			}
			uni.setStorageSync('business_draft', businessData);

			uni.showToast({
				title: '附件信息保存成功',
				icon: 'success'
			});

			// 返回到创建页面
			setTimeout(() => {
				uni.navigateBack();
			}, 1000);
		} else {
			uni.showToast({
				title: response.message || '保存失败',
				icon: 'none'
			});
		}
		
	} catch (error) {
		console.error('保存附件信息失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.business-attachments {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 140rpx;
}

.container {
	padding: 20rpx;
}

.basic-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 24rpx;
	padding: 32rpx;
	
	.card-info {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
		
		.required {
			color: #ff4d4f;
			font-size: 32rpx;
			margin-right: 8rpx;
		}
		
		.title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
	}
	
	.card-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 24rpx;
	}
	
	.card-container {
		.upload-row {
			display: flex;
			gap: 24rpx;
		}
		
		.upload-item {
			flex: 1;
			
			&.single {
				max-width: 200rpx;
			}
			
			.upload-area {
				width: 100%;
				height: 140rpx;
				border: 2rpx dashed #d0d0d0;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #fafafa;
				position: relative;
				
				.upload-image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
				
				.upload-placeholder {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					color: #999;
					
					.upload-icon {
						width: 40rpx;
						height: 40rpx;
						margin-bottom: 8rpx;
					}
					
					text {
						font-size: 24rpx;
					}
				}
			}
		}
	}
}

.footer-btn {
	position: fixed;
	z-index: 999;
	width: 100%;
	background-color: #ffffff;
	left: 0;
	bottom: 0;
	padding-top: 24rpx;
	padding-bottom: 24rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
	
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0;
		padding: 0;
		background: #000000;
		color: #ffffff;
		border-radius: 8rpx;
		border: none;
		
		&::after {
			border: none;
		}
		
		&.btn-primary {
			font-weight: 500;
			font-size: 32rpx;
			width: 670rpx;
			height: 104rpx;
			margin: auto;
			
			&:disabled {
				background: #ccc;
				color: #999;
			}
		}
	}
}
</style> 