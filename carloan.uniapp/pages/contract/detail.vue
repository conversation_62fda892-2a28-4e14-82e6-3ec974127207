<template>
	<view class="contract-detail">
		<navbar title="签约详情" />
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading">
			<uni-load-more status="loading" />
		</view>
		
		<!-- 详情内容 -->
		<view v-else-if="contractData" class="detail-content">
			<!-- 状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-title">签约状态</view>
					<view class="status-badge" :class="getStatusClass(contractData.status)">
						{{ contractData.status_text }}
					</view>
				</view>
				<view class="status-desc">
					{{ getStatusDescription(contractData.status) }}
				</view>
			</view>
			
			<!-- 客户信息 -->
			<view class="info-card">
				<view class="card-title">客户信息</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">客户姓名</view>
						<view class="value">{{ contractData.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="label">联系电话</view>
						<view class="value">{{ contractData.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="label">身份证号</view>
						<view class="value">{{ formatIdCard(contractData.customer_id_card) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 产品信息 -->
			<view class="info-card">
				<view class="card-title">产品信息</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">产品名称</view>
						<view class="value">{{ contractData.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="label">贷款金额</view>
						<view class="value">¥{{ formatAmount(contractData.loan_amount) }}</view>
					</view>
					<view class="info-item">
						<view class="label">贷款期限</view>
						<view class="value">{{ contractData.loan_period }}个月</view>
					</view>
					<view class="info-item">
						<view class="label">利率</view>
						<view class="value">{{ (contractData.interest_rate * 100).toFixed(2) }}%</view>
					</view>
				</view>
			</view>
			
			<!-- 车辆信息 -->
			<view class="info-card">
				<view class="card-title">车辆信息</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">车辆品牌</view>
						<view class="value">{{ contractData.vehicle_brand }}</view>
					</view>
					<view class="info-item">
						<view class="label">车辆型号</view>
						<view class="value">{{ contractData.vehicle_model }}</view>
					</view>
					<view class="info-item">
						<view class="label">车架号</view>
						<view class="value">{{ contractData.vehicle_vin }}</view>
					</view>
					<view class="info-item">
						<view class="label">车辆年份</view>
						<view class="value">{{ contractData.vehicle_year }}年</view>
					</view>
					<view class="info-item">
						<view class="label">车辆价格</view>
						<view class="value">¥{{ formatAmount(contractData.vehicle_price) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 业务信息 -->
			<view class="info-card">
				<view class="card-title">业务信息</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">业务单号</view>
						<view class="value">{{ contractData.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="label">提交时间</view>
						<view class="value">{{ contractData.submit_time }}</view>
					</view>
					<view class="info-item" v-if="contractData.approval_time">
						<view class="label">审批时间</view>
						<view class="value">{{ contractData.approval_time }}</view>
					</view>
				</view>
			</view>
			
			<!-- 审批备注 -->
			<view class="info-card" v-if="contractData.approval_notes">
				<view class="card-title">审批备注</view>
				<view class="notes-content">
					{{ contractData.approval_notes }}
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view v-else class="error-state">
			<view class="error-text">加载失败，请重试</view>
			<button class="retry-btn" @click="loadContractDetail">重新加载</button>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="contractData">
			<button 
				class="action-btn primary" 
				@click="onViewProgress"
				v-if="contractData.can_view_progress"
			>
				查看进度
			</button>
			<button 
				class="action-btn primary" 
				@click="onInitiateContract"
				v-if="contractData.can_initiate"
			>
				发起签约
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"
import { useContractStore } from "@/stores/contract"

// 响应式数据
const contractData = ref(null)
const loading = ref(true)
const contractId = ref('')

// 状态管理
const contractStore = useContractStore()

// 页面加载时获取参数
onLoad((options) => {
	if (options.id) {
		contractId.value = options.id
		loadContractDetail()
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	}
})

// 加载签约详情
const loadContractDetail = async () => {
	try {
		loading.value = true
		
		const response = await request.get(`/contracts/${contractId.value}`)
		
		if (response && response.status_code === 200) {
			contractData.value = response.data
		} else {
			throw new Error(response?.message || '获取详情失败')
		}
	} catch (error) {
		console.error('加载签约详情失败:', error)
		uni.showToast({
			title: error.message || '网络错误',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		'contract_pending': 'warning',
		'contract_processing': 'info',
		'contract_completed': 'success',
		'approved': 'success'
	}
	return classMap[status] || 'default'
}

// 获取状态描述
const getStatusDescription = (status) => {
	const descMap = {
		'contract_pending': '业务已通过审批，可以发起签约',
		'contract_processing': '已发起签约，等待客户确认',
		'contract_completed': '客户已完成签约',
		'approved': '业务已完成，审批通过'
	}
	return descMap[status] || ''
}

// 格式化身份证号
const formatIdCard = (idCard) => {
	if (!idCard) return ''
	return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0.00'
	return Number(amount).toLocaleString('zh-CN', {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	})
}

// 查看进度
const onViewProgress = () => {
	uni.navigateTo({
		url: `/pages/contract/progress?id=${contractId.value}`
	})
}

// 发起签约
const onInitiateContract = async () => {
	try {
		const result = await uni.showModal({
			title: '确认签约',
			content: '确定要发起电子签约吗？系统将发送签约邀请给客户。',
			confirmText: '确认发起',
			cancelText: '取消'
		})
		
		if (!result.confirm) {
			return
		}
		
		uni.showLoading({ title: '发起中...' })
		
		const response = await request.post(`/contracts/${contractId.value}/initiate`, {
			signing_method: 'electronic',
			notes: '发起电子签约'
		})
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '签约已发起',
				icon: 'success'
			})
			
			// 更新本地数据
			contractData.value.status = 'contract_processing'
			contractData.value.status_text = '签约中'
			contractData.value.can_initiate = false
			contractData.value.can_view_progress = true
			
			// 更新store
			contractStore.initiateContract(contractId.value, {
				signing_method: 'electronic',
				initiate_time: new Date().toISOString()
			})
		} else {
			uni.showToast({
				title: response.message || '发起签约失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('发起签约失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
.contract-detail {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.detail-content {
	padding: 20rpx;
}

.status-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.status-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		
		.status-badge {
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			
			&.warning {
				background: #fff7e6;
				color: #fa8c16;
			}
			
			&.info {
				background: #e6f7ff;
				color: #1890ff;
			}
			
			&.success {
				background: #f6ffed;
				color: #52c41a;
			}
		}
	}
	
	.status-desc {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
	}
}

.info-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.card-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 24rpx;
		padding-bottom: 16rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.info-list {
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #f8f8f8;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				font-size: 28rpx;
				color: #666;
				width: 160rpx;
				flex-shrink: 0;
			}
			
			.value {
				font-size: 28rpx;
				color: #333;
				flex: 1;
				text-align: right;
			}
		}
	}
	
	.notes-content {
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		background: #f8f8f8;
		padding: 20rpx;
		border-radius: 8rpx;
	}
}

.error-state {
	padding: 100rpx 40rpx;
	text-align: center;
	
	.error-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
	}
	
	.retry-btn {
		background: #007aff;
		color: #fff;
		border: none;
		border-radius: 8rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
	border-top: 1rpx solid #f0f0f0;
	
	.action-btn {
		width: 100%;
		height: 88rpx;
		border-radius: 8rpx;
		font-size: 32rpx;
		border: none;
		
		&.primary {
			background: #007aff;
			color: #fff;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f5f5f5;
}
</style> 