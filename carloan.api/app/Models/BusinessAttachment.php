<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessAttachment extends Model
{
    protected $table = 'business_attachments';

    protected $fillable = [
        'business_application_id',
        'customer_id',
        'file_name',
        'file_url',
        'file_type',
        'file_size',
        'category',
        'is_required',
        'upload_time'
    ];

    protected $casts = [
        'business_application_id' => 'integer',
        'customer_id' => 'integer',
        'file_size' => 'integer',
        'is_required' => 'boolean',
        'upload_time' => 'datetime'
    ];

    // 附件类别常量
    const CATEGORY_ID_CARD_FRONT = 'id_card_front';     // 身份证正面
    const CATEGORY_ID_CARD_BACK = 'id_card_back';       // 身份证背面
    const CATEGORY_INCOME_PROOF = 'income_proof';       // 收入证明
    const CATEGORY_PROPERTY_PROOF = 'property_proof';   // 房产证明
    const CATEGORY_VEHICLE_PROOF = 'vehicle_proof';     // 车辆证明
    const CATEGORY_BANK_STATEMENT = 'bank_statement';   // 银行流水
    const CATEGORY_WORK_PROOF = 'work_proof';          // 工作证明
    const CATEGORY_OTHER = 'other';                     // 其他

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'business_application_id');
    }

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 获取文件大小格式化
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }

    /**
     * 获取文件扩展名
     */
    public function getFileExtensionAttribute()
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * 是否图片文件
     */
    public function getIsImageAttribute()
    {
        $imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return in_array($this->file_type, $imageTypes);
    }

    /**
     * 是否PDF文件
     */
    public function getIsPdfAttribute()
    {
        return $this->file_type === 'application/pdf';
    }

    /**
     * 是否文档文件
     */
    public function getIsDocumentAttribute()
    {
        $docTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        return in_array($this->file_type, $docTypes);
    }

    /**
     * 获取类别文本
     */
    public function getCategoryTextAttribute()
    {
        $categories = [
            self::CATEGORY_ID_CARD_FRONT => '身份证正面',
            self::CATEGORY_ID_CARD_BACK => '身份证背面',
            self::CATEGORY_INCOME_PROOF => '收入证明',
            self::CATEGORY_PROPERTY_PROOF => '房产证明',
            self::CATEGORY_VEHICLE_PROOF => '车辆证明',
            self::CATEGORY_BANK_STATEMENT => '银行流水',
            self::CATEGORY_WORK_PROOF => '工作证明',
            self::CATEGORY_OTHER => '其他'
        ];

        return $categories[$this->category] ?? '未知类别';
    }

    /**
     * 作用域：按业务申请ID筛选
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('business_application_id', $applicationId);
    }

    /**
     * 作用域：按客户ID筛选
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * 作用域：按类别筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：必需附件
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * 作用域：可选附件
     */
    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    /**
     * 作用域：图片附件
     */
    public function scopeImages($query)
    {
        return $query->where('file_type', 'like', 'image/%');
    }

    /**
     * 作用域：PDF附件
     */
    public function scopePdfs($query)
    {
        return $query->where('file_type', 'application/pdf');
    }

    /**
     * 作用域：最近上传
     */
    public function scopeRecentlyUploaded($query, $days = 7)
    {
        return $query->where('upload_time', '>=', now()->subDays($days));
    }
} 