import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const operationRouter = {
  path: '/operation',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Operation',
  meta: {
    title: '运营管理',
    icon: 'el-icon-data-analysis'
  },
  children: [
    {
      path: 'performance',
      component: PlaceholderPage,
      name: 'OperationPerformance',
      meta: { title: '业绩统计' }
    },
    {
      path: 'analytics',
      component: PlaceholderPage,
      name: 'OperationAnalytics',
      meta: { title: '数据分析' }
    },
    {
      path: 'monitor',
      component: PlaceholderPage,
      name: 'OperationMonitor',
      meta: { title: '系统监控' }
    },
    {
      path: 'reports',
      component: PlaceholderPage,
      name: 'OperationReports',
      meta: { title: '运营报表' }
    },
    {
      path: 'performance/detail/:id',
      component: PlaceholderPage,
      name: 'OperationPerformanceDetail',
      meta: { title: '业绩详情', hidden: true }
    },
    {
      path: 'analytics/detail/:type',
      component: PlaceholderPage,
      name: 'OperationAnalyticsDetail',
      meta: { title: '分析详情', hidden: true }
    }
  ]
}

export default operationRouter
