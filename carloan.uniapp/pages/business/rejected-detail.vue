<template>
	<!-- 驳回详情 -->
	<view class="rejected-detail">
		<navbar title="驳回详情" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 详情内容 -->
		<view class="content" v-else-if="detail">
			<!-- 驳回状态 -->
			<view class="section">
				<view class="section-title">驳回状态</view>
				<view class="section-content">
					<view class="status-info">
						<view class="status-badge error">
							{{ detail.status_text || '已驳回' }}
						</view>
						<view class="reject-info" v-if="detail.reject_info">
							<view class="reject-stage">{{ detail.reject_info.reject_stage_text }}驳回</view>
							<view class="reject-time" v-if="detail.reject_info.reject_time">{{ detail.reject_info.reject_time }}</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 驳回原因 -->
			<view class="section" v-if="detail.reject_info">
				<view class="section-title">驳回原因</view>
				<view class="section-content">
					<view class="reject-reason">
						<view class="reason-content">
							{{ detail.reject_info.reject_reason || '系统驳回' }}
						</view>
						<view class="reviewer-info" v-if="detail.reject_info.reviewer">
							<view class="reviewer-name">审核人：{{ detail.reject_info.reviewer.name }}</view>
							<view class="reviewer-role" v-if="detail.reject_info.reviewer.role">{{ detail.reject_info.reviewer.role }}</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="section">
				<view class="section-title">基本信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">业务单号：</view>
						<view class="info-value">{{ detail.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">客户姓名：</view>
						<view class="info-value">{{ detail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">联系电话：</view>
						<view class="info-value">{{ detail.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">产品名称：</view>
						<view class="info-value">{{ detail.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">申请金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_amount) }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">提交时间：</view>
						<view class="info-value">{{ detail.submit_time || detail.created_time }}</view>
					</view>
				</view>
			</view>
			
			<!-- 车辆信息 -->
			<view class="section" v-if="detail.vehicle_info">
				<view class="section-title">车辆信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">车辆品牌：</view>
						<view class="info-value">{{ detail.vehicle_info.brand }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">车辆型号：</view>
						<view class="info-value">{{ detail.vehicle_info.model }}</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_info.year">
						<view class="info-label">车辆年份：</view>
						<view class="info-value">{{ detail.vehicle_info.year }}年</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_info.price">
						<view class="info-label">车辆价格：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.vehicle_info.price) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 审批历程 -->
			<view class="section" v-if="detail.review_history && detail.review_history.length > 0">
				<view class="section-title">审批历程</view>
				<view class="section-content">
					<view class="review-timeline">
						<view 
							class="timeline-item" 
							v-for="(step, index) in detail.review_history" 
							:key="index"
							:class="{ 
								completed: step.status === 'completed',
								rejected: step.status === 'rejected',
								current: step.status === 'current'
							}"
						>
							<view class="timeline-dot">
								<image 
									class="dot-icon" 
									:src="getTimelineIcon(step.status)" 
								/>
							</view>
							<view class="timeline-content">
								<view class="timeline-title">{{ step.title }}</view>
								<view class="timeline-desc" v-if="step.description">{{ step.description }}</view>
								<view class="timeline-time" v-if="step.time">{{ step.time }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 处理建议 -->
			<view class="section" v-if="detail.suggestions && detail.suggestions.length > 0">
				<view class="section-title">处理建议</view>
				<view class="section-content">
					<view class="suggestions-list">
						<view 
							class="suggestion-item" 
							v-for="(suggestion, index) in detail.suggestions" 
							:key="index"
						>
							<view class="suggestion-icon">💡</view>
							<view class="suggestion-text">{{ suggestion }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else>
			<view class="error-icon">❌</view>
			<view class="error-text">{{ errorMessage || '数据加载失败' }}</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="detail">
			<button 
				class="action-btn outline" 
				@click="onContactCustomer"
				v-if="detail.customer_phone"
			>
				联系客户
			</button>
			<button 
				class="action-btn primary" 
				@click="onCreateNewApplication"
				v-if="detail.can_recreate"
			>
				重新申请
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 响应式数据
const loading = ref(true)
const detail = ref(null)
const errorMessage = ref('')
const applicationId = ref('')

// 页面加载
onLoad((options) => {
	if (options.id) {
		applicationId.value = options.id
		loadDetail()
	} else {
		errorMessage.value = '参数错误'
		loading.value = false
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		errorMessage.value = ''
		
		const response = await request.get(`/business-applications/${applicationId.value}/rejected-detail`)
		
		console.log('驳回详情API Response:', response)
		
		if (response && response.status_code === 200) {
			detail.value = response.data
		} else {
			errorMessage.value = response?.message || '获取数据失败'
		}
	} catch (error) {
		console.error('获取驳回详情失败:', error)
		errorMessage.value = '网络错误，请重试'
	} finally {
		loading.value = false
	}
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return Number(amount).toLocaleString()
}

// 获取时间轴图标
const getTimelineIcon = (status) => {
	const iconMap = {
		'completed': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-green.png',
		'rejected': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-red.png',
		'current': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-blue.png',
		'pending': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-grey.png'
	}
	return iconMap[status] || iconMap['pending']
}

// 联系客户
const onContactCustomer = () => {
	if (detail.value.customer_phone) {
		uni.makePhoneCall({
			phoneNumber: detail.value.customer_phone
		})
	} else {
		uni.showToast({
			title: '无客户联系方式',
			icon: 'none'
		})
	}
}

// 重新申请
const onCreateNewApplication = () => {
	uni.showModal({
		title: '重新申请',
		content: '是否要为该客户重新创建业务申请？',
		confirmText: '确认',
		cancelText: '取消',
		success: (res) => {
			if (res.confirm) {
				// 跳转到创建新业务页面，预填客户信息
				uni.navigateTo({
					url: `/pages/business/create?customer_id=${detail.value.customer_id}&from=rejected`
				})
			}
		}
	})
}
</script>

<style lang="scss" scoped>
.rejected-detail {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		background: #f8f9fa;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.section-content {
		padding: 30rpx;
	}
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.info-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
		flex-shrink: 0;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		
		&.amount {
			color: #e6a23c;
			font-weight: 500;
		}
	}
}

.status-info {
	text-align: center;
	
	.status-badge {
		display: inline-block;
		padding: 12rpx 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 20rpx;
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
	}
	
	.reject-info {
		.reject-stage {
			font-size: 30rpx;
			font-weight: 500;
			color: #ff4d4f;
			margin-bottom: 8rpx;
		}
		
		.reject-time {
			font-size: 26rpx;
			color: #666;
		}
	}
}

.reject-reason {
	.reason-content {
		padding: 20rpx;
		background: #fff2f0;
		border-left: 4rpx solid #ff4d4f;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		margin-bottom: 20rpx;
	}
	
	.reviewer-info {
		.reviewer-name {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 4rpx;
		}
		
		.reviewer-role {
			font-size: 24rpx;
			color: #999;
		}
	}
}

.review-timeline {
	.timeline-item {
		display: flex;
		margin-bottom: 40rpx;
		position: relative;
		
		&:last-child {
			margin-bottom: 0;
			
			&::after {
				display: none;
			}
		}
		
		&::after {
			content: '';
			position: absolute;
			left: 19rpx;
			top: 38rpx;
			width: 2rpx;
			height: calc(100% + 20rpx);
			background: #ddd;
			z-index: 0;
		}
		
		&.completed::after {
			background: #52c41a;
		}
		
		&.rejected::after {
			background: #ff4d4f;
		}
		
		.timeline-dot {
			width: 38rpx;
			height: 38rpx;
			margin-right: 20rpx;
			position: relative;
			z-index: 1;
			
			.dot-icon {
				width: 100%;
				height: 100%;
			}
		}
		
		.timeline-content {
			flex: 1;
			
			.timeline-title {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.timeline-desc {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 8rpx;
			}
			
			.timeline-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.suggestions-list {
	.suggestion-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.suggestion-icon {
			font-size: 32rpx;
			margin-right: 16rpx;
			margin-top: 4rpx;
		}
		
		.suggestion-text {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}
	}
}

.error-state {
	padding: 100rpx 40rpx;
	text-align: center;
	
	.error-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
	}
	
	.btn {
		height: 76rpx;
		padding: 20rpx 32rpx;
		border-radius: 8rpx;
		border: none;
		font-size: 28rpx;
		
		&.btn-primary {
			background: #333;
			color: #fff;
		}
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 24rpx 40rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
	display: flex;
	gap: 16rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	
	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 8rpx;
		border: none;
		font-size: 32rpx;
		font-weight: 500;
		
		&.primary {
			background: #333;
			color: #fff;
		}
		
		&.outline {
			background: #fff;
			color: #333;
			border: 2rpx solid #d9d9d9;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 