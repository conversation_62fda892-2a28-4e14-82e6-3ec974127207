<template>
	<!-- 创建业务 -->
	<view class="business-create">
		<navbar title="创建业务" />
		<view class="check-box">
			<view class="check-label">核验：</view>
			<view class="input-container">
				<input
					class="check-input"
					v-model="customerPhone"
					placeholderClass="placeholder"
					type="text"
					placeholder="请输入手机号"
					maxlength="11"
				/>
				<button 
					class="btn check-btn" 
					:disabled="verifyLoading"
					@click="handleVerifyCustomer"
				>
					{{ verifyLoading ? '检验中...' : '检验用户' }}
				</button>
			</view>
		</view>
		<view class="tip error" v-if="verifyError">{{ verifyError }}</view>
		<view class="tip success" v-if="verifySuccess && !customerExists">核验成功，请继续完成业务创建</view>
		
		<!-- 客户存在时显示客户信息 -->
		<view class="customer-info-card" v-if="customerExists">
			<view class="card-title">客户信息</view>
			<view class="info-list">
				<view class="info-item">
					<view class="info-label">客户：</view>
					<view class="info-value">{{ customerInfo.customer_name || '张三' }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">身份证号码：</view>
					<view class="info-value">{{ formatIdCard(customerInfo.customer_id_card) }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">建单时间：</view>
					<view class="info-value">{{ customerInfo.created_at || '2025-02-10 18:00:00' }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">渠道商：</view>
					<view class="info-value">{{ customerInfo.channel_name || '渠道商名称' }}</view>
				</view>
			</view>
		</view>
		
		<!-- 业务类型选择 -->
		<view class="business-type-card" v-if="verifySuccess && !customerExists && showBusinessTypeSelect">
			<view class="card-title">业务类型</view>
			<view class="type-options">
				<view 
					class="type-option" 
					:class="{ active: businessData.business_type === 2 }"
					@click="selectBusinessType(2)"
				>
					<view class="option-radio">
						<view class="radio-dot" v-if="businessData.business_type === 2"></view>
					</view>
					<view class="option-text">以租代购</view>
				</view>
				<view 
					class="type-option" 
					:class="{ active: businessData.business_type === 1 }"
					@click="selectBusinessType(1)"
				>
					<view class="option-radio">
						<view class="radio-dot" v-if="businessData.business_type === 1"></view>
					</view>
					<view class="option-text">车抵贷</view>
				</view>
			</view>
		</view>
		
		<!-- 默认业务流程 -->
		<template v-if="pageMode === 'default'">
			<view class="business-card" v-if="verifySuccess && !customerExists">
				<view class="card-item" @click="goProductSelect">
					<view class="label">选择金融产品</view>
					<view class="status" v-if="businessData.product_id">已完善</view>
					<view class="status placeholder" v-else>请选择金融产品</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goPath('business/basic')">
					<view class="label">基础资料</view>
					<view class="status" v-if="businessData.basic_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goPath('business/contact')">
					<view class="label">直系亲属联系人信息</view>
					<view class="status" v-if="businessData.contact_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goPath('business/assets')">
					<view class="label">资产信息</view>
					<view class="status" v-if="businessData.assets_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goPath('business/liabilities')">
					<view class="label">负债信息</view>
					<view class="status" v-if="businessData.liabilities_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goVehicleSelect">
					<view class="label">标的物信息</view>
					<view class="status" v-if="businessData.vehicle_completed">已完善</view>
					<view class="status placeholder" v-else>请选择车辆信息</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
			</view>
			<view class="business-card" v-if="verifySuccess && !customerExists">
				<view class="card-item" @click="goPath('business/attachments')">
					<view class="label">点击上传/查看附件</view>
					<view class="status" v-if="businessData.attachments_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
			</view>
		</template>
		
		<!-- 车辆评估业务流程 -->
		<template v-if="pageMode === 'vehicle_assessment'">
			<view class="business-card" v-if="verifySuccess && !customerExists">
				<view class="card-item" @click="goProductSelect">
					<view class="label">选择金融产品</view>
					<view class="status" v-if="businessData.product_id">已完善</view>
					<view class="status placeholder" v-else>请选择金融产品</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goCustomerSelect">
					<view class="label">客户信息</view>
					<view class="status" v-if="assessmentData.customer_info_completed">已完善</view>
					<view class="status placeholder" v-else>请选择客户信息</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
				<view class="card-item" @click="goVehicleSelect">
					<view class="label">车辆信息</view>
					<view class="status" v-if="assessmentData.vehicle_info_completed">已完善</view>
					<view class="status placeholder" v-else>请选择车辆信息</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
			</view>
			<view class="business-card" v-if="verifySuccess && !customerExists">
				<view class="card-item" @click="goPath('business/attachments')">
					<view class="label">点击上传/查看附件</view>
					<view class="status" v-if="assessmentData.attachments_completed">已完善</view>
					<image
						class="arrow"
						src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
					/>
				</view>
			</view>
			
			<!-- 新车业务复选框 -->
			<view class="checkbox-card" v-if="verifySuccess && !customerExists">
				<view class="checkbox-item" @click="toggleNewCarBusiness" :class="{ active: isNewCarBusiness }">
					<view class="checkbox-box" :class="{ checked: isNewCarBusiness }">
						<wd-icon v-if="isNewCarBusiness" name="check" size="18px" color="#ffffff" />
					</view>
					<view class="checkbox-text">新车业务</view>
				</view>
			</view>
		</template>

		<view class="footer-btn" v-if="verifySuccess && !customerExists">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmitApplication"
			>
				{{ submitLoading ? '提交中...' : '确认申请' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const customerPhone = ref('');
const verifyLoading = ref(false);
const verifySuccess = ref(false);
const verifyError = ref('');
const submitLoading = ref(false);
const customerExists = ref(false); // 客户是否存在
const customerInfo = ref({}); // 客户信息
const showBusinessTypeSelect = ref(true); // 是否显示业务类型选择

// 页面模式，默认为 'default'，车辆评估为 'vehicle_assessment'
const pageMode = ref('default'); 
// 车辆业务类型： 'new_car' 或 'used_car'
const vehicleBusinessType = ref('new_car'); 

// 业务数据状态
const businessData = ref({
	customer_phone: '',
	customer_name: '',
	customer_id_card: '',
	product_id: '',
	product_name: '',
	basic_completed: false,
	contact_completed: false,
	assets_completed: false,
	liabilities_completed: false,
	vehicle_completed: false,
	attachments_completed: false,
	business_type: 2, // 默认为以租代购
	// 车辆信息
	vehicle_id: '',
	vehicle_model: '',
	vehicle_vin: '',
	// 客户选择信息
	customer_id: '',
	customer_completed: false,
});

// 车辆评估流程状态
const assessmentData = ref({
	customer_info_completed: false,
	vehicle_info_completed: false,
	attachments_completed: false,
});

// 计算是否可以提交
const canSubmit = computed(() => {
	if (pageMode.value === 'vehicle_assessment') {
		// 在车辆评估模式下，需要完成选择金融产品、客户信息、车辆信息和附件上传
		return verifySuccess.value &&
			businessData.value.product_id &&
			assessmentData.value.customer_info_completed &&
			(assessmentData.value.vehicle_info_completed || businessData.value.vehicle_id) &&
			assessmentData.value.attachments_completed;
	}
	// 默认模式下的提交条件
	return verifySuccess.value && 
		   businessData.value.business_type &&
		   businessData.value.basic_completed &&
		   businessData.value.contact_completed &&
		   businessData.value.assets_completed &&
		   businessData.value.liabilities_completed &&
		   (businessData.value.vehicle_completed || businessData.value.vehicle_id) &&
		   businessData.value.product_id;
});

// 页面加载时恢复数据
onMounted(() => {
	loadBusinessData();
});

// 页面显示时刷新状态
onShow(() => {
	// 每次页面显示时都重新加载最新的数据状态
	loadBusinessData();
});

// 页面加载参数处理
onLoad((options) => {
	if (options.customer_phone) {
		customerPhone.value = options.customer_phone;
		handleVerifyCustomer();
	}
	
	// 处理业务类型参数和来源
	if (options.business_type) {
		businessData.value.business_type = parseInt(options.business_type);
	}
	
	// 根据来源参数控制业务类型选择的显示
	if (options.from === 'home_new_business') {
		// 从首页创建新业务进来，默认选中以租代购，隐藏选择
		businessData.value.business_type = 2;
		showBusinessTypeSelect.value = false;
	} else if (options.from === 'home_car_loan') {
		// 从首页创建车抵贷进来，默认选中车抵贷，隐藏选择
		businessData.value.business_type = 1;
		showBusinessTypeSelect.value = false;
	} else if (options.from === 'vehicle_assessment') {
		// 从车辆评估进来
		pageMode.value = 'vehicle_assessment';
		showBusinessTypeSelect.value = false;
		// 重新加载数据以应用车辆评估模式的重置逻辑
		loadBusinessData();
		// 保存页面模式状态
		saveBusinessData();
	} else {
		// 从其他地方进来，显示业务类型选择
		showBusinessTypeSelect.value = true;
	}
});

// 加载业务数据
const loadBusinessData = () => {
	try {
		const storedData = uni.getStorageSync('business_draft');
		if (storedData) {
			// 检查是否是从车辆评估新进入的页面
			const isVehicleAssessmentMode = pageMode.value === 'vehicle_assessment';
			const isFirstTimeEntering = !storedData.page_mode || storedData.page_mode !== 'vehicle_assessment';
			
			if (isVehicleAssessmentMode && isFirstTimeEntering) {
				// 车辆评估模式首次进入，只恢复基本信息，但保留已选择的客户和车辆信息
				businessData.value = { 
					...businessData.value, 
					...storedData,
					// 重置产品选择状态
					product_id: '',
					product_name: '',
					// 保留客户和车辆选择信息（如果有的话）
					customer_id: storedData.customer_id || '',
					customer_name: storedData.customer_name || '',
					customer_phone: storedData.customer_phone || '',
					customer_id_card: storedData.customer_id_card || '',
					customer_completed: !!storedData.customer_id,
					vehicle_id: storedData.vehicle_id || '',
					vehicle_model: storedData.vehicle_model || '',
					vehicle_vin: storedData.vehicle_vin || '',
					vehicle_completed: !!storedData.vehicle_id
				};
				// 设置评估数据状态，基于是否已有选择信息
				assessmentData.value = {
					customer_info_completed: !!storedData.customer_id,
					vehicle_info_completed: !!storedData.vehicle_id,
					attachments_completed: storedData.assessment_data ? storedData.assessment_data.attachments_completed : false,
				};
			} else {
				// 正常模式或车辆评估模式的后续访问，恢复所有数据
				businessData.value = { ...businessData.value, ...storedData };
				
				// 恢复车辆评估模式状态
				if (storedData.assessment_data) {
					assessmentData.value = { ...assessmentData.value, ...storedData.assessment_data };
				}
			}
			
			// 恢复新车业务状态
			if (storedData.is_new_car_business !== undefined) {
				isNewCarBusiness.value = storedData.is_new_car_business;
			}
			
			if (storedData.page_mode) {
				pageMode.value = storedData.page_mode;
			}
			
			if (businessData.value.customer_phone) {
				customerPhone.value = businessData.value.customer_phone;
				verifySuccess.value = true;
			}
			
			console.log('加载业务数据完成:', businessData.value);
			console.log('评估数据:', assessmentData.value);
			console.log('页面模式:', pageMode.value);
		}
	} catch (error) {
		console.error('加载业务数据失败:', error);
	}
};

// 从服务器加载草稿数据
const loadDraftDataFromServer = async () => {
	if (!businessData.value.customer_phone) {
		return;
	}
	
	try {
		const response = await request.get('/business-applications/draft-data', {
			customer_phone: businessData.value.customer_phone
		});
		
		if (response.status_code === 200 && response.data.has_data) {
			const data = response.data;
			businessData.value.basic_completed = data.basic_completed;
			businessData.value.contact_completed = data.contact_completed;
			businessData.value.assets_completed = data.assets_completed;
			businessData.value.liabilities_completed = data.liabilities_completed;
			businessData.value.vehicle_completed = data.vehicle_completed;
			businessData.value.attachments_completed = data.attachments_completed;
			
			// 在车辆评估模式下，同步更新评估数据的附件状态
			if (pageMode.value === 'vehicle_assessment') {
				assessmentData.value.attachments_completed = data.attachments_completed;
			}
			
			// 更新本地存储，但保留现有的 assessmentData 状态
			saveBusinessData();
		}
	} catch (error) {
		console.error('从服务器加载草稿数据失败:', error);
	}
};

// 保存业务数据
const saveBusinessData = () => {
	try {
		const dataToSave = {
			...businessData.value,
			is_new_car_business: isNewCarBusiness.value,
			// 保存车辆评估模式的状态
			assessment_data: assessmentData.value,
			page_mode: pageMode.value
		};
		uni.setStorageSync('business_draft', dataToSave);
	} catch (error) {
		console.error('保存业务数据失败:', error);
	}
};

// 客户核验
const handleVerifyCustomer = async () => {
	try {
		// 验证手机号
		if (!customerPhone.value) {
			return uni.showToast({
				title: '请输入手机号',
				icon: 'none'
			});
		}

		if (!/^1[3-9]\d{9}$/.test(customerPhone.value)) {
			return uni.showToast({
				title: '请输入正确的手机号',
				icon: 'none'
			});
		}

		verifyLoading.value = true;
		verifyError.value = '';

		const response = await request.post('/customer/verify', {
			phone: customerPhone.value
		});

		if (response.status_code === 200) {
			if (response.data.exists) {
				// 客户存在
				verifySuccess.value = false;
				customerExists.value = true;
				customerInfo.value = response.data;
				verifyError.value = '该用户已存在，请联系管理员';
				// 禁止后续业务填写
			} else {
				// 客户不存在
				verifySuccess.value = true;
				customerExists.value = false;
				verifyError.value = '';
				businessData.value.customer_phone = customerPhone.value;
				saveBusinessData();
				uni.showToast({
					title: '核验成功',
					icon: 'success'
				});
			}
		} else {
			verifyError.value = response.message || '核验失败，请重试';
		}
	} catch (error) {
		verifyError.value = '网络错误，请重试';
		console.error('客户核验失败:', error);
	} finally {
		verifyLoading.value = false;
	}
};

// 页面跳转
const goPath = (path) => {
	if (!verifySuccess.value) {
		return uni.showToast({
			title: '请先完成客户核验',
			icon: 'none'
		});
	}
	
	uni.navigateTo({
		url: `/pages/${path}`,
	});
};

// 金融产品选择
const goProductSelect = () => {
	if (!verifySuccess.value) {
		return uni.showToast({
			title: '请先完成客户核验',
			icon: 'none'
		});
	}
	
	uni.navigateTo({
		url: '/pages/business/product-select'
	});
};

// 车辆信息选择
const goVehicleSelect = () => {
	if (!verifySuccess.value) {
		return uni.showToast({
			title: '请先完成客户核验',
			icon: 'none'
		});
	}
	
	uni.navigateTo({
		url: '/pages/business/vehicle-select'
	});
};

// 客户信息选择
const goCustomerSelect = () => {
	if (!verifySuccess.value) {
		return uni.showToast({
			title: '请先完成客户核验',
			icon: 'none'
		});
	}
	
	uni.navigateTo({
		url: '/pages/business/customer-select'
	});
};

// 提交申请
const handleSubmitApplication = async () => {
	try {
		if (!canSubmit.value) {
			return uni.showToast({
				title: '请完成所有必填信息',
				icon: 'none'
			});
		}

		submitLoading.value = true;

		// 准备提交数据，包含新车业务信息和车辆ID
		const submitData = {
			...businessData.value,
			is_new_car_business: isNewCarBusiness.value,
			// 如果选择了车辆，传递vehicle_id
			...(businessData.value.vehicle_id ? { vehicle_id: businessData.value.vehicle_id } : {})
		};

		const response = await request.post('/business-applications', submitData);

		if (response.status_code === 200) {
			// 清除草稿数据
			uni.removeStorageSync('business_draft');
			
			uni.showToast({
				title: '申请提交成功',
				icon: 'success'
			});

			// 跳转到申请详情页面
			setTimeout(() => {
				uni.redirectTo({
					url: `/pages/all_business/detail?id=${response.data.id}`
				});
			}, 1500);
		} else {
			uni.showToast({
				title: response.message || '提交失败，请重试',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('提交申请失败:', error);
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};

// 选择业务类型
const selectBusinessType = (type) => {
	businessData.value.business_type = type;
};

// 功能开发中提示
const handleComingSoon = (type) => {
	uni.showToast({
		title: '功能开发中，敬请期待',
		icon: 'none'
	});
};

// 格式化身份证号（脱敏显示）
const formatIdCard = (idCard) => {
	if (!idCard) return '***';
	if (idCard.length === 18) {
		return idCard.substring(0, 3) + '***********' + idCard.substring(14);
	}
	return idCard;
};

// 新车业务复选框
const isNewCarBusiness = ref(false);
const toggleNewCarBusiness = () => {
	isNewCarBusiness.value = !isNewCarBusiness.value;
	// 状态改变时自动保存
	saveBusinessData();
};
</script>

<style lang="scss" scoped>
.business-create {
	padding-bottom: 188rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 188rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 188rpx);
	
	.check-box {
		width: 750rpx;
		height: 202rpx;
		background: #ffffff;
		margin-top: 24rpx;
		padding: 36rpx 40rpx;
		box-sizing: border-box;
		
		.check-label {
			font-weight: 500;
			font-size: 30rpx;
			color: #000000;
		}
		
		.input-container {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-top: 24rpx;
			
			.check-input {
				width: 470rpx;
				height: 64rpx;
				border: 2rpx solid #d0d0d0;
				padding: 0 20rpx;
				box-sizing: border-box;
				border-radius: 8rpx;
			}
			
			.check-btn {
				width: 176rpx;
				height: 64rpx;
				font-weight: 400;
				font-size: 28rpx;
				
				&:disabled {
					opacity: 0.6;
				}
			}
		}
	}
	
	.tip {
		margin: 0; // 和上面check-box无间距，左右也无间距
		padding: 24rpx 40rpx; // 使用padding撑开高度，自然居中，左右与check-box内边距一致
		height: inherit!important;
		font-size: 28rpx;
		line-height: 1.4;
		
		&.success {
			background: #f0f9ff;
			color: #1890ff;
		}
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
	}
	
	.business-card {
		padding: 0 24rpx;
		width: 670rpx;
		background: #ffffff;
		border-radius: 16rpx;
		box-sizing: border-box;
		margin: 36rpx auto 0 auto;
		
		.card-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 622rpx;
			height: 107rpx;
			border-bottom: 1rpx solid #ededed;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				font-weight: 400;
				font-size: 30rpx;
				color: #000000;
				flex: 1;
			}
			
			.status {
				font-size: 26rpx;
				margin-right: 20rpx;
				
				&.selected {
					color: #52c41a;
					font-weight: 500;
				}
				
				&.placeholder {
					color: #999999;
				}
			}
			
			.arrow {
				width: 60rpx;
				height: 60rpx;
			}
		}
	}
	
	.customer-info-card {
		padding: 24rpx;
		background: #ffffff;
		border-radius: 16rpx;
		margin: 36rpx 24rpx 0;
		
		.card-title {
			font-weight: 500;
			font-size: 30rpx;
			color: #000000;
			margin-bottom: 24rpx;
		}
		
		.info-list {
			.info-item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f5f5f5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.info-label {
					font-size: 28rpx;
					color: #666666;
					width: 160rpx;
					flex-shrink: 0;
				}
				
				.info-value {
					font-size: 28rpx;
					color: #333333;
					flex: 1;
				}
			}
		}
	}
	
	.customer-not-found {
		padding: 24rpx;
		
		.not-found-card {
			background: #ffffff;
			border-radius: 16rpx;
			padding: 40rpx 24rpx;
			text-align: center;
			
			.card-title {
				font-weight: 500;
				font-size: 30rpx;
				color: #000000;
				margin-bottom: 20rpx;
			}
			
			.not-found-desc {
				font-size: 26rpx;
				color: #666666;
				margin-bottom: 40rpx;
				line-height: 1.6;
			}
			
			.action-buttons {
				.btn {
					width: 300rpx;
					height: 80rpx;
					font-size: 28rpx;
				}
			}
		}
	}
	
	.business-type-card {
		padding: 24rpx;
		background: #ffffff;
		border-radius: 16rpx;
		margin: 36rpx 24rpx 0;
		
		.card-title {
			font-weight: 500;
			font-size: 30rpx;
			color: #000000;
			margin-bottom: 24rpx;
		}
		
		.type-options {
			display: flex;
			align-items: center;
			gap: 20rpx;
			
			.type-option {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex: 1;
				height: 107rpx;
				border: 1rpx solid #ededed;
				border-radius: 8rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				
				&.active {
					border: 1rpx solid #000000;
				}
				
				.option-radio {
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					border: 1rpx solid #999999;
					margin-right: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					
					.radio-dot {
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
						background: #1890ff;
					}
				}
				
				.option-text {
					font-weight: 400;
					font-size: 28rpx;
					color: #000000;
				}
			}
		}
	}
	
	.checkbox-card {
		padding: 0 24rpx;
		background: #ffffff;
		border-radius: 16rpx;
		margin: 36rpx 24rpx 0;
		
		.checkbox-item {
			display: flex;
			align-items: center;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			transition: all 0.3s ease;
			cursor: pointer;
			
			&:last-child {
				border-bottom: none;
			}
			
			&.active {
				.checkbox-text {
					color: #000000;
					font-weight: 500;
				}
			}
			
			&:active {
				background: rgba(0, 0, 0, 0.05);
				border-radius: 8rpx;
			}
			
			.checkbox-box {
				width: 44rpx;
				height: 44rpx;
				border-radius: 6rpx;
				border: 2rpx solid #d0d0d0;
				margin-right: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #ffffff;
				transition: all 0.3s ease;
				flex-shrink: 0;
				
				&.checked {
					border: 2rpx solid #000000;
					background: #000000;
				}
			}
			
			.checkbox-text {
				font-size: 30rpx;
				color: #333333;
				flex: 1;
				transition: all 0.3s ease;
			}
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style>
