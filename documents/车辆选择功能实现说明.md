# 车辆选择功能实现说明

## 功能概述

实现了在创建业务申请时选择车辆信息的功能，用户可以从已有的车辆评估记录中选择合适的车辆作为业务标的物。

## 实现内容

### 1. 前端实现

#### 新增页面
- **pages/business/vehicle-select.vue** - 车辆选择页面
  - 完全按照UI设计稿高度还原，包括颜色、布局、字体大小等
  - 支持分页加载车辆评估列表
  - 显示车辆评估信息（车况、品牌型号、车架号）
  - 价格表格完全按照设计稿实现（四列+三列布局）
  - 点击选择车辆，弹窗确认后返回上一页

#### 页面注册
- 在 `pages.json` 中注册了新页面路由

#### 功能集成
- 修改 `pages/business/create.vue`：
  - 添加车辆选择跳转逻辑
  - 处理车辆选择回调事件
  - 显示已选择的车辆型号
  - 在提交时传递 `vehicle_id` 参数

### 2. 后端实现

#### 数据结构优化
- 修复了 `VehicleAssessment` 模型中价格格式化的问题
- 确保价格显示正确（除以10000转换为万元单位）

#### 接口优化
- 修改 `BusinessApplicationController` 的 `store` 方法：
  - 支持接收 `vehicle_id` 参数
  - 当传入 `vehicle_id` 时，自动从 `vehicle_assessments` 表获取车辆信息
  - 转换车辆评估数据为业务申请所需格式

#### 测试数据
- 创建了 `vehicle_assessments_test_data.sql` 文件
- 包含5条测试车辆评估记录，覆盖不同品牌和车况

### 3. 数据流程

1. 用户在创建业务页面点击"标的物信息"
2. 跳转到车辆选择页面，显示所有可选车辆
3. 用户点击选择某个车辆，弹窗确认
4. 返回创建业务页面，显示已选择的车辆型号
5. 提交业务申请时，传递 `vehicle_id` 给后端
6. 后端自动从车辆评估表获取完整车辆信息并保存

## 使用方法

### 准备测试数据
```sql
-- 执行测试数据SQL文件
source carloan.api/database/sql/vehicle_assessments_test_data.sql
```

### 测试流程
1. 登录系统
2. 进入创建业务页面
3. 完成客户核验
4. 点击"标的物信息"进入车辆选择页面
5. 选择合适的车辆
6. 完成其他必填信息后提交申请

## 技术特点

1. **高度还原UI** - 完全按照设计稿实现，包括表格样式、颜色、字体等
2. **数据一致性** - 前后端数据格式统一，价格单位正确显示
3. **用户体验** - 支持确认选择、显示选择结果、错误处理等
4. **代码复用** - 复用了车辆评估页面的样式和数据结构
5. **扩展性** - 支持分页加载，可处理大量车辆数据

## 注意事项

1. 确保 `vehicle_assessments` 表有数据才能正常选择车辆
2. 车辆选择是可选功能，也可以手动填写车辆信息
3. 选择的车辆信息会自动填充到业务申请的vehicle字段中
4. price字段以分为单位存储，前端显示时转换为万元 