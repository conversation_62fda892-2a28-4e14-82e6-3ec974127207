# 客户选择功能实现说明

## 功能概述
根据UI设计图实现了客户信息选择页面，用户可以从现有客户列表中选择客户进行业务操作。

## 实现内容

### 1. 前端页面实现
- **文件位置**: `carloan.uniapp/pages/business/customer-select.vue`
- **功能特点**:
  - 分页显示客户列表
  - 支持下拉刷新和上拉加载更多
  - 客户信息展示（姓名、身份证号、查询时间）
  - 风险评分状态显示（查询中/具体分数）
  - 电话拨打功能
  - 选择确认弹窗
  - 选择状态回显

### 2. 后端接口优化
- **CustomerTransformer 优化**: 添加了风险查询相关字段
  - `risk_status`: 风险查询状态
  - `risk_score`: 风险评分
  - `risk_level`: 风险等级
  - `latest_query_time`: 最新查询时间

- **CustomerController 优化**: 预加载风险查询数据，提高查询效率

### 3. 页面集成
- **路由配置**: 在 `pages.json` 中添加了 `customer-select` 路由
- **业务创建页面集成**: 在 `pages/business/create.vue` 中添加了客户选择入口
- **数据存储**: 选择的客户信息会保存到 `business_draft` 缓存中

## 页面布局特点

### UI 设计还原
- 左侧圆形选择按钮（类似 radio button）
- 右侧客户信息卡片
- 卡片头部显示"客户信息"标题和状态
- 客户姓名带电话图标（可点击拨打）
- 身份证号码显示
- 查询时间显示

### 状态管理
- 支持四种状态显示：
  - `querying`: 查询中（蓝色）
  - `completed`: 已完成显示分数（红色）
  - `failed`: 查询失败（灰色）
  - `unknown`: 未查询（灰色）

## 数据流程
1. 用户进入客户选择页面
2. 调用 `/customers` API 获取客户列表
3. 用户点击选择某个客户
4. 弹出确认对话框
5. 确认后将客户信息保存到本地缓存
6. 返回上一页面，显示选中的客户信息

## 技术要点
- 使用 Vue 3 Composition API
- 集成 z-paging 分页组件
- 响应式数据管理
- 本地缓存数据持久化
- 错误处理和用户反馈

## API 依赖
- `GET /customers`: 获取客户列表
- 支持分页参数 `page` 和 `page_size`
- 返回包含风险查询信息的完整客户数据

## 样式特色
- 遵循现有设计规范
- 自适应布局
- 流畅的交互动画
- 一致的视觉风格 