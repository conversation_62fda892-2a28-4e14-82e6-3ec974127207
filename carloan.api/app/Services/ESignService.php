<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Services\ThirdPartyApiLogService;

class ESignService
{
    private $baseUrl;
    private $appId;
    private $appSecret;
    
    public function __construct()
    {
        $this->baseUrl = env('ESIGN_BASE_URL', 'https://openapi.esign.cn');
        $this->appId = env('ESIGN_APP_ID');
        $this->appSecret = env('ESIGN_APP_SECRET');
    }
    
    /**
     * 发送HTTP请求并记录日志
     *
     * @param string $method HTTP方法
     * @param string $uri 请求URI
     * @param array $options 请求选项
     * @param string $apiName 接口名称
     * @param int|null $businessId 业务ID
     * @param string|null $businessType 业务类型
     * @return array 响应数据
     * @throws \Exception
     */
    private function makeHttpRequest(
        string $method, 
        string $uri, 
        array $options = [], 
        string $apiName = '',
        ?int $businessId = null,
        ?string $businessType = null
    ): array {
        $startTime = microtime(true);
        $client = new Client();
        $fullUrl = $this->baseUrl . $uri;
        
        $responseStatus = null;
        $responseHeaders = null;
        $responseBody = null;
        $errorMessage = null;
        $isSuccess = false;
        
        try {
            $response = $client->request($method, $fullUrl, $options);
            $responseStatus = $response->getStatusCode();
            $responseHeaders = $response->getHeaders();
            $responseBody = json_decode($response->getBody()->getContents(), true);
            $isSuccess = $responseStatus >= 200 && $responseStatus < 300;
            
            return $responseBody;
            
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $responseStatus = method_exists($e, 'getResponse') && $e->getResponse() 
                ? $e->getResponse()->getStatusCode() 
                : 0;
            
            throw $e;
        } finally {
            // 计算请求耗时
            $duration = (int)((microtime(true) - $startTime) * 1000);
            
            // 记录API调用日志
            ThirdPartyApiLogService::log(
                'esign',
                $apiName ?: $uri,
                $method,
                $fullUrl,
                $options['headers'] ?? null,
                $options['json'] ?? $options['form_params'] ?? null,
                $responseStatus,
                $responseHeaders,
                $responseBody,
                $duration,
                $errorMessage,
                $isSuccess,
                $businessId,
                $businessType
            );
        }
    }
    
    /**
     * 获取访问令牌
     */
    private function getAccessToken()
    {
        try {
            $result = $this->makeHttpRequest(
                'POST',
                '/v1/oauth2/access_token',
                [
                    'json' => [
                        'appId' => $this->appId,
                        'secret' => $this->appSecret,
                        'grantType' => 'client_credentials',
                        'scope' => 'get_sign_url'
                    ],
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ],
                '获取访问令牌'
            );
            
            if ($result['code'] === 0) {
                return $result['data']['token'];
            }
            
            throw new \Exception('获取e签宝访问令牌失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('e签宝获取访问令牌失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 上传本地文件
     */
    public function uploadFile($filePath, $fileName = null, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            if (!$fileName) {
                $fileName = basename($filePath);
            }
            
            // 1. 获取文件上传地址
            $result = $this->makeHttpRequest(
                'GET',
                '/v3/files/file-upload-url',
                [
                    'query' => [
                        'fileName' => $fileName,
                        'fileSize' => filesize($filePath),
                        'fileMd5' => md5_file($filePath)
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '获取文件上传地址',
                $businessId,
                'contract'
            );
            
            if ($result['code'] !== 0) {
                throw new \Exception('获取文件上传地址失败: ' . $result['message']);
            }
            
            $fileUploadUrl = $result['data']['fileUploadUrl'];
            $fileId = $result['data']['fileId'];
            
            // 2. 上传文件流
            $fileContent = file_get_contents($filePath);
            $client = new Client();
            $response = $client->request('PUT', $fileUploadUrl, [
                'body' => $fileContent,
                'headers' => [
                    'Content-Type' => 'application/octet-stream'
                ]
            ]);
            
            if ($response->getStatusCode() !== 200) {
                throw new \Exception('文件上传失败');
            }
            
            // 3. 查询文件上传状态，确保文件上传成功
            $maxRetries = 10;
            $retryCount = 0;
            
            while ($retryCount < $maxRetries) {
                sleep(1); // 等待1秒
                
                $statusResult = $this->makeHttpRequest(
                    'GET',
                    "/v3/files/{$fileId}",
                    [
                        'headers' => [
                            'X-Tsign-Open-Token' => $token,
                            'Content-Type' => 'application/json'
                        ]
                    ],
                    '查询文件上传状态',
                    $businessId,
                    'contract'
                );
                
                if ($statusResult['code'] === 0 && $statusResult['data']['fileStatus'] === 2) {
                    return [
                        'fileId' => $fileId,
                        'fileName' => $fileName,
                        'fileSize' => filesize($filePath)
                    ];
                }
                
                $retryCount++;
            }
            
            throw new \Exception('文件上传状态检查超时');
            
        } catch (\Exception $e) {
            Log::error('e签宝文件上传失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 创建合同模板
     */
    public function createDocTemplate($templateName, $fileId, $templateType = 0, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'POST',
                '/v3/doc-templates/doc-template-create-url',
                [
                    'json' => [
                        'docTemplateName' => $templateName,
                        'docTemplateType' => $templateType, // 0: PDF模板, 1: HTML模板
                        'fileId' => $fileId,
                        'redirectUrl' => env('ESIGN_TEMPLATE_REDIRECT_URL'),
                        'signerRoles' => ['甲方', '乙方'] // 签署方角色
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '创建合同模板',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return [
                    'docTemplateId' => $result['data']['docTemplateId'],
                    'docTemplateCreateUrl' => $result['data']['docTemplateCreateUrl'],
                    'docTemplateCreateLongUrl' => $result['data']['docTemplateCreateLongUrl']
                ];
            }
            
            throw new \Exception('创建合同模板失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('创建e签宝合同模板失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 查询合同模板详情
     */
    public function getDocTemplateDetail($docTemplateId, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'GET',
                "/v3/doc-templates/{$docTemplateId}",
                [
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '查询合同模板详情',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return $result['data'];
            }
            
            throw new \Exception('查询合同模板详情失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('查询e签宝合同模板详情失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 填写模板生成文件
     */
    public function fillTemplateToFile($docTemplateId, $fileName, $components, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'POST',
                '/v3/files/create-by-doc-template',
                [
                    'json' => [
                        'docTemplateId' => $docTemplateId,
                        'fileName' => $fileName,
                        'components' => $components,
                        'requiredCheck' => true // 校验必填控件
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '填写模板生成文件',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return [
                    'fileId' => $result['data']['fileId'],
                    'fileDownloadUrl' => $result['data']['fileDownloadUrl']
                ];
            }
            
            throw new \Exception('填写模板生成文件失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('e签宝填写模板生成文件失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成标准车贷合同
     */
    public function generateStandardContract($contractData)
    {
        try {
            $businessId = $contractData['application_id'] ?? null;
            
            // 使用预设的标准合同模板
            $templateId = env('ESIGN_STANDARD_CONTRACT_TEMPLATE_ID');
            
            if (!$templateId) {
                throw new \Exception('未配置标准合同模板ID');
            }
            
            // 准备填充数据
            $components = [
                [
                    'componentKey' => 'customer_name',
                    'componentValue' => $contractData['customer_name']
                ],
                [
                    'componentKey' => 'customer_id_card',
                    'componentValue' => $contractData['customer_id_card']
                ],
                [
                    'componentKey' => 'customer_phone',
                    'componentValue' => $contractData['customer_phone']
                ],
                [
                    'componentKey' => 'loan_amount',
                    'componentValue' => number_format($contractData['loan_amount'], 2)
                ],
                [
                    'componentKey' => 'loan_period',
                    'componentValue' => $contractData['loan_period']
                ],
                [
                    'componentKey' => 'interest_rate',
                    'componentValue' => number_format($contractData['interest_rate'], 4)
                ],
                [
                    'componentKey' => 'vehicle_brand',
                    'componentValue' => $contractData['vehicle_brand']
                ],
                [
                    'componentKey' => 'vehicle_model',
                    'componentValue' => $contractData['vehicle_model']
                ],
                [
                    'componentKey' => 'vehicle_vin',
                    'componentValue' => $contractData['vehicle_vin']
                ],
                [
                    'componentKey' => 'contract_date',
                    'componentValue' => date('Y-m-d')
                ]
            ];
            
            $fileName = "车贷合同_{$contractData['application_no']}.pdf";
            
            return $this->fillTemplateToFile($templateId, $fileName, $components, $businessId);
            
        } catch (\Exception $e) {
            Log::error('生成标准车贷合同失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成表格类车贷合同
     */
    public function generateTableContract($contractData)
    {
        try {
            $businessId = $contractData['application_id'] ?? null;
            
            // 使用预设的表格合同模板
            $templateId = env('ESIGN_TABLE_CONTRACT_TEMPLATE_ID');
            
            if (!$templateId) {
                throw new \Exception('未配置表格合同模板ID');
            }
            
            // 准备填充数据
            $components = [
                // 基本信息
                [
                    'componentKey' => 'customer_name',
                    'componentValue' => $contractData['customer_name']
                ],
                [
                    'componentKey' => 'customer_id_card',
                    'componentValue' => $contractData['customer_id_card']
                ],
                [
                    'componentKey' => 'customer_phone',
                    'componentValue' => $contractData['customer_phone']
                ],
                // 贷款信息
                [
                    'componentKey' => 'loan_amount',
                    'componentValue' => number_format($contractData['loan_amount'], 2)
                ],
                [
                    'componentKey' => 'loan_period',
                    'componentValue' => $contractData['loan_period']
                ],
                [
                    'componentKey' => 'interest_rate',
                    'componentValue' => number_format($contractData['interest_rate'], 4)
                ],
                // 车辆信息
                [
                    'componentKey' => 'vehicle_brand',
                    'componentValue' => $contractData['vehicle_brand']
                ],
                [
                    'componentKey' => 'vehicle_model',
                    'componentValue' => $contractData['vehicle_model']
                ],
                [
                    'componentKey' => 'vehicle_vin',
                    'componentValue' => $contractData['vehicle_vin']
                ],
                [
                    'componentKey' => 'vehicle_price',
                    'componentValue' => number_format($contractData['vehicle_price'], 2)
                ]
            ];
            
            // 如果有联系人信息，加入动态表格
            if (!empty($contractData['contacts'])) {
                $contactsData = [];
                foreach ($contractData['contacts'] as $index => $contact) {
                    $contactsData[] = [
                        'componentKey' => "contact_name_{$index}",
                        'componentValue' => $contact['name']
                    ];
                    $contactsData[] = [
                        'componentKey' => "contact_phone_{$index}",
                        'componentValue' => $contact['phone']
                    ];
                    $contactsData[] = [
                        'componentKey' => "contact_relation_{$index}",
                        'componentValue' => $contact['relation']
                    ];
                }
                $components = array_merge($components, $contactsData);
            }
            
            // 如果有资产信息，加入动态表格
            if (!empty($contractData['assets'])) {
                $assetsData = [];
                foreach ($contractData['assets'] as $index => $asset) {
                    $assetsData[] = [
                        'componentKey' => "asset_type_{$index}",
                        'componentValue' => $asset['type']
                    ];
                    $assetsData[] = [
                        'componentKey' => "asset_value_{$index}",
                        'componentValue' => number_format($asset['value'], 2)
                    ];
                    $assetsData[] = [
                        'componentKey' => "asset_description_{$index}",
                        'componentValue' => $asset['description']
                    ];
                }
                $components = array_merge($components, $assetsData);
            }
            
            $components[] = [
                'componentKey' => 'contract_date',
                'componentValue' => date('Y-m-d')
            ];
            
            $fileName = "车贷合同_{$contractData['application_no']}.pdf";
            
            return $this->fillTemplateToFile($templateId, $fileName, $components, $businessId);
            
        } catch (\Exception $e) {
            Log::error('生成表格车贷合同失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 创建个人账户
     */
    public function createPersonalAccount($name, $idCard, $mobile, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'POST',
                '/v1/accounts/createByThirdPartyUserId',
                [
                    'json' => [
                        'thirdPartyUserId' => 'customer_' . $idCard,
                        'name' => $name,
                        'idType' => 'CRED_PSN_CH_IDCARD',
                        'idNumber' => $idCard,
                        'mobile' => $mobile,
                        'email' => ''
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '创建个人账户',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return $result['data']['accountId'];
            }
            
            throw new \Exception('创建e签宝个人账户失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('创建e签宝个人账户失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 创建签署流程
     */
    public function createSignFlow($contractData)
    {
        try {
            $token = $this->getAccessToken();
            $businessId = $contractData['application_id'] ?? null;

            // 1. 创建签署流程
            $result = $this->makeHttpRequest(
                'POST',
                '/v1/signflows',
                [
                    'json' => [
                        'businessScene' => '车贷合同签署',
                        'noticeDeveloperUrl' => env('ESIGN_CALLBACK_URL'),
                        'noticeType' => 'SIGN_FLOW_FINISH',
                        'redirectUrl' => env('ESIGN_REDIRECT_URL')
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '创建签署流程',
                $businessId,
                'contract'
            );
            
            if ($result['code'] !== 0) {
                throw new \Exception('创建签署流程失败: ' . $result['message']);
            }
            
            $flowId = $result['data']['flowId'];
            
            // 2. 添加合同文档
            $this->addDocumentToFlow($flowId, $contractData, $token);
            
            // 3. 添加签署方
            $this->addSignerToFlow($flowId, $contractData, $token);
            
            // 4. 启动签署流程
            $this->startSignFlow($flowId, $token, $businessId);
            
            return $flowId;
            
        } catch (\Exception $e) {
            Log::error('创建e签宝签署流程失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 添加文档到签署流程
     */
    private function addDocumentToFlow($flowId, $contractData, $token)
    {
        // 根据合同类型生成不同的合同
        if ($contractData['contract_type'] === 'standard') {
            $contractFile = $this->generateStandardContract($contractData);
        } else {
            $contractFile = $this->generateTableContract($contractData);
        }
        
        $businessId = $contractData['application_id'] ?? null;
        
        $result = $this->makeHttpRequest(
            'POST',
            "/v1/signflows/{$flowId}/documents",
            [
                'json' => [
                    'docs' => [
                        [
                            'fileId' => $contractFile['fileId'],
                            'fileName' => "车贷合同_{$contractData['application_no']}.pdf"
                        ]
                    ]
                ],
                'headers' => [
                    'X-Tsign-Open-Token' => $token,
                    'Content-Type' => 'application/json'
                ]
            ],
            '添加文档到流程',
            $businessId,
            'contract'
        );
        
        if ($result['code'] !== 0) {
            throw new \Exception('添加合同文档失败: ' . $result['message']);
        }
        
        return $result['data']['docs'][0]['fileId'];
    }
    
    /**
     * 添加签署方到流程
     */
    private function addSignerToFlow($flowId, $contractData, $token)
    {
        $businessId = $contractData['application_id'] ?? null;
        
        // 确保客户账户存在
        $accountId = $this->createPersonalAccount(
            $contractData['customer_name'],
            $contractData['customer_id_card'],
            $contractData['customer_phone'],
            $businessId
        );
        
        $result = $this->makeHttpRequest(
            'POST',
            "/v1/signflows/{$flowId}/signers",
            [
                'json' => [
                    'signers' => [
                        [
                            'signerAccount' => [
                                'signerAccountId' => $accountId,
                                'authorizedAccountId' => ''
                            ],
                            'signFields' => [
                                [
                                    'fileId' => '', // 这里需要文档ID
                                    'normalSignFieldConfig' => [
                                        'freeMode' => true,
                                        'signFieldStyle' => 1,
                                        'signFieldPosition' => [
                                            'positionPage' => '1',
                                            'positionX' => 100,
                                            'positionY' => 200
                                        ]
                                    ]
                                ]
                            ],
                            'noticeConfig' => [
                                'noticeTypes' => ['SMS'],
                                'smsConfig' => [
                                    'smsContent' => "【车贷平台】您的车贷合同已生成，请点击链接完成签署：{signUrl}"
                                ]
                            ]
                        ]
                    ]
                ],
                'headers' => [
                    'X-Tsign-Open-Token' => $token,
                    'Content-Type' => 'application/json'
                ]
            ],
            '添加签署方',
            $businessId,
            'contract'
        );
        
        if ($result['code'] !== 0) {
            throw new \Exception('添加签署方失败: ' . $result['message']);
        }
    }
    
    /**
     * 启动签署流程
     */
    private function startSignFlow($flowId, $token, $businessId = null)
    {
        $result = $this->makeHttpRequest(
            'PUT',
            "/v1/signflows/{$flowId}/start",
            [
                'headers' => [
                    'X-Tsign-Open-Token' => $token,
                    'Content-Type' => 'application/json'
                ]
            ],
            '启动签署流程',
            $businessId,
            'contract'
        );
        
        if ($result['code'] !== 0) {
            throw new \Exception('启动签署流程失败: ' . $result['message']);
        }
    }
    
    /**
     * 获取签署链接
     */
    public function getSignUrl($flowId, $accountId, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'GET',
                "/v1/signflows/{$flowId}/signers/{$accountId}/signurl",
                [
                    'query' => [
                        'urlType' => 1 // H5签署链接
                    ],
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '获取签署链接',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return $result['data']['shortUrl'];
            }
            
            throw new \Exception('获取签署链接失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('获取e签宝签署链接失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 查询签署状态
     */
    public function getSignStatus($flowId, $businessId = null)
    {
        try {
            $token = $this->getAccessToken();
            
            $result = $this->makeHttpRequest(
                'GET',
                "/v1/signflows/{$flowId}",
                [
                    'headers' => [
                        'X-Tsign-Open-Token' => $token,
                        'Content-Type' => 'application/json'
                    ]
                ],
                '查询签署状态',
                $businessId,
                'contract'
            );
            
            if ($result['code'] === 0) {
                return $result['data'];
            }
            
            throw new \Exception('查询签署状态失败: ' . $result['message']);
            
        } catch (\Exception $e) {
            Log::error('查询e签宝签署状态失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 处理e签宝回调
     */
    public function handleCallback($callbackData)
    {
        try {
            Log::info('收到e签宝回调: ', $callbackData);
            
            $action = $callbackData['action'] ?? '';
            $flowId = $callbackData['flowId'] ?? '';
            
            switch ($action) {
                case 'SIGN_FLOW_FINISH':
                    $this->handleSignFlowFinish($flowId, $callbackData);
                    break;
                case 'SIGN_FLOW_REVOKE':
                    $this->handleSignFlowRevoke($flowId, $callbackData);
                    break;
                default:
                    Log::warning('未知的e签宝回调类型: ' . $action);
            }
            
        } catch (\Exception $e) {
            Log::error('处理e签宝回调失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 处理签署完成回调
     */
    private function handleSignFlowFinish($flowId, $callbackData)
    {
        // 查找对应的业务申请并更新状态
        // 实际项目中需要根据flowId找到对应的业务申请
        Log::info("签署流程完成: {$flowId}");
    }
    
    /**
     * 处理签署撤销回调
     */
    private function handleSignFlowRevoke($flowId, $callbackData)
    {
        // 处理签署撤销
        Log::info("签署流程撤销: {$flowId}");
    }
} 