-- 审批工作流相关表结构
-- 创建时间: 2024-03-17
-- 说明: 用于管理业务申请的审批流程、审批任务和抄送记录

-- 1. 审批流程配置表
CREATE TABLE IF NOT EXISTS `approval_workflows` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `business_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务类型',
  `step_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批步骤名称',
  `step_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '步骤代码',
  `step_order` int(11) NOT NULL DEFAULT '0' COMMENT '步骤顺序',
  `approver_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '审批人类型:user指定用户,role角色,system系统',
  `approver_ids` json DEFAULT NULL COMMENT '审批人ID列表',
  `cc_user_ids` json DEFAULT NULL COMMENT '抄送人ID列表',
  `is_required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否必须审批:1是,0否',
  `auto_approve_conditions` json DEFAULT NULL COMMENT '自动审批条件',
  `timeout_hours` int(11) DEFAULT NULL COMMENT '超时时间(小时)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  KEY `approval_workflows_business_type_index` (`business_type`),
  KEY `approval_workflows_step_code_index` (`step_code`),
  KEY `approval_workflows_step_order_index` (`step_order`),
  KEY `approval_workflows_status_index` (`status`),
  KEY `approval_workflows_deleted_at_index` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批流程配置表';

-- 2. 审批任务表
CREATE TABLE IF NOT EXISTS `approval_tasks` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务申请ID',
  `workflow_id` bigint(20) UNSIGNED NOT NULL COMMENT '工作流配置ID',
  `task_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务编号',
  `step_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批步骤名称',
  `step_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '步骤代码',
  `step_order` int(11) NOT NULL DEFAULT '0' COMMENT '步骤顺序',
  `approver_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批人ID',
  `approver_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批人姓名',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '任务状态:pending待审批,approved已通过,rejected已拒绝,cancelled已取消,timeout已超时',
  `status_text` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '待审批' COMMENT '状态文本',
  `assigned_time` timestamp NULL DEFAULT NULL COMMENT '分配时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `approve_result` tinyint(1) DEFAULT NULL COMMENT '审批结果:1通过,0拒绝',
  `approve_notes` text COLLATE utf8mb4_unicode_ci COMMENT '审批意见',
  `timeout_time` timestamp NULL DEFAULT NULL COMMENT '超时时间',
  `is_timeout` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已超时:1是,0否',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `approval_tasks_task_no_unique` (`task_no`),
  KEY `approval_tasks_application_id_index` (`application_id`),
  KEY `approval_tasks_workflow_id_index` (`workflow_id`),
  KEY `approval_tasks_approver_id_index` (`approver_id`),
  KEY `approval_tasks_status_index` (`status`),
  KEY `approval_tasks_step_code_index` (`step_code`),
  KEY `approval_tasks_approver_id_status_index` (`approver_id`,`status`),
  KEY `approval_tasks_assigned_time_index` (`assigned_time`),
  KEY `approval_tasks_deleted_at_index` (`deleted_at`),
  CONSTRAINT `approval_tasks_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE,
  CONSTRAINT `approval_tasks_workflow_id_foreign` FOREIGN KEY (`workflow_id`) REFERENCES `approval_workflows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批任务表';

-- 3. 抄送记录表
CREATE TABLE IF NOT EXISTS `approval_cc_records` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务申请ID',
  `task_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '关联审批任务ID',
  `cc_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '抄送人ID',
  `cc_user_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '抄送人姓名',
  `cc_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'workflow' COMMENT '抄送类型:workflow工作流抄送,manual手动抄送',
  `step_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '抄送步骤名称',
  `step_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '抄送步骤代码',
  `cc_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '抄送原因',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读:1是,0否',
  `read_time` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `cc_time` timestamp NULL DEFAULT NULL COMMENT '抄送时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  KEY `approval_cc_records_application_id_index` (`application_id`),
  KEY `approval_cc_records_task_id_index` (`task_id`),
  KEY `approval_cc_records_cc_user_id_index` (`cc_user_id`),
  KEY `approval_cc_records_cc_type_index` (`cc_type`),
  KEY `approval_cc_records_step_code_index` (`step_code`),
  KEY `approval_cc_records_cc_user_id_is_read_index` (`cc_user_id`,`is_read`),
  KEY `approval_cc_records_cc_time_index` (`cc_time`),
  KEY `approval_cc_records_deleted_at_index` (`deleted_at`),
  CONSTRAINT `approval_cc_records_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE,
  CONSTRAINT `approval_cc_records_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `approval_tasks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抄送记录表';

-- 4. 插入默认审批流程配置
INSERT IGNORE INTO `approval_workflows` (
  `business_type`, `step_name`, `step_code`, `step_order`, `approver_type`, `approver_ids`, 
  `cc_user_ids`, `is_required`, `timeout_hours`, `status`, `created_at`, `updated_at`
) VALUES 
-- 业务申请审批流程
('business_application', '初审', 'initial_review', 1, 'user', JSON_ARRAY(2), JSON_ARRAY(3), 1, 24, 1, NOW(), NOW()),
('business_application', '终审', 'final_review', 2, 'user', JSON_ARRAY(3), JSON_ARRAY(2), 1, 48, 1, NOW(), NOW()),
('business_application', '复审', 'secondary_review', 3, 'user', JSON_ARRAY(2, 3), JSON_ARRAY(), 1, 24, 1, NOW(), NOW()),

-- 面审预约流程
('interview_appointment', '面审安排', 'interview_schedule', 1, 'user', JSON_ARRAY(1), JSON_ARRAY(2, 3), 1, 12, 1, NOW(), NOW()),

-- 签约流程
('contract_process', '合同审核', 'contract_review', 1, 'user', JSON_ARRAY(3), JSON_ARRAY(2), 1, 24, 1, NOW(), NOW()),

-- 补件审核流程
('supplement_review', '补件审核', 'supplement_approve', 1, 'user', JSON_ARRAY(2), JSON_ARRAY(3), 1, 48, 1, NOW(), NOW());

-- 5. 创建用户审批统计视图（支持软删除）
CREATE OR REPLACE VIEW `v_user_approval_summary` AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    -- 待审批任务数
    COALESCE(pending_tasks.count, 0) as pending_approval_count,
    -- 未读抄送数
    COALESCE(unread_cc.count, 0) as unread_cc_count,
    -- 总抄送数
    COALESCE(total_cc.count, 0) as total_cc_count,
    -- 我发起的申请数
    COALESCE(my_apps.count, 0) as my_application_count,
    -- 今日待审批
    COALESCE(today_pending.count, 0) as today_pending_count,
    -- 本周待审批
    COALESCE(week_pending.count, 0) as week_pending_count
FROM users u
LEFT JOIN (
    SELECT approver_id, COUNT(*) as count
    FROM approval_tasks 
    WHERE status = 'pending' AND deleted_at IS NULL
    GROUP BY approver_id
) pending_tasks ON u.id = pending_tasks.approver_id
LEFT JOIN (
    SELECT cc_user_id, COUNT(*) as count
    FROM approval_cc_records 
    WHERE is_read = 0 AND deleted_at IS NULL
    GROUP BY cc_user_id
) unread_cc ON u.id = unread_cc.cc_user_id
LEFT JOIN (
    SELECT cc_user_id, COUNT(*) as count
    FROM approval_cc_records 
    WHERE deleted_at IS NULL
    GROUP BY cc_user_id
) total_cc ON u.id = total_cc.cc_user_id
LEFT JOIN (
    SELECT user_id, COUNT(*) as count
    FROM business_applications 
    WHERE deleted_at IS NULL
    GROUP BY user_id
) my_apps ON u.id = my_apps.user_id
LEFT JOIN (
    SELECT approver_id, COUNT(*) as count
    FROM approval_tasks 
    WHERE status = 'pending' 
    AND DATE(assigned_time) = CURDATE() 
    AND deleted_at IS NULL
    GROUP BY approver_id
) today_pending ON u.id = today_pending.approver_id
LEFT JOIN (
    SELECT approver_id, COUNT(*) as count
    FROM approval_tasks 
    WHERE status = 'pending' 
    AND assigned_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    AND deleted_at IS NULL
    GROUP BY approver_id
) week_pending ON u.id = week_pending.approver_id
WHERE u.deleted_at IS NULL;

-- 6. 创建索引优化查询性能
-- ALTER TABLE `approval_tasks` ADD INDEX `idx_approver_status_time` (`approver_id`, `status`, `assigned_time`);
-- ALTER TABLE `approval_cc_records` ADD INDEX `idx_cc_user_read_time` (`cc_user_id`, `is_read`, `cc_time`);
-- ALTER TABLE `business_applications` ADD INDEX `idx_user_status_submit` (`user_id`, `status`, `submit_time`);

-- 6. 创建视图简化查询
CREATE OR REPLACE VIEW `v_user_approval_summary` AS
SELECT 
  u.id as user_id,
  u.name as user_name,
  -- 待我审批数量
  COALESCE(pending_tasks.count, 0) as pending_approval_count,
  -- 抄送我的数量 
  COALESCE(cc_records.count, 0) as cc_count,
  -- 我发起的数量
  COALESCE(my_applications.count, 0) as my_application_count
FROM users u
LEFT JOIN (
  SELECT approver_id, COUNT(*) as count
  FROM approval_tasks 
  WHERE status = 'pending' AND deleted_at IS NULL
  GROUP BY approver_id
) pending_tasks ON u.id = pending_tasks.approver_id
LEFT JOIN (
  SELECT cc_user_id, COUNT(*) as count
  FROM approval_cc_records 
  WHERE is_read = 0 AND deleted_at IS NULL
  GROUP BY cc_user_id  
) cc_records ON u.id = cc_records.cc_user_id
LEFT JOIN (
  SELECT user_id, COUNT(*) as count
  FROM business_applications
  WHERE deleted_at IS NULL
  GROUP BY user_id
) my_applications ON u.id = my_applications.user_id
WHERE u.status = 1 AND u.deleted_at IS NULL;

-- 7. 验证数据
SELECT * FROM approval_workflows ORDER BY business_type, step_order;

-- 8. 清理脚本（备用，慎用）
-- DROP VIEW IF EXISTS v_user_approval_summary;
-- DELETE FROM approval_cc_records;
-- DELETE FROM approval_tasks; 
-- DELETE FROM approval_workflows;
-- DROP TABLE IF EXISTS approval_cc_records;
-- DROP TABLE IF EXISTS approval_tasks;
-- DROP TABLE IF EXISTS approval_workflows; 