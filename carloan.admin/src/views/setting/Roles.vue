<template>
  <div class="roles">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="w200 mr10 mb10"
        placeholder="角色名称/代码"
        clearable
      />
      <el-select
        v-model="listQuery.type"
        class="w150 mr10 mb10"
        placeholder="角色类型"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option
          v-for="type in roleTypes"
          :key="type.value"
          :label="type.label"
          :value="type.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.status"
        class="w150 mr10 mb10"
        placeholder="状态"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option label="启用" value="1" />
        <el-option label="禁用" value="0" />
      </el-select>
      <el-button
        class="filter-item mb10 mr10"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item mb10"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增角色
      </el-button>
    </div>

    <el-table
      :key="'roles'"
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="角色名称" prop="name" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="角色代码" prop="code" align="center">
        <template slot-scope="scope">
          <code style="font-size: 12px;">{{ scope.row.code }}</code>
        </template>
      </el-table-column>

      <el-table-column label="类型" prop="type" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.type === 'admin' ? 'danger' : (scope.row.type === 'finance' ? 'warning' : 'success')"
            size="small"
          >
            {{ getRoleTypeLabel(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="描述" prop="description" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.description" placement="top" :disabled="!scope.row.description || scope.row.description.length < 20">
            <span>{{ scope.row.description ? (scope.row.description.length > 20 ? scope.row.description.substring(0, 20) + '...' : scope.row.description) : '-' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="用户数量" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="`管理员: ${scope.row.admin_count || 0}个, 业务员: ${scope.row.user_count || 0}个`" placement="top">
            <span style="cursor: pointer;">{{ scope.row.total_count || 0 }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="排序" prop="sort_order" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.sort_order }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            :disabled="scope.row.code === 'super_admin'"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="320"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="mini"
            @click="handleViewPermissions(row)"
          >
            权限
          </el-button>
          <el-button
            type="success"
            size="mini"
            @click="handleCopy(row)"
          >
            复制
          </el-button>
          <el-button
            v-if="row.code !== 'super_admin'"
            type="danger"
            size="mini"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <!-- 角色表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="100px"
        style="padding: 0 10px;"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="temp.name" placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色代码" prop="code">
              <el-input
                v-model="temp.code"
                placeholder="请输入角色代码"
                :disabled="temp.code === 'super_admin'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="角色类型" prop="type">
              <el-select v-model="temp.type" placeholder="请选择角色类型" style="width: 100%">
                <el-option
                  v-for="type in roleTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number
                v-model="temp.sort_order"
                :min="0"
                :max="999"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-form-item label="权限配置" prop="permissions">
          <div style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 15px; max-height: 300px; overflow-y: auto;">
            <el-checkbox
              v-model="allPermissionsSelected"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              style="margin-bottom: 15px; font-weight: bold;"
            >
              全选
            </el-checkbox>

            <div v-for="(groupPerms, groupName) in groupedPermissions" :key="groupName" style="margin-bottom: 5px;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #606266;">
                {{ groupName }}
              </div>
              <el-checkbox-group v-model="temp.permissions" style="margin-left: 20px;">
                <div v-for="perm in (groupPerms || [])" :key="perm.code" style="margin-bottom: 5px;">
                  <el-checkbox :label="perm.code" style="width: 100%;">
                    <span style="margin-left: 5px;">{{ perm.name }}</span>
                    <span v-if="perm.description" style="color: #909399; font-size: 12px; margin-left: 10px;">
                      ({{ perm.description }})
                    </span>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 复制角色对话框 -->
    <el-dialog
      title="复制角色"
      :visible.sync="copyDialogVisible"
      width="500px"
    >
      <el-form
        ref="copyForm"
        :model="copyTemp"
        :rules="copyRules"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="copyTemp.name" placeholder="请输入新角色名称" />
        </el-form-item>
        <el-form-item label="角色代码" prop="code">
          <el-input v-model="copyTemp.code" placeholder="请输入新角色代码" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="copyTemp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="copyDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmCopy">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 权限查看对话框 -->
    <el-dialog
      title="角色权限详情"
      :visible.sync="permissionDialogVisible"
      width="600px"
    >
      <div v-if="currentRolePermissions.length > 0">
        <div v-for="(groupPerms, groupName) in currentGroupedPermissions" :key="groupName" style="margin-bottom: 20px;">
          <div style="font-weight: bold; margin-bottom: 10px; color: #409EFF;">
            {{ groupName }}
          </div>
          <div style="margin-left: 20px;">
            <el-tag
              v-for="perm in groupPerms"
              :key="perm.code"
              style="margin: 2px 5px 2px 0;"
              size="small"
            >
              {{ perm.name }}
            </el-tag>
          </div>
        </div>
      </div>
      <div v-else style="text-align: center; color: #909399; padding: 40px;">
        该角色暂无权限
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchRoleList,
  createRole,
  updateRole,
  deleteRole,
  fetchRolePermissions,
  fetchRoleTypes,
  toggleRoleStatus,
  copyRole
} from '@/api/permission'
import Pagination from '@/components/Pagination'

export default {
  name: 'Roles',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      loading: true,
      listQuery: {
        page: 1,
        page_size: 20,
        keyword: '',
        type: '',
        status: ''
      },
      roleTypes: [],
      allPermissions: [],
      groupedPermissions: {},
      dialogVisible: false,
      dialogStatus: '',
      dialogTitle: '',
      temp: {
        id: undefined,
        name: '',
        code: '',
        description: '',
        type: 'user',
        permissions: [],
        status: 1,
        sort_order: 0
      },
      rules: {
        name: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '角色代码不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '角色代码长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择角色类型', trigger: 'change' }
        ]
      },
      copyDialogVisible: false,
      copyTemp: {
        name: '',
        code: '',
        description: ''
      },
      copyRules: {
        name: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '角色代码不能为空', trigger: 'blur' }
        ]
      },
      copyId: null,
      permissionDialogVisible: false,
      currentRolePermissions: [],
      currentGroupedPermissions: {}
    }
  },
  computed: {
    allPermissionsSelected: {
      get() {
        const tempPermissions = this.temp.permissions || []
        const allPermissions = this.allPermissions || []
        return tempPermissions.length === allPermissions.length && allPermissions.length > 0
      },
      set(val) {
        const allPermissions = this.allPermissions || []
        if (val) {
          this.temp.permissions = allPermissions.map(p => p.code)
        } else {
          this.temp.permissions = []
        }
      }
    },
    isIndeterminate() {
      const tempPermissions = this.temp.permissions || []
      const allPermissions = this.allPermissions || []
      const checkedCount = tempPermissions.length
      return checkedCount > 0 && checkedCount < allPermissions.length
    }
  },
  created() {
    this.getList()
    this.getRoleTypes()
    this.getPermissions()
  },
  methods: {
    getList() {
      this.loading = true
      fetchRoleList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    getRoleTypes() {
      fetchRoleTypes().then(response => {
        this.roleTypes = response.data
      })
    },
    getPermissions() {
      fetchRolePermissions().then(response => {
        console.log('权限接口返回数据:', response)

        // 适配不同的数据结构
        let data = response.data

        // 如果是Dingo API包装的结构，尝试获取实际数据
        if (data && data.data) {
          data = data.data
        }

        console.log('处理后的数据:', data)

        // 标准的数据结构
        this.allPermissions = data.permissions || []
        this.groupedPermissions = data.grouped_permissions || {}

        console.log('设置后的权限数据:', {
          allPermissions: this.allPermissions,
          groupedPermissions: this.groupedPermissions
        })
      }).catch(error => {
        console.error('获取权限列表失败:', error)
        this.allPermissions = []
        this.groupedPermissions = {}
      })
    },
    getRoleTypeLabel(type) {
      const typeObj = this.roleTypes.find(t => t.value === type)
      return typeObj ? typeObj.label : type
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        code: '',
        description: '',
        type: 'user',
        permissions: [],
        status: 1,
        sort_order: 0
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogTitle = '创建角色'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createRole(this.temp).then(response => {
            this.list.unshift(response.data)
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      // 处理权限数组，确保始终是数组
      if (Array.isArray(row.permissions)) {
        this.temp.permissions = [...row.permissions]
      } else {
        this.temp.permissions = []
      }
      this.dialogStatus = 'update'
      this.dialogTitle = '编辑角色'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateRole(tempData.id, tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRole(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleStatusChange(row) {
      toggleRoleStatus(row.id, row.status).then(() => {
        this.$notify({
          title: '成功',
          message: row.status ? '启用成功' : '禁用成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        // 恢复状态
        row.status = row.status === 1 ? 0 : 1
      })
    },
    handleCopy(row) {
      this.copyId = row.id
      this.copyTemp = {
        name: row.name + ' (复制)',
        code: row.code + '_copy',
        description: row.description
      }
      this.copyDialogVisible = true
    },
    confirmCopy() {
      this.$refs['copyForm'].validate((valid) => {
        if (valid) {
          copyRole(this.copyId, this.copyTemp).then(() => {
            this.copyDialogVisible = false
            this.$notify({
              title: '成功',
              message: '角色复制成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleViewPermissions(row) {
      if (row.permission_details && Array.isArray(row.permission_details)) {
        this.currentRolePermissions = row.permission_details
      } else {
        const allPermissions = this.allPermissions || []
        const rowPermissions = row.permissions || []
        this.currentRolePermissions = allPermissions.filter(p =>
          rowPermissions.includes(p.code)
        )
      }

      // 按组分类
      this.currentGroupedPermissions = {}
      const rolePermissions = this.currentRolePermissions || []
      rolePermissions.forEach(perm => {
        const group = perm.group_name || '其他'
        if (!this.currentGroupedPermissions[group]) {
          this.currentGroupedPermissions[group] = []
        }
        this.currentGroupedPermissions[group].push(perm)
      })

      this.permissionDialogVisible = true
    },
    handleCheckAllChange(val) {
      const allPermissions = this.allPermissions || []
      this.temp.permissions = val ? allPermissions.map(p => p.code) : []
    },
    resetForm() {
      this.$refs['dataForm'] && this.$refs['dataForm'].resetFields()
    }
  }
}
</script>

<style scoped>
.roles {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}
</style>
