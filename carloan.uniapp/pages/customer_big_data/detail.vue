<template>
	<!-- 客户大数据详情 -->
	<view class="customer-big-data-detail">
		<navbar title="客户详情" />
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading" :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载完成' }" />
		</view>
		
		<!-- 详情内容 -->
		<view v-else-if="customerDetail" class="detail-content">
			<!-- 客户基本信息 -->
			<view class="info-card">
				<view class="card-header">
					<view class="card-title">客户信息</view>
					<view 
						class="status-badge" 
						:class="getStatusClass(customerDetail.status, customerDetail.risk_level)"
					>
						<view v-if="customerDetail.status === 'querying'">
							查询中
						</view>
						<view v-else-if="customerDetail.status === 'failed'">
							查询失败
						</view>
						<view v-else-if="customerDetail.status === 'completed' && customerDetail.risk_score" class="score-display">
							<text>{{ customerDetail.risk_score }}</text>
							<text>分</text>
						</view>
						<view v-else>
							{{ customerDetail.status_text }}
						</view>
					</view>
				</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">客户姓名：</view>
						<view class="value">{{ customerDetail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="label">身份证号：</view>
						<view class="value">{{ customerDetail.customer_id_card }}</view>
					</view>
					<view class="info-item">
						<view class="label">手机号码：</view>
						<view class="value">{{ customerDetail.customer_phone || '--' }}</view>
					</view>
					<view class="info-item">
						<view class="label">查询时间：</view>
						<view class="value">{{ customerDetail.query_time || customerDetail.created_time }}</view>
					</view>
					<view class="info-item" v-if="customerDetail.risk_level">
						<view class="label">风险等级：</view>
						<view class="value" :class="getRiskLevelClass(customerDetail.risk_level)">
							{{ getRiskLevelText(customerDetail.risk_level) }}
						</view>
					</view>
					<view class="info-item" v-if="customerDetail.completed_time">
						<view class="label">查询完成时间：</view>
						<view class="value">{{ customerDetail.completed_time }}</view>
					</view>
				</view>
			</view>

			<!-- 风险评分详情 -->
			<view class="info-card" v-if="customerDetail.status === 'completed' && customerDetail.risk_score">
				<view class="card-title">风险评分详情</view>
				<view class="risk-score-detail">
					<!-- 总分 -->
					<view class="score-summary">
						<view class="score-number" :class="getRiskLevelClass(customerDetail.risk_level)">
							{{ customerDetail.risk_score }}
						</view>
						<view class="score-label">综合风险评分</view>
						<view class="score-description">
							{{ getRiskDescription(customerDetail.risk_level) }}
						</view>
					</view>
					
					<!-- 分项评分 -->
					<view class="score-items" v-if="customerDetail.score_details">
						<view 
							class="score-item"
							v-for="(item, index) in customerDetail.score_details"
							:key="index"
						>
							<view class="item-name">{{ item.name }}</view>
							<view class="item-score" :class="getScoreClass(item.score)">
								{{ item.score }}分
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 风险因子 -->
			<view class="info-card" v-if="customerDetail.risk_factors && customerDetail.risk_factors.length > 0">
				<view class="card-title">主要风险因子</view>
				<view class="risk-factors">
					<view 
						class="risk-factor-item"
						v-for="(factor, index) in customerDetail.risk_factors"
						:key="index"
						:class="getRiskFactorClass(factor.level)"
					>
						<view class="factor-icon">
							<image 
								class="icon"
								:src="getRiskFactorIcon(factor.level)"
							/>
						</view>
						<view class="factor-content">
							<view class="factor-name">{{ factor.name }}</view>
							<view class="factor-description">{{ factor.description }}</view>
						</view>
						<view class="factor-level">{{ factor.level_text }}</view>
					</view>
				</view>
			</view>

			<!-- 查询报告 -->
			<view class="info-card" v-if="customerDetail.report_url">
				<view class="card-title">详细报告</view>
				<view class="report-section">
					<view class="report-item" @click="viewReport">
						<view class="report-icon">
							<image 
								class="icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-report.png"
							/>
						</view>
						<view class="report-info">
							<view class="report-name">客户风险分析报告</view>
							<view class="report-desc">详细的风险评估报告，包含多维度数据分析</view>
						</view>
						<view class="report-action">
							<image 
								class="action-icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 查询失败信息 -->
			<view class="info-card" v-if="customerDetail.status === 'failed' && customerDetail.error_message">
				<view class="card-title">查询失败原因</view>
				<view class="error-info">
					<view class="error-message">{{ customerDetail.error_message }}</view>
					<view class="error-suggestion" v-if="customerDetail.error_suggestion">
						建议：{{ customerDetail.error_suggestion }}
					</view>
				</view>
			</view>

			<!-- 操作记录 -->
			<view class="info-card" v-if="customerDetail.operation_log && customerDetail.operation_log.length > 0">
				<view class="card-title">操作记录</view>
				<view class="operation-list">
					<view 
						class="operation-item"
						v-for="(log, index) in customerDetail.operation_log"
						:key="index"
					>
						<view class="operation-dot"></view>
						<view class="operation-content">
							<view class="operation-desc">{{ log.description }}</view>
							<view class="operation-time">{{ log.created_at }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="action-bar" v-if="showActionButtons">
				<button 
					class="btn btn-white" 
					@click="onRequery"
					v-if="customerDetail.can_requery"
				>
					重新查询
				</button>
				<button 
					class="btn btn-primary" 
					@click="goCreateBusiness"
					v-if="customerDetail.can_create_business"
				>
					创建业务
				</button>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-else class="error-container">
			<view class="error-icon">
				<image src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/error.png" />
			</view>
			<view class="error-text">加载失败，请重试</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 响应式数据
const customerDetail = ref(null)
const loading = ref(true)
const customerId = ref('')

// 计算属性
const showActionButtons = computed(() => {
	return customerDetail.value && (customerDetail.value.can_requery || customerDetail.value.can_create_business)
})

// 页面加载时获取参数
onLoad((option) => {
	if (option.id) {
		customerId.value = option.id
		loadDetail()
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		
		const response = await request.get(`/customer-big-data/${customerId.value}`)
		
		console.log('客户大数据详情API Response:', response)
		
		if (response && response.status_code === 200) {
			customerDetail.value = response.data
		} else {
			console.error('获取客户详情失败:', response)
			const errorMessage = response?.message || '获取详情失败'
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('获取客户详情失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 获取状态样式类
const getStatusClass = (status, riskLevel) => {
	if (status === 'querying') {
		return 'warning'
	} else if (status === 'failed') {
		return 'error'
	} else if (status === 'completed' && riskLevel) {
		const classMap = {
			'low': 'score-blue',
			'medium': 'score-yellow',
			'high': 'score-red'
		}
		return classMap[riskLevel] || 'default'
	}
	return 'default'
}

// 获取风险等级样式类
const getRiskLevelClass = (riskLevel) => {
	const classMap = {
		'low': 'risk-low',
		'medium': 'risk-medium',
		'high': 'risk-high'
	}
	return classMap[riskLevel] || ''
}

// 获取风险等级文本
const getRiskLevelText = (riskLevel) => {
	const textMap = {
		'low': '低风险',
		'medium': '中风险',
		'high': '高风险'
	}
	return textMap[riskLevel] || '未知'
}

// 获取风险描述
const getRiskDescription = (riskLevel) => {
	const descMap = {
		'low': '客户信用状况良好，风险较低',
		'medium': '客户信用状况一般，需谨慎评估',
		'high': '客户存在较高风险，建议慎重考虑'
	}
	return descMap[riskLevel] || ''
}

// 获取评分样式类
const getScoreClass = (score) => {
	if (score >= 80) return 'score-high'
	if (score >= 60) return 'score-medium'
	return 'score-low'
}

// 获取风险因子样式类
const getRiskFactorClass = (level) => {
	const classMap = {
		'high': 'factor-high',
		'medium': 'factor-medium',
		'low': 'factor-low'
	}
	return classMap[level] || ''
}

// 获取风险因子图标
const getRiskFactorIcon = (level) => {
	const iconMap = {
		'high': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-risk-high.png',
		'medium': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-risk-medium.png',
		'low': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-risk-low.png'
	}
	return iconMap[level] || 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-risk-default.png'
}

// 查看报告
const viewReport = () => {
	if (customerDetail.value?.report_url) {
		uni.navigateTo({
			url: `/pages/webview/index?url=${encodeURIComponent(customerDetail.value.report_url)}&title=${encodeURIComponent('风险分析报告')}`
		})
	}
}

// 重新查询
const onRequery = async () => {
	try {
		const result = await uni.showModal({
			title: '确认重新查询',
			content: '确定要重新查询该客户的风险数据吗？',
			confirmText: '确认',
			cancelText: '取消'
		})
		
		if (!result.confirm) {
			return
		}
		
		uni.showLoading({ title: '查询中...' })
		
		const response = await request.post(`/customer-big-data/${customerId.value}/requery`)
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '重新查询已启动',
				icon: 'success'
			})
			
			// 刷新详情
			loadDetail()
		} else {
			const errorMessage = response.message || '重新查询失败'
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('重新查询失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}

// 创建业务
const goCreateBusiness = () => {
	uni.navigateTo({
		url: "/pages/business/basic",
	})
}
</script>

<style lang="scss" scoped>
.customer-big-data-detail {
	background: #f3f3f3;
	min-height: 100vh;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading-container {
	padding: 100rpx 0;
	text-align: center;
}

.error-container {
	padding: 100rpx 30rpx;
	text-align: center;
	
	.error-icon {
		margin-bottom: 30rpx;
		
		image {
			width: 120rpx;
			height: 120rpx;
		}
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 30rpx;
	}
}

.detail-content {
	padding: 20rpx 30rpx;
}

.info-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 30rpx 20rpx;
		
		.card-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		
		.status-badge {
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			text-align: center;
			
			&.warning {
				background: #fff7e6;
				color: #fa8c16;
			}
			
			&.error {
				background: #fff2f0;
				color: #ff4d4f;
			}
			
			&.score-red {
				background: #fff2f0;
				color: #ff4d4f;
			}
			
			&.score-yellow {
				background: #fff7e6;
				color: #fa8c16;
			}
			
			&.score-blue {
				background: #e6f7ff;
				color: #1890ff;
			}
			
			.score-display {
				display: flex;
				align-items: center;
				gap: 2rpx;
			}
		}
	}
	
	.card-title {
		padding: 30rpx 30rpx 20rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
	}
	
	.info-list {
		padding: 0 30rpx 30rpx;
		
		.info-item {
			display: flex;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				width: 160rpx;
				font-size: 28rpx;
				color: #666;
				flex-shrink: 0;
			}
			
			.value {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				
				&.risk-low {
					color: #52c41a;
				}
				
				&.risk-medium {
					color: #fa8c16;
				}
				
				&.risk-high {
					color: #ff4d4f;
				}
			}
		}
	}
}

.risk-score-detail {
	padding: 0 30rpx 30rpx;
	
	.score-summary {
		text-align: center;
		padding: 40rpx 0;
		border-bottom: 2rpx solid #f0f0f0;
		margin-bottom: 30rpx;
		
		.score-number {
			font-size: 80rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
			
			&.risk-low {
				color: #52c41a;
			}
			
			&.risk-medium {
				color: #fa8c16;
			}
			
			&.risk-high {
				color: #ff4d4f;
			}
		}
		
		.score-label {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 10rpx;
		}
		
		.score-description {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.score-items {
		.score-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 2rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.item-name {
				font-size: 28rpx;
				color: #333;
			}
			
			.item-score {
				font-size: 28rpx;
				font-weight: 500;
				
				&.score-high {
					color: #52c41a;
				}
				
				&.score-medium {
					color: #fa8c16;
				}
				
				&.score-low {
					color: #ff4d4f;
				}
			}
		}
	}
}

.risk-factors {
	padding: 0 30rpx 30rpx;
	
	.risk-factor-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 2rpx solid #f5f5f5;
		
		&:last-child {
			border-bottom: none;
		}
		
		.factor-icon {
			width: 60rpx;
			height: 60rpx;
			margin-right: 20rpx;
			
			.icon {
				width: 100%;
				height: 100%;
			}
		}
		
		.factor-content {
			flex: 1;
			
			.factor-name {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.factor-description {
				font-size: 24rpx;
				color: #666;
			}
		}
		
		.factor-level {
			font-size: 24rpx;
			padding: 6rpx 12rpx;
			border-radius: 6rpx;
		}
		
		&.factor-high .factor-level {
			background: #fff2f0;
			color: #ff4d4f;
		}
		
		&.factor-medium .factor-level {
			background: #fff7e6;
			color: #fa8c16;
		}
		
		&.factor-low .factor-level {
			background: #f6ffed;
			color: #52c41a;
		}
	}
}

.report-section {
	padding: 0 30rpx 30rpx;
	
	.report-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		
		.report-icon {
			width: 60rpx;
			height: 60rpx;
			margin-right: 20rpx;
			
			.icon {
				width: 100%;
				height: 100%;
			}
		}
		
		.report-info {
			flex: 1;
			
			.report-name {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.report-desc {
				font-size: 24rpx;
				color: #666;
			}
		}
		
		.report-action {
			width: 40rpx;
			height: 40rpx;
			
			.action-icon {
				width: 100%;
				height: 100%;
			}
		}
	}
}

.error-info {
	padding: 0 30rpx 30rpx;
	
	.error-message {
		font-size: 28rpx;
		color: #ff4d4f;
		margin-bottom: 16rpx;
	}
	
	.error-suggestion {
		font-size: 24rpx;
		color: #666;
	}
}

.operation-list {
	padding: 0 30rpx 30rpx;
	
	.operation-item {
		display: flex;
		align-items: flex-start;
		padding-bottom: 24rpx;
		
		&:last-child {
			padding-bottom: 0;
		}
		
		.operation-dot {
			width: 12rpx;
			height: 12rpx;
			background: #1890ff;
			border-radius: 50%;
			margin-top: 12rpx;
			margin-right: 20rpx;
			flex-shrink: 0;
		}
		
		.operation-content {
			flex: 1;
			
			.operation-desc {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.operation-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
	border-top: 2rpx solid #f0f0f0;
	display: flex;
	gap: 20rpx;
	
	.btn {
		/* 重置按钮默认样式 */
		appearance: none;
		-webkit-appearance: none;
		outline: none;
		box-sizing: border-box;
		
		/* 统一尺寸和布局 */
		flex: 1;
		height: 88rpx;
		border-radius: 12rpx;
		border: none;
		
		/* 统一文字样式 */
		font-size: 32rpx;
		line-height: 1;
		text-align: center;
		white-space: nowrap;
		
		/* 统一交互效果 */
		cursor: pointer;
		transition: opacity 0.2s;
		
		&:active {
			opacity: 0.8;
		}
		
		&.btn-white {
			background: #fff;
			color: #666;
			border: 2rpx solid #d9d9d9;
		}
		
		&.btn-primary {
			background: #333;
			color: #fff;
		}
	}
}
</style> 