import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const financeRouter = {
  path: '/finance',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Finance',
  meta: {
    title: '财务管理',
    icon: 'el-icon-money'
  },
  children: [
    {
      path: 'lending',
      component: PlaceholderPage,
      name: 'FinanceLending',
      meta: { title: '放款管理' }
    },
    {
      path: 'repayment',
      component: PlaceholderPage,
      name: 'FinanceRepayment',
      meta: { title: '还款管理' }
    },
    {
      path: 'reports',
      component: PlaceholderPage,
      name: 'FinanceReports',
      meta: { title: '财务报表' }
    },
    {
      path: 'overdue',
      component: PlaceholderPage,
      name: 'FinanceOverdue',
      meta: { title: '逾期管理' }
    },
    {
      path: 'lending/process/:id',
      component: PlaceholderPage,
      name: 'FinanceLendingProcess',
      meta: { title: '放款处理', hidden: true }
    },
    {
      path: 'repayment/detail/:id',
      component: PlaceholderPage,
      name: 'FinanceRepaymentDetail',
      meta: { title: '还款详情', hidden: true }
    }
  ]
}

export default financeRouter
