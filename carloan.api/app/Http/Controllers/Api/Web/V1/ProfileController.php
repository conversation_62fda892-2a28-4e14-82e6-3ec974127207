<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\User;
use App\Models\BusinessApplication;
use App\Models\ApprovalTask;
use App\Models\ApprovalCcRecord;
use App\Transformers\Web\UserTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use App\Traits\oss;

class ProfileController extends BaseController
{
    use oss;
    /**
     * 获取个人中心信息
     * 包含用户基本信息和统计数据
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfileInfo()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 刷新用户信息
            $user = User::with('channel')->find($user->id);
            
            // 获取统计数据
            $statistics = $this->getUserStatistics($user->id);

            $data = [
                // 用户基本信息
                'user_info' => [
                    'id' => $user->id,
                    'name' => $user->name ?: $user->nickname ?: '未设置名称',
                    'phone' => $user->phone,
                    'nickname' => $user->nickname,
                    'avatar' => $user->avatar,
                    'channel_code' => $user->channel_code,
                    'channel_name' => $user->channel ? $user->channel->name : '',
                    'register_time' => $user->register_time ? $user->register_time->format('Y-m-d H:i:s') : '',
                    'login_time' => $user->login_time ? $user->login_time->format('Y-m-d H:i:s') : ''
                ],
                
                // 工作统计
                'work_statistics' => [
                    'pending_approval_count' => $statistics['pending_approval_count'],
                    'cc_count' => $statistics['cc_count'], 
                    'my_application_count' => $statistics['my_application_count']
                ],

                // 提醒统计
                'reminder_statistics' => [
                    'pending_insurance' => $statistics['pending_insurance'] ?? 0,
                    'pending_inspection' => $statistics['pending_inspection'] ?? 0,
                    'pending_maintenance' => $statistics['pending_maintenance'] ?? 0,
                    'gps_offline' => $statistics['gps_offline'] ?? 0,
                    'overdue_payment' => $statistics['overdue_payment'] ?? 0
                ],

                // 今日工作概览
                'today_overview' => [
                    'today_applications' => $statistics['today_applications'],
                    'today_approvals' => $statistics['today_approvals'],
                    'week_applications' => $statistics['week_applications'],
                    'month_applications' => $statistics['month_applications']
                ]
            ];

            return $this->successHandler('获取成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取个人中心信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取个人信息失败', 500);
        }
    }

    /**
     * 获取用户统计数据
     * 
     * @param int $userId
     * @return array
     */
    private function getUserStatistics($userId)
    {
        // 待我审批数量
        $pendingApprovalCount = ApprovalTask::where('approver_id', $userId)
            ->where('status', ApprovalTask::STATUS_PENDING)
            ->count();

        // 抄送我的未读数量
        $ccCount = ApprovalCcRecord::where('cc_user_id', $userId)
            ->where('is_read', false)
            ->count();

        // 我发起的业务申请总数
        $myApplicationCount = BusinessApplication::where('user_id', $userId)
            ->whereNull('deleted_at')
            ->count();

        // 今日数据
        $today = Carbon::today();
        $todayApplications = BusinessApplication::where('user_id', $userId)
            ->whereDate('created_at', $today)
            ->count();

        $todayApprovals = ApprovalTask::where('approver_id', $userId)
            ->whereDate('completed_time', $today)
            ->whereIn('status', [ApprovalTask::STATUS_APPROVED, ApprovalTask::STATUS_REJECTED])
            ->count();

        // 本周数据
        $weekStart = Carbon::now()->startOfWeek();
        $weekApplications = BusinessApplication::where('user_id', $userId)
            ->where('created_at', '>=', $weekStart)
            ->count();

        // 本月数据  
        $monthStart = Carbon::now()->startOfMonth();
        $monthApplications = BusinessApplication::where('user_id', $userId)
            ->where('created_at', '>=', $monthStart)
            ->count();

        return [
            'pending_approval_count' => $pendingApprovalCount,
            'cc_count' => $ccCount,
            'my_application_count' => $myApplicationCount,
            'today_applications' => $todayApplications,
            'today_approvals' => $todayApprovals,
            'week_applications' => $weekApplications,
            'month_applications' => $monthApplications
        ];
    }

    /**
     * 获取待审批任务列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPendingApprovals(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 10);

            $tasks = ApprovalTask::with(['application', 'workflow'])
                ->where('approver_id', $user->id)
                ->where('status', ApprovalTask::STATUS_PENDING)
                ->orderBy('assigned_time', 'asc')
                ->paginate($perPage, ['*'], 'page', $page);

            $data = [
                'list' => $tasks->items(),
                'total' => $tasks->total(),
                'current_page' => $tasks->currentPage(),
                'per_page' => $tasks->perPage(),
                'last_page' => $tasks->lastPage()
            ];

            return $this->successHandler('获取成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取待审批任务失败: ' . $e->getMessage());
            return $this->errorHandler('获取失败', 500);
        }
    }

    /**
     * 获取抄送记录列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCcRecords(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 10);
            $isRead = $request->get('is_read'); // null=全部, 1=已读, 0=未读

            $query = ApprovalCcRecord::with(['application', 'task'])
                ->where('cc_user_id', $user->id)
                ->orderBy('cc_time', 'desc');

            if ($isRead !== null) {
                $query->where('is_read', (bool)$isRead);
            }

            $records = $query->paginate($perPage, ['*'], 'page', $page);

            $data = [
                'list' => $records->items(),
                'total' => $records->total(),
                'current_page' => $records->currentPage(),
                'per_page' => $records->perPage(),
                'last_page' => $records->lastPage()
            ];

            return $this->successHandler('获取成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取抄送记录失败: ' . $e->getMessage());
            return $this->errorHandler('获取失败', 500);
        }
    }

    /**
     * 获取我发起的业务申请列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMyApplications(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 10);
            $status = $request->get('status'); // 可选筛选状态

            $query = BusinessApplication::where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            if ($status) {
                $query->where('status', $status);
            }

            $applications = $query->paginate($perPage, ['*'], 'page', $page);

            $data = [
                'list' => $applications->items(),
                'total' => $applications->total(),
                'current_page' => $applications->currentPage(),
                'per_page' => $applications->perPage(),
                'last_page' => $applications->lastPage()
            ];

            return $this->successHandler('获取成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取我的申请失败: ' . $e->getMessage());
            return $this->errorHandler('获取失败', 500);
        }
    }

    /**
     * 标记抄送记录为已读
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markCcAsRead(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $recordIds = $request->get('record_ids', []); // 可选，指定记录ID数组

            if (empty($recordIds)) {
                // 标记所有未读为已读
                ApprovalCcRecord::batchMarkAsRead($user->id);
                $message = '已标记所有抄送为已读';
            } else {
                // 标记指定记录为已读
                ApprovalCcRecord::batchMarkAsRead($user->id, $recordIds);
                $message = '已标记选中抄送为已读';
            }

            return $this->successHandler($message, 200);

        } catch (\Exception $e) {
            \Log::error('标记抄送已读失败: ' . $e->getMessage());
            return $this->errorHandler('操作失败', 500);
        }
    }

    /**
     * 获取工作概览数据
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkOverview()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取最近7天的业务申请趋势
            $weeklyTrend = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $count = BusinessApplication::where('user_id', $user->id)
                    ->whereDate('created_at', $date)
                    ->count();
                
                $weeklyTrend[] = [
                    'date' => $date->format('m-d'),
                    'count' => $count
                ];
            }

            // 获取状态分布
            $statusDistribution = BusinessApplication::where('user_id', $user->id)
                ->select('status', 'status_text', DB::raw('count(*) as count'))
                ->groupBy('status', 'status_text')
                ->get()
                ->map(function($item) {
                    return [
                        'status' => $item->status,
                        'status_text' => $item->status_text,
                        'count' => $item->count
                    ];
                });

            $data = [
                'weekly_trend' => $weeklyTrend,
                'status_distribution' => $statusDistribution
            ];

            return $this->successHandler('获取成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取工作概览失败: ' . $e->getMessage());
            return $this->errorHandler('获取失败', 500);
        }
    }

    /**
     * 更新个人信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证输入
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:50',
                'nickname' => 'sometimes|required|string|max:50',
            ], [
                'name.required' => '姓名不能为空',
                'name.max' => '姓名不能超过50个字符',
                'nickname.required' => '昵称不能为空',
                'nickname.max' => '昵称不能超过50个字符',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            // 更新用户信息
            $updateData = [];
            if ($request->has('name')) {
                $updateData['name'] = trim($request->name);
            }
            if ($request->has('nickname')) {
                $updateData['nickname'] = trim($request->nickname);
            }

            if (!empty($updateData)) {
                $user->update($updateData);
            }

            return $this->successHandler('个人信息更新成功', 200, [
                'name' => $user->name,
                'nickname' => $user->nickname,
            ]);

        } catch (\Exception $e) {
            \Log::error('更新个人信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('更新失败', 500);
        }
    }

    /**
     * 上传头像
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAvatar(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证文件
            $validator = Validator::make($request->all(), [
                'avatar' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 最大5MB
            ], [
                'avatar.required' => '请选择头像文件',
                'avatar.image' => '头像必须是图片格式',
                'avatar.mimes' => '头像格式仅支持 jpeg、png、jpg',
                'avatar.max' => '头像大小不能超过5MB',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $file = $request->file('avatar');
            if (!$file) {
                return $this->errorHandler('头像文件上传失败', 422);
            }

            // 生成文件名
            $fileName = 'avatar_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = 'users/avatars/' . $fileName;

            try {
                // 上传到OSS
                $ossResult = $this->uploadToOss($file->getRealPath(), $path);
                
                if (!$ossResult['success']) {
                    return $this->errorHandler('头像上传失败: ' . $ossResult['message'], 500);
                }

                // 删除旧头像（如果存在）
                if ($user->avatar) {
                    try {
                        $oldAvatarPath = str_replace('oss:', '', $user->avatar);
                        $this->deleteFromOss($oldAvatarPath);
                    } catch (\Exception $e) {
                        \Log::warning('删除旧头像失败: ' . $e->getMessage());
                    }
                }

                // 更新用户头像
                $user->update([
                    'avatar' => 'oss:' . $path
                ]);

                // 生成头像访问URL
                $avatarUrl = $this->getOssPrivateUrl('oss', $path);

                return $this->successHandler('头像上传成功', 200, [
                    'avatar' => 'oss:' . $path,
                    'avatar_url' => $avatarUrl
                ]);

            } catch (\Exception $e) {
                \Log::error('OSS上传失败: ' . $e->getMessage());
                return $this->errorHandler('头像上传失败', 500);
            }

        } catch (\Exception $e) {
            \Log::error('头像上传失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('头像上传失败', 500);
        }
    }

    /**
     * 上传文件到OSS
     * 
     * @param string $filePath
     * @param string $ossPath
     * @return array
     */
    private function uploadToOss($filePath, $ossPath)
    {
        try {
            // 这里调用OSS上传逻辑
            // 具体实现需要根据项目中的OSS配置来处理
            
            // 模拟上传成功
            return [
                'success' => true,
                'path' => $ossPath,
                'message' => '上传成功'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 从OSS删除文件
     * 
     * @param string $ossPath
     * @return bool
     */
    private function deleteFromOss($ossPath)
    {
        try {
            // 这里调用OSS删除逻辑
            // 具体实现需要根据项目中的OSS配置来处理
            
            return true;
            
        } catch (\Exception $e) {
            \Log::warning('OSS删除文件失败: ' . $e->getMessage());
            return false;
        }
    }
}
