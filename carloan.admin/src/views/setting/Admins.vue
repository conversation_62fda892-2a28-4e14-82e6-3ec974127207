<template>
  <div class="admins">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        class="w200 mr10 mb10"
        placeholder="用户名/姓名/手机号"
        clearable
      />
      <el-select
        v-model="listQuery.role_id"
        class="w200 mr10 mb10"
        placeholder="角色"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option
          v-for="role in adminRoles"
          :key="role.id"
          :label="role.name"
          :value="role.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.status"
        class="w150 mr10 mb10"
        placeholder="状态"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option label="正常" value="1" />
        <el-option label="禁用" value="0" />
      </el-select>
      <el-button
        class="filter-item mb10 mr10"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item mb10"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增管理员
      </el-button>
    </div>

    <el-table
      :key="'admins'"
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户名" prop="username" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>

      <el-table-column label="姓名" prop="name" align="center" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="邮箱" prop="email" align="center" min-width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.email || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="手机号" prop="phone" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.phone || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="角色" align="center" min-width="120">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.role"
            :type="scope.row.role.type === 'admin' ? 'danger' : 'warning'"
            size="small"
          >
            {{ scope.row.role.name }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="权限" align="center" min-width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.admin_permissions && scope.row.admin_permissions.length > 0">
            <el-tag
              v-for="(perm, index) in scope.row.admin_permissions.slice(0, 2)"
              :key="index"
              size="mini"
              style="margin: 1px;"
            >
              {{ perm }}
            </el-tag>
            <el-tag
              v-if="scope.row.admin_permissions.length > 2"
              size="mini"
              type="info"
              style="margin: 1px;"
            >
              +{{ scope.row.admin_permissions.length - 2 }}
            </el-tag>
          </div>
          <span v-else>继承角色权限</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.created_at }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="320"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="warning"
            size="mini"
            @click="handleChangePassword(row)"
          >
            改密码
          </el-button>
          <el-button
            type="info"
            size="mini"
            @click="handleViewDetail(row)"
          >
            详情
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <!-- 管理员表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-position="left"
        label-width="100px"
        style="padding: 0 10px;"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input v-model="temp.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input v-model="temp.name" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="temp.email" placeholder="请输入邮箱 (可选)" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="temp.phone" placeholder="请输入手机号 (可选)" />
        </el-form-item>

        <el-form-item label="角色" prop="role_id">
          <el-select v-model="temp.role_id" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in adminRoles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            >
              <span style="float: left">{{ role.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ role.type }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="额外权限" prop="admin_permissions">
          <el-select
            v-model="temp.admin_permissions"
            multiple
            filterable
            allow-create
            placeholder="请选择额外权限 (可选)"
            style="width: 100%"
          >
            <el-option
              v-for="perm in allPermissions"
              :key="perm.code"
              :label="perm.name"
              :value="perm.code"
            >
              <span style="float: left">{{ perm.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ perm.code }}</span>
            </el-option>
          </el-select>
          <div class="form-tip">额外权限将在角色权限基础上叠加</div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="passwordDialogVisible"
      width="400px"
    >
      <el-form
        ref="passwordForm"
        :model="passwordTemp"
        :rules="passwordRules"
        label-position="left"
        label-width="80px"
      >
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="passwordTemp.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="password_confirmation">
          <el-input
            v-model="passwordTemp.password_confirmation"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="confirmChangePassword">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 详情查看对话框 -->
    <el-dialog
      title="管理员详情"
      :visible.sync="detailDialogVisible"
      width="600px"
    >
      <el-descriptions v-if="currentAdmin" :column="2" border>
        <el-descriptions-item label="ID">{{ currentAdmin.id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ currentAdmin.username }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ currentAdmin.name }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ currentAdmin.email || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentAdmin.phone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="角色">
          <el-tag v-if="currentAdmin.role" :type="currentAdmin.role.type === 'admin' ? 'danger' : 'warning'">
            {{ currentAdmin.role.name }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentAdmin.status === 1 ? 'success' : 'danger'">
            {{ currentAdmin.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentAdmin.created_at }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentAdmin.updated_at }}</el-descriptions-item>
        <el-descriptions-item label="拥有权限" :span="2">
            <div v-if="currentAdmin.permissions && currentAdmin.permissions.length > 0"
                 style="max-width: 100%; max-height: 200px; overflow-y: auto; overflow-x: hidden; display: flex; flex-wrap: wrap; gap: 4px;">
              <el-tag
                v-for="perm in currentAdmin.permissions"
                :key="perm"
                size="small"
                style="flex-shrink: 0;"
              >
                {{ perm }}
              </el-tag>
           </div>
          <span v-else>无特殊权限</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchAdminList,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  changeAdminPassword,
  fetchAdminRoles,
  toggleAdminStatus,
  fetchAdmin
} from '@/api/permission'
import { fetchRolePermissions } from '@/api/permission'
import Pagination from '@/components/Pagination'

export default {
  name: 'Admins',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      loading: true,
      listQuery: {
        page: 1,
        page_size: 20,
        keyword: '',
        role_id: '',
        status: ''
      },
      adminRoles: [],
      allPermissions: [],
      dialogVisible: false,
      dialogStatus: '',
      dialogTitle: '',
      temp: {
        id: undefined,
        username: '',
        password: '',
        name: '',
        email: '',
        phone: '',
        role_id: '',
        admin_permissions: [],
        status: 1
      },
      rules: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
          { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        role_id: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      passwordDialogVisible: false,
      passwordTemp: {
        id: '',
        password: '',
        password_confirmation: ''
      },
      passwordRules: {
        password: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
        ],
        password_confirmation: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.passwordTemp.password) {
                callback(new Error('两次输入密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      detailDialogVisible: false,
      currentAdmin: null
    }
  },
  created() {
    this.getList()
    this.getAdminRoles()
    this.getAllPermissions()
  },
  methods: {
    getList() {
      this.loading = true
      fetchAdminList(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.loading = false
      })
    },
    getAdminRoles() {
      fetchAdminRoles().then(response => {
        this.adminRoles = response.data
      })
    },
    getAllPermissions() {
      fetchRolePermissions().then(response => {
        this.allPermissions = response.data.permissions
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        username: '',
        password: '',
        name: '',
        email: '',
        phone: '',
        role_id: '',
        admin_permissions: [],
        status: 1
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogTitle = '创建管理员'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createAdmin(this.temp).then(response => {
            this.list.unshift(response.data)
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleEdit(row) {
      this.temp = Object.assign({}, row)
      if (row.admin_permissions) {
        this.temp.admin_permissions = [...row.admin_permissions]
      } else {
        this.temp.admin_permissions = []
      }
      this.dialogStatus = 'update'
      this.dialogTitle = '编辑管理员'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          delete tempData.password // 编辑时不提交密码
          updateAdmin(tempData.id, tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该管理员, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAdmin(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleStatusChange(row) {
      toggleAdminStatus(row.id, row.status).then(() => {
        this.$notify({
          title: '成功',
          message: row.status ? '启用成功' : '禁用成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        // 恢复状态
        row.status = row.status === 1 ? 0 : 1
      })
    },
    handleChangePassword(row) {
      this.passwordTemp = {
        id: row.id,
        password: '',
        password_confirmation: ''
      }
      this.passwordDialogVisible = true
      this.$nextTick(() => {
        this.$refs['passwordForm'].clearValidate()
      })
    },
    confirmChangePassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          changeAdminPassword(this.passwordTemp.id, {
            password: this.passwordTemp.password,
            password_confirmation: this.passwordTemp.password_confirmation
          }).then(() => {
            this.passwordDialogVisible = false
            this.$notify({
              title: '成功',
              message: '密码修改成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleViewDetail(row) {
      fetchAdmin(row.id).then(response => {
        this.currentAdmin = response.data
        this.detailDialogVisible = true
      })
    },
    resetForm() {
      this.$refs['dataForm'] && this.$refs['dataForm'].resetFields()
    }
  }
}
</script>

<style scoped>
.admins {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
