<template>
  <div class="customer-list">
    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.phone"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="手机号"
      />
      <el-input
        v-model="listQuery.name"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="客户姓名"
      />
      <el-input
        v-model="listQuery.id_card"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;"
        placeholder="身份证号"
      />
      <el-select
        v-model="listQuery.status"
        class="filter-item"
        style="width: 200px; margin-right: 10px; margin-bottom: 10px;vertical-align: middle;"
        placeholder="状态"
        clearable
      >
        <el-option label="正常" value="active" />
        <el-option label="停用" value="inactive" />
        <el-option label="黑名单" value="blacklist" />
      </el-select>
      <el-date-picker
        v-model="listQuery.created_at"
        type="daterange"
        range-separator="至"
        start-placeholder="创建开始日期"
        end-placeholder="创建结束日期"
        style="margin-right: 10px; margin-bottom: 10px;vertical-align: middle;"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        style="margin-bottom: 10px;"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="客户姓名" prop="name" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.name || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="手机号" prop="phone" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.phone || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="身份证号" prop="id_card" align="center" min-width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.id_card ? hideIdCard(scope.row.id_card) : '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="性别" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ getGenderText(scope.row.gender) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            size="small"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.created_at) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-popconfirm
            title="确定删除这个客户吗？"
            @confirm="handleDelete(row)"
          >
            <el-button
              slot="reference"
              type="danger"
              size="mini"
            >
              删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.per_page"
      :page-count.sync="pageCount"
      :page-sizes="[20, 50, 100, 200]"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getCustomerList, deleteCustomer } from '@/api/customer'
import Pagination from '@/components/Pagination'

export default {
  name: 'CustomerList',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      pageCount: 0,
      loading: false,
      listQuery: {
        page: 1,
        per_page: 20,
        phone: '',
        name: '',
        id_card: '',
        status: '',
        created_at: null
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = { ...this.listQuery }

        // 处理日期范围
        if (params.created_at && params.created_at.length === 2) {
          params.created_at_start = params.created_at[0]
          params.created_at_end = params.created_at[1]
          delete params.created_at
        } else {
          delete params.created_at
        }

        const response = await getCustomerList(params)
        this.list = response.data
        this.total = response.meta.pagination.total
        this.pageCount = response.meta.pagination.total_pages
      } catch (error) {
        console.error('获取客户列表失败:', error)
        this.$message.error('获取客户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },



    // 删除客户
    async handleDelete(row) {
      try {
        await deleteCustomer(row.id)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        console.error('删除客户失败:', error)
        this.$message.error('删除客户失败')
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      return idCard.substring(0, 6) + '****' + idCard.substring(idCard.length - 4)
    },

    // 获取性别文本
    getGenderText(gender) {
      switch (gender) {
        case 1:
          return '男'
        case 2:
          return '女'
        default:
          return '未知'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'active':
          return '正常'
        case 'inactive':
          return '停用'
        case 'blacklist':
          return '黑名单'
        default:
          return '未知'
      }
    },

    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case 'active':
          return 'success'
        case 'inactive':
          return 'warning'
        case 'blacklist':
          return 'danger'
        default:
          return 'info'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.customer-list {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
