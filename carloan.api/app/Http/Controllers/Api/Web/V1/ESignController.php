<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Services\ESignService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ESignController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 处理e签宝回调
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function callback(Request $request)
    {
        try {
            Log::info('收到e签宝回调请求', $request->all());
            
            $eSignService = new ESignService();
            $eSignService->handleCallback($request->all());
            
            // 返回成功响应给e签宝
            return response()->json([
                'code' => 0,
                'message' => 'success'
            ]);
            
        } catch (\Exception $e) {
            Log::error('处理e签宝回调失败: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 即使处理失败也要返回成功，避免e签宝重复回调
            return response()->json([
                'code' => 0,
                'message' => 'received'
            ]);
        }
    }
} 