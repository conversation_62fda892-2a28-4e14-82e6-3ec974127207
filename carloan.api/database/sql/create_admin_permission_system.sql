-- 管理后台权限系统完整SQL
-- 创建时间: 2024-12-25
-- 说明: 支持渠道、销售、金融公司、平台管理员四种角色的权限管理系统

-- 1. 角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色类型:admin管理员,channel渠道,finance金融公司,platform平台',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_code_unique` (`code`),
  KEY `roles_type_index` (`type`),
  KEY `roles_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 2. 菜单表
CREATE TABLE IF NOT EXISTS `menus` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '父级菜单ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单代码',
  `path` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由路径',
  `component` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'menu' COMMENT '类型:menu菜单,button按钮',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `menus_code_unique` (`code`),
  KEY `menus_parent_id_index` (`parent_id`),
  KEY `menus_type_index` (`type`),
  KEY `menus_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';

-- 3. 权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限代码',
  `menu_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '关联菜单ID',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限描述',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'menu' COMMENT '权限类型:menu菜单,action操作',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_code_unique` (`code`),
  KEY `permissions_menu_id_index` (`menu_id`),
  KEY `permissions_type_index` (`type`),
  KEY `permissions_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 4. 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) UNSIGNED NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) UNSIGNED NOT NULL COMMENT '权限ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permissions_role_id_permission_id_unique` (`role_id`,`permission_id`),
  KEY `role_permissions_role_id_index` (`role_id`),
  KEY `role_permissions_permission_id_index` (`permission_id`),
  CONSTRAINT `role_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 5. 金融公司表
CREATE TABLE IF NOT EXISTS `finance_companies` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司代码',
  `contact_person` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司地址',
  `business_license` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '营业执照',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `cooperation_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '合作状态:active正常,suspended暂停,terminated终止',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `finance_companies_code_unique` (`code`),
  KEY `finance_companies_status_index` (`status`),
  KEY `finance_companies_cooperation_status_index` (`cooperation_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金融公司表';

-- 6. 渠道用户表（渠道登录账号）
CREATE TABLE IF NOT EXISTS `channel_users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `channel_id` bigint(20) UNSIGNED NOT NULL COMMENT '渠道ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `role_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '角色ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `channel_users_username_unique` (`username`),
  KEY `channel_users_channel_id_index` (`channel_id`),
  KEY `channel_users_role_id_index` (`role_id`),
  KEY `channel_users_status_index` (`status`),
  CONSTRAINT `channel_users_channel_id_foreign` FOREIGN KEY (`channel_id`) REFERENCES `channels` (`id`) ON DELETE CASCADE,
  CONSTRAINT `channel_users_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='渠道用户表';

-- 7. 金融公司用户表
CREATE TABLE IF NOT EXISTS `finance_users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `finance_company_id` bigint(20) UNSIGNED NOT NULL COMMENT '金融公司ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `role_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '角色ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `finance_users_username_unique` (`username`),
  KEY `finance_users_finance_company_id_index` (`finance_company_id`),
  KEY `finance_users_role_id_index` (`role_id`),
  KEY `finance_users_status_index` (`status`),
  CONSTRAINT `finance_users_finance_company_id_foreign` FOREIGN KEY (`finance_company_id`) REFERENCES `finance_companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `finance_users_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金融公司用户表';

-- 8. 管理员角色关联表
CREATE TABLE IF NOT EXISTS `admin_roles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` bigint(20) UNSIGNED NOT NULL COMMENT '管理员ID',
  `role_id` bigint(20) UNSIGNED NOT NULL COMMENT '角色ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_roles_admin_id_role_id_unique` (`admin_id`,`role_id`),
  KEY `admin_roles_admin_id_index` (`admin_id`),
  KEY `admin_roles_role_id_index` (`role_id`),
  CONSTRAINT `admin_roles_admin_id_foreign` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `admin_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色关联表';

-- 9. 插入基础角色数据
INSERT IGNORE INTO `roles` (`name`, `code`, `description`, `type`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 'platform', 1, 1, NOW(), NOW()),
('平台管理员', 'platform_admin', '平台管理员，负责平台整体运营管理', 'platform', 1, 2, NOW(), NOW()),
('金融公司管理员', 'finance_admin', '金融公司管理员，负责审批和风控', 'finance', 1, 3, NOW(), NOW()),
('金融公司操作员', 'finance_operator', '金融公司操作员，负责日常业务操作', 'finance', 1, 4, NOW(), NOW()),
('渠道管理员', 'channel_admin', '渠道管理员，负责渠道业务管理', 'channel', 1, 5, NOW(), NOW()),
('渠道操作员', 'channel_operator', '渠道操作员，负责渠道日常操作', 'channel', 1, 6, NOW(), NOW());

-- 10. 插入基础菜单数据
INSERT IGNORE INTO `menus` (`parent_id`, `name`, `code`, `path`, `component`, `icon`, `type`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
-- 一级菜单
(NULL, '首页', 'dashboard', '/dashboard', 'dashboard/index', 'dashboard', 'menu', 1, 1, NOW(), NOW()),
(NULL, '业务管理', 'business', '/business', 'Layout', 'business', 'menu', 2, 1, NOW(), NOW()),
(NULL, '客户管理', 'customer', '/customer', 'Layout', 'customer', 'menu', 3, 1, NOW(), NOW()),
(NULL, '审批管理', 'approval', '/approval', 'Layout', 'approval', 'menu', 4, 1, NOW(), NOW()),
(NULL, '金融管理', 'finance', '/finance', 'Layout', 'finance', 'menu', 5, 1, NOW(), NOW()),
(NULL, '渠道管理', 'channel', '/channel', 'Layout', 'channel', 'menu', 6, 1, NOW(), NOW()),
(NULL, '报表统计', 'report', '/report', 'Layout', 'report', 'menu', 7, 1, NOW(), NOW()),
(NULL, '系统管理', 'system', '/system', 'Layout', 'system', 'menu', 8, 1, NOW(), NOW()),

-- 业务管理二级菜单
((SELECT id FROM menus WHERE code = 'business'), '全部业务', 'business_all', '/business/all', 'business/all', 'list', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'business'), '业务申请', 'business_application', '/business/application', 'business/application', 'form', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'business'), '合同管理', 'business_contract', '/business/contract', 'business/contract', 'contract', 'menu', 3, 1, NOW(), NOW()),

-- 客户管理二级菜单
((SELECT id FROM menus WHERE code = 'customer'), '客户列表', 'customer_list', '/customer/list', 'customer/list', 'user', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'customer'), '风险查询', 'customer_risk', '/customer/risk', 'customer/risk', 'risk', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'customer'), '销售线索', 'customer_leads', '/customer/leads', 'customer/leads', 'leads', 'menu', 3, 1, NOW(), NOW()),

-- 审批管理二级菜单
((SELECT id FROM menus WHERE code = 'approval'), '待我审批', 'approval_pending', '/approval/pending', 'approval/pending', 'pending', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'approval'), '抄送我的', 'approval_cc', '/approval/cc', 'approval/cc', 'cc', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'approval'), '我发起的', 'approval_initiated', '/approval/initiated', 'approval/initiated', 'initiated', 'menu', 3, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'approval'), '审批流程', 'approval_workflow', '/approval/workflow', 'approval/workflow', 'workflow', 'menu', 4, 1, NOW(), NOW()),

-- 金融管理二级菜单
((SELECT id FROM menus WHERE code = 'finance'), '金融产品', 'finance_product', '/finance/product', 'finance/product', 'product', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'finance'), '风控规则', 'finance_risk', '/finance/risk', 'finance/risk', 'risk', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'finance'), '公司管理', 'finance_company', '/finance/company', 'finance/company', 'company', 'menu', 3, 1, NOW(), NOW()),

-- 渠道管理二级菜单
((SELECT id FROM menus WHERE code = 'channel'), '渠道列表', 'channel_list', '/channel/list', 'channel/list', 'list', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'channel'), '渠道用户', 'channel_user', '/channel/user', 'channel/user', 'user', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'channel'), '业绩统计', 'channel_performance', '/channel/performance', 'channel/performance', 'performance', 'menu', 3, 1, NOW(), NOW()),

-- 报表统计二级菜单
((SELECT id FROM menus WHERE code = 'report'), '业务报表', 'report_business', '/report/business', 'report/business', 'business', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'report'), '客户报表', 'report_customer', '/report/customer', 'report/customer', 'customer', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'report'), '渠道报表', 'report_channel', '/report/channel', 'report/channel', 'channel', 'menu', 3, 1, NOW(), NOW()),

-- 系统管理二级菜单
((SELECT id FROM menus WHERE code = 'system'), '用户管理', 'system_user', '/system/user', 'system/user', 'user', 'menu', 1, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'system'), '角色管理', 'system_role', '/system/role', 'system/role', 'role', 'menu', 2, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'system'), '菜单管理', 'system_menu', '/system/menu', 'system/menu', 'menu', 'menu', 3, 1, NOW(), NOW()),
((SELECT id FROM menus WHERE code = 'system'), '系统设置', 'system_setting', '/system/setting', 'system/setting', 'setting', 'menu', 4, 1, NOW(), NOW());

-- 11. 插入基础权限数据（基于菜单）
INSERT IGNORE INTO `permissions` (`name`, `code`, `menu_id`, `description`, `type`, `status`, `created_at`, `updated_at`)
SELECT 
    m.name, 
    m.code, 
    m.id, 
    CONCAT(m.name, '访问权限'), 
    'menu', 
    1, 
    NOW(), 
    NOW()
FROM menus m 
WHERE m.status = 1;

-- 12. 插入基础金融公司数据
INSERT IGNORE INTO `finance_companies` (`name`, `code`, `contact_person`, `contact_phone`, `contact_email`, `status`, `cooperation_status`, `created_at`, `updated_at`) VALUES
('中信银行', 'CITIC_BANK', '张经理', '400-8888-8888', '<EMAIL>', 1, 'active', NOW(), NOW()),
('平安银行', 'PING_AN_BANK', '李经理', '400-6666-6666', '<EMAIL>', 1, 'active', NOW(), NOW()),
('招商银行', 'CMB_BANK', '王经理', '400-8888-8888', '<EMAIL>', 1, 'active', NOW(), NOW());

-- 13. 配置角色权限（示例）
-- 超级管理员拥有所有权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'super_admin'),
    p.id,
    NOW(),
    NOW()
FROM permissions p;

-- 平台管理员权限（除了系统管理）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'platform_admin'),
    p.id,
    NOW(),
    NOW()
FROM permissions p
WHERE p.code NOT IN ('system_user', 'system_role', 'system_menu');

-- 金融公司管理员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'finance_admin'),
    p.id,
    NOW(),
    NOW()
FROM permissions p
WHERE p.code IN ('dashboard', 'business_all', 'customer_list', 'customer_risk', 'approval_pending', 'approval_cc', 'approval_initiated', 'finance_product', 'finance_risk', 'report_business', 'report_customer');

-- 渠道管理员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'channel_admin'),
    p.id,
    NOW(),
    NOW()
FROM permissions p
WHERE p.code IN ('dashboard', 'business_all', 'business_application', 'customer_list', 'customer_leads', 'channel_list', 'channel_user', 'channel_performance', 'report_channel');

-- 14. 创建默认超级管理员账号
INSERT IGNORE INTO `admins` (`username`, `password`, `name`, `email`, `phone`, `status`, `created_at`, `updated_at`)
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', '<EMAIL>', '***********', 1, NOW(), NOW());

-- 给超级管理员分配角色
INSERT IGNORE INTO `admin_roles` (`admin_id`, `role_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM admins WHERE username = 'admin'),
    (SELECT id FROM roles WHERE code = 'super_admin'),
    NOW(),
    NOW();

-- 15. 验证数据
SELECT '角色表' as table_name, COUNT(*) as count FROM roles
UNION ALL
SELECT '菜单表' as table_name, COUNT(*) as count FROM menus
UNION ALL
SELECT '权限表' as table_name, COUNT(*) as count FROM permissions
UNION ALL
SELECT '角色权限表' as table_name, COUNT(*) as count FROM role_permissions
UNION ALL
SELECT '金融公司表' as table_name, COUNT(*) as count FROM finance_companies; 