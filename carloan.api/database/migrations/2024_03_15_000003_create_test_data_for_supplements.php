<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\BusinessApplication;
use App\Models\CustomerSupplement;

class CreateTestDataForSupplements extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 创建测试业务申请数据
        $applications = [
            [
                'application_no' => 'APP20240315001',
                'user_id' => 1, // 假设存在用户ID为1的用户
                'channel_code' => 'DEFAULT',
                'customer_name' => '张三',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199001011234',
                'product_name' => '24年以租代售计划',
                'loan_amount' => 150000.00,
                'loan_period' => 24,
                'interest_rate' => 0.0650,
                'vehicle_brand' => '丰田',
                'vehicle_model' => '凯美瑞',
                'vehicle_vin' => 'JTDKN3DU5E0123456',
                'vehicle_year' => 2023,
                'vehicle_price' => 200000.00,
                'status' => BusinessApplication::STATUS_SUPPLEMENT_REQUIRED,
                'status_text' => '需要补件',
                'need_supplement' => true,
                'supplement_reason' => '收入证明不完整，需要补充银行流水',
                'submit_time' => now()->subDays(3),
                'supplement_deadline' => now()->addDays(4),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(1)
            ],
            [
                'application_no' => 'APP20240315002',
                'user_id' => 1,
                'channel_code' => 'DEFAULT',
                'customer_name' => '李四',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199002021234',
                'product_name' => '36年车贷计划',
                'loan_amount' => 200000.00,
                'loan_period' => 36,
                'interest_rate' => 0.0720,
                'vehicle_brand' => '本田',
                'vehicle_model' => '雅阁',
                'vehicle_vin' => 'JHMCR6F70EC123456',
                'vehicle_year' => 2024,
                'vehicle_price' => 250000.00,
                'status' => BusinessApplication::STATUS_SUPPLEMENT_REQUIRED,
                'status_text' => '需要补件',
                'need_supplement' => true,
                'supplement_reason' => '身份证照片不清晰，需要重新上传',
                'submit_time' => now()->subDays(5),
                'supplement_deadline' => now()->addDays(2),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(2)
            ],
            [
                'application_no' => 'APP20240315003',
                'user_id' => 1,
                'channel_code' => 'DEFAULT',
                'customer_name' => '王五',
                'customer_phone' => '***********',
                'customer_id_card' => '110101199003031234',
                'product_name' => '12年短期贷款',
                'loan_amount' => 80000.00,
                'loan_period' => 12,
                'interest_rate' => 0.0580,
                'vehicle_brand' => '大众',
                'vehicle_model' => '帕萨特',
                'vehicle_vin' => 'WVWZZZ3CZ9E123456',
                'vehicle_year' => 2022,
                'vehicle_price' => 180000.00,
                'status' => BusinessApplication::STATUS_INITIAL_REVIEW,
                'status_text' => '初审中',
                'need_supplement' => false,
                'submit_time' => now()->subDays(1),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subHours(2)
            ]
        ];

        foreach ($applications as $appData) {
            DB::table('business_applications')->insert($appData);
        }

        // 获取刚创建的申请ID
        $app1 = DB::table('business_applications')->where('application_no', 'APP20240315001')->first();
        $app2 = DB::table('business_applications')->where('application_no', 'APP20240315002')->first();

        // 创建测试补件数据
        $supplements = [
            [
                'application_id' => $app1->id,
                'user_id' => 1,
                'supplement_no' => 'SUP20240315001',
                'reason' => '收入证明不完整，需要补充银行流水',
                'requirements' => '请提供最近6个月的银行流水账单，需要加盖银行公章',
                'required_documents' => json_encode([
                    ['name' => '银行流水', 'type' => 'pdf', 'required' => true],
                    ['name' => '收入证明', 'type' => 'image', 'required' => true]
                ]),
                'status' => CustomerSupplement::STATUS_PENDING,
                'status_text' => '待补件',
                'created_time' => now()->subDays(3),
                'deadline' => now()->addDays(4),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3)
            ],
            [
                'application_id' => $app2->id,
                'user_id' => 1,
                'supplement_no' => 'SUP20240315002',
                'reason' => '身份证照片不清晰，需要重新上传',
                'requirements' => '请重新拍摄身份证正反面照片，确保字迹清晰可见',
                'required_documents' => json_encode([
                    ['name' => '身份证正面', 'type' => 'image', 'required' => true],
                    ['name' => '身份证反面', 'type' => 'image', 'required' => true]
                ]),
                'status' => CustomerSupplement::STATUS_REJECTED,
                'status_text' => '已驳回',
                'created_time' => now()->subDays(5),
                'deadline' => now()->addDays(2),
                'submitted_time' => now()->subDays(3),
                'reviewed_time' => now()->subDays(2),
                'submitted_documents' => json_encode([
                    ['name' => '身份证正面.jpg', 'url' => 'https://example.com/id_front.jpg', 'type' => 'image'],
                    ['name' => '身份证反面.jpg', 'url' => 'https://example.com/id_back.jpg', 'type' => 'image']
                ]),
                'customer_notes' => '已重新拍摄上传',
                'reviewer_id' => 1,
                'review_notes' => '照片仍然不够清晰，请在光线充足的环境下重新拍摄',
                'review_result' => false,
                'operation_log' => json_encode([
                    [
                        'action' => 'create',
                        'description' => '创建补件记录',
                        'user_id' => 1,
                        'created_at' => now()->subDays(5)->toDateTimeString()
                    ],
                    [
                        'action' => 'customer_submit',
                        'description' => '客户提交补件材料',
                        'user_id' => 1,
                        'created_at' => now()->subDays(3)->toDateTimeString()
                    ],
                    [
                        'action' => 'review',
                        'description' => '补件审核驳回',
                        'user_id' => 1,
                        'created_at' => now()->subDays(2)->toDateTimeString()
                    ]
                ]),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(2)
            ]
        ];

        foreach ($supplements as $supData) {
            DB::table('customer_supplements')->insert($supData);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除测试数据
        DB::table('customer_supplements')->whereIn('supplement_no', ['SUP20240315001', 'SUP20240315002'])->delete();
        DB::table('business_applications')->whereIn('application_no', ['APP20240315001', 'APP20240315002', 'APP20240315003'])->delete();
    }
} 