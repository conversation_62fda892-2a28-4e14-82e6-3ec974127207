-- 管理后台权限系统测试数据
-- 创建时间: 2024-12-25
-- 说明: 为各种角色创建测试账号和数据

-- 1. 确保基础数据存在（确保channels表有数据）
INSERT IGNORE INTO `channels` (`id`, `code`, `name`, `type`, `contact_person`, `contact_phone`, `status`, `sort`, `created_at`, `updated_at`) VALUES
(1, 'DIRECT', '直营渠道', 'direct', '直营负责人', '400-8888-8888', 1, 1, NOW(), NOW()),
(2, 'DEALER_A', '经销商A', 'dealer', '经销商A负责人', '400-1111-1111', 1, 2, NOW(), NOW()),
(3, 'DEALER_B', '经销商B', 'dealer', '经销商B负责人', '400-2222-2222', 1, 3, NOW(), NOW()),
(4, 'PARTNER_A', '合作伙伴A', 'partner', '合作伙伴A负责人', '400-3333-3333', 1, 4, NOW(), NOW());

-- 2. 创建平台管理员账号
INSERT IGNORE INTO `admins` (`username`, `password`, `name`, `email`, `phone`, `status`, `created_at`, `updated_at`) VALUES
('platform_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '平台管理员', '<EMAIL>', '13800138001', 1, NOW(), NOW()),
('platform_ops', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '平台运营', '<EMAIL>', '13800138002', 1, NOW(), NOW());

-- 3. 给平台管理员分配角色
INSERT IGNORE INTO `admin_roles` (`admin_id`, `role_id`, `created_at`, `updated_at`)
SELECT 
    a.id,
    r.id,
    NOW(),
    NOW()
FROM admins a, roles r
WHERE a.username = 'platform_admin' AND r.code = 'platform_admin'
UNION ALL
SELECT 
    a.id,
    r.id,
    NOW(),
    NOW()
FROM admins a, roles r
WHERE a.username = 'platform_ops' AND r.code = 'platform_admin';

-- 4. 创建金融公司用户账号
INSERT IGNORE INTO `finance_users` (`finance_company_id`, `username`, `password`, `name`, `phone`, `email`, `role_id`, `status`, `created_at`, `updated_at`)
SELECT 
    fc.id,
    CONCAT(fc.code, '_admin'),
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    CONCAT(fc.name, '管理员'),
    CONCAT('138', LPAD(fc.id, 8, '0')),
    CONCAT(LOWER(fc.code), '_admin@', LOWER(fc.code), '.com'),
    (SELECT id FROM roles WHERE code = 'finance_admin'),
    1,
    NOW(),
    NOW()
FROM finance_companies fc
WHERE fc.status = 1;

INSERT IGNORE INTO `finance_users` (`finance_company_id`, `username`, `password`, `name`, `phone`, `email`, `role_id`, `status`, `created_at`, `updated_at`)
SELECT 
    fc.id,
    CONCAT(fc.code, '_operator'),
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    CONCAT(fc.name, '操作员'),
    CONCAT('139', LPAD(fc.id, 8, '0')),
    CONCAT(LOWER(fc.code), '_operator@', LOWER(fc.code), '.com'),
    (SELECT id FROM roles WHERE code = 'finance_operator'),
    1,
    NOW(),
    NOW()
FROM finance_companies fc
WHERE fc.status = 1;

-- 5. 创建渠道用户账号
INSERT IGNORE INTO `channel_users` (`channel_id`, `username`, `password`, `name`, `phone`, `email`, `role_id`, `status`, `created_at`, `updated_at`)
SELECT 
    c.id,
    CONCAT(c.code, '_admin'),
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    CONCAT(c.name, '管理员'),
    CONCAT('137', LPAD(c.id, 8, '0')),
    CONCAT(LOWER(c.code), '<EMAIL>'),
    (SELECT id FROM roles WHERE code = 'channel_admin'),
    1,
    NOW(),
    NOW()
FROM channels c
WHERE c.status = 1;

INSERT IGNORE INTO `channel_users` (`channel_id`, `username`, `password`, `name`, `phone`, `email`, `role_id`, `status`, `created_at`, `updated_at`)
SELECT 
    c.id,
    CONCAT(c.code, '_operator'),
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    CONCAT(c.name, '操作员'),
    CONCAT('136', LPAD(c.id, 8, '0')),
    CONCAT(LOWER(c.code), '<EMAIL>'),
    (SELECT id FROM roles WHERE code = 'channel_operator'),
    1,
    NOW(),
    NOW()
FROM channels c
WHERE c.status = 1;

-- 6. 为金融公司操作员配置权限（比管理员权限少一些）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'finance_operator'),
    p.id,
    NOW(),
    NOW()
FROM permissions p
WHERE p.code IN ('dashboard', 'business_all', 'customer_list', 'customer_risk', 'approval_pending', 'approval_cc', 'report_business');

-- 7. 为渠道操作员配置权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
SELECT 
    (SELECT id FROM roles WHERE code = 'channel_operator'),
    p.id,
    NOW(),
    NOW()
FROM permissions p
WHERE p.code IN ('dashboard', 'business_all', 'business_application', 'customer_list', 'customer_leads', 'report_channel');

-- 8. 查看创建的账号信息
SELECT '管理员账号' as account_type, username, name, email, phone FROM admins WHERE username != 'admin'
UNION ALL
SELECT '金融公司账号' as account_type, username, name, email, phone FROM finance_users
UNION ALL  
SELECT '渠道账号' as account_type, username, name, email, phone FROM channel_users;

-- 9. 查看角色权限分配情况
SELECT 
    r.name as role_name,
    COUNT(rp.permission_id) as permission_count,
    GROUP_CONCAT(p.name SEPARATOR ', ') as permissions
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.id, r.name
ORDER BY r.sort_order;

-- 10. 查看用户角色分配情况
SELECT 
    'admins' as user_type,
    a.username,
    a.name,
    GROUP_CONCAT(r.name SEPARATOR ', ') as roles
FROM admins a
LEFT JOIN admin_roles ar ON a.id = ar.admin_id
LEFT JOIN roles r ON ar.role_id = r.id
WHERE a.status = 1
GROUP BY a.id, a.username, a.name

UNION ALL

SELECT 
    'finance_users' as user_type,
    fu.username,
    fu.name,
    r.name as roles
FROM finance_users fu
LEFT JOIN roles r ON fu.role_id = r.id
WHERE fu.status = 1

UNION ALL

SELECT 
    'channel_users' as user_type,
    cu.username,
    cu.name,
    r.name as roles
FROM channel_users cu
LEFT JOIN roles r ON cu.role_id = r.id
WHERE cu.status = 1;

-- 11. 创建一些附加的菜单权限（如果需要更细粒度的控制）
-- 注意：这里只是示例，根据实际需求调整

-- 为业务管理添加操作权限
INSERT IGNORE INTO `permissions` (`name`, `code`, `menu_id`, `description`, `type`, `status`, `created_at`, `updated_at`) VALUES
('创建业务申请', 'business_create', (SELECT id FROM menus WHERE code = 'business_application'), '创建新的业务申请', 'action', 1, NOW(), NOW()),
('编辑业务申请', 'business_edit', (SELECT id FROM menus WHERE code = 'business_application'), '编辑业务申请信息', 'action', 1, NOW(), NOW()),
('删除业务申请', 'business_delete', (SELECT id FROM menus WHERE code = 'business_application'), '删除业务申请', 'action', 1, NOW(), NOW()),
('审批业务申请', 'business_approve', (SELECT id FROM menus WHERE code = 'business_all'), '审批业务申请', 'action', 1, NOW(), NOW()),
('导出业务数据', 'business_export', (SELECT id FROM menus WHERE code = 'business_all'), '导出业务数据', 'action', 1, NOW(), NOW());

-- 为客户管理添加操作权限  
INSERT IGNORE INTO `permissions` (`name`, `code`, `menu_id`, `description`, `type`, `status`, `created_at`, `updated_at`) VALUES
('创建客户', 'customer_create', (SELECT id FROM menus WHERE code = 'customer_list'), '创建新客户', 'action', 1, NOW(), NOW()),
('编辑客户', 'customer_edit', (SELECT id FROM menus WHERE code = 'customer_list'), '编辑客户信息', 'action', 1, NOW(), NOW()),
('删除客户', 'customer_delete', (SELECT id FROM menus WHERE code = 'customer_list'), '删除客户', 'action', 1, NOW(), NOW()),
('查询客户风险', 'customer_risk_query', (SELECT id FROM menus WHERE code = 'customer_risk'), '查询客户风险信息', 'action', 1, NOW(), NOW());

-- 默认密码说明
-- 所有测试账号的默认密码都是: password
-- 在实际使用中，应该要求用户首次登录时修改密码 