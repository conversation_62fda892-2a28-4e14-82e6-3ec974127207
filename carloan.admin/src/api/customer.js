import request from '@/utils/request'

// 获取客户列表
export function getCustomerList(params) {
  return request({
    url: 'customers',
    method: 'get',
    params
  })
}

// 获取客户详情
export function getCustomerDetail(id) {
  return request({
    url: `customer/${id}`,
    method: 'get'
  })
}

// 新增客户
export function createCustomer(data) {
  return request({
    url: 'customer',
    method: 'post',
    data
  })
}

// 更新客户
export function updateCustomer(id, data) {
  return request({
    url: `customer/${id}`,
    method: 'put',
    data
  })
}

// 删除客户
export function deleteCustomer(id) {
  return request({
    url: `customer/${id}`,
    method: 'delete'
  })
}

// 批量删除客户
export function batchDeleteCustomers(ids) {
  return request({
    url: 'customers/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 导出客户列表
export function exportCustomers(params) {
  return request({
    url: 'customers/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导入客户
export function importCustomers(data) {
  return request({
    url: 'customers/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ======================== 大数据查询管理相关接口 ========================

// 获取大数据查询列表
export function getBigDataList(params) {
  return request({
    url: 'customer-big-data',
    method: 'get',
    params
  })
}

// 获取大数据查询详情
export function getBigDataDetail(id) {
  return request({
    url: `customer-big-data/${id}`,
    method: 'get'
  })
}

// 重新查询大数据
export function requeueBigData(id) {
  return request({
    url: `customer-big-data/${id}/requery`,
    method: 'post'
  })
}
