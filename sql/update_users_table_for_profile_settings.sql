-- 个人设置功能用户表结构优化
-- 执行时间：2024-12-25
-- 说明：确保用户表支持个人设置功能，包括头像、姓名、昵称等字段的完整性检查和索引优化

-- 1. 检查用户表是否存在必要字段，如果不存在则添加
-- 注意：这些字段在当前表结构中已经存在，此处仅作为备用

-- 检查avatar字段
SELECT COUNT(*) as avatar_exists 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'avatar' 
AND table_schema = DATABASE();

-- 如果avatar字段不存在则添加（通常不需要执行，因为表中已有此字段）
-- ALTER TABLE `users` ADD COLUMN `avatar` VARCHAR(255) NULL COMMENT '用户头像' AFTER `phone`;

-- 检查name字段
SELECT COUNT(*) as name_exists 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'name' 
AND table_schema = DATABASE();

-- 如果name字段不存在则添加（通常不需要执行，因为表中已有此字段）
-- ALTER TABLE `users` ADD COLUMN `name` VARCHAR(50) NULL COMMENT '用户真实姓名' AFTER `avatar`;

-- 检查nickname字段
SELECT COUNT(*) as nickname_exists 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'nickname' 
AND table_schema = DATABASE();

-- 如果nickname字段不存在则添加（通常不需要执行，因为表中已有此字段）  
-- ALTER TABLE `users` ADD COLUMN `nickname` VARCHAR(50) NULL COMMENT '用户昵称' AFTER `name`;

-- 2. 优化字段长度和注释（如果当前字段长度不合适）
ALTER TABLE `users` 
MODIFY COLUMN `avatar` VARCHAR(500) NULL COMMENT '用户头像路径(支持OSS存储格式)',
MODIFY COLUMN `name` VARCHAR(50) NULL COMMENT '用户真实姓名',
MODIFY COLUMN `nickname` VARCHAR(50) NULL COMMENT '用户昵称';

-- 3. 添加索引以优化查询性能
-- 为常用的查询字段添加索引

-- 检查是否已存在索引，避免重复创建
SELECT COUNT(*) as index_exists
FROM information_schema.statistics 
WHERE table_name = 'users' 
AND index_name = 'idx_users_phone' 
AND table_schema = DATABASE();

-- 为手机号添加索引（如果不存在）
-- ALTER TABLE `users` ADD INDEX `idx_users_phone` (`phone`);

-- 为渠道代码添加索引（如果不存在）
SELECT COUNT(*) as channel_index_exists
FROM information_schema.statistics 
WHERE table_name = 'users' 
AND index_name = 'idx_users_channel_code' 
AND table_schema = DATABASE();

-- ALTER TABLE `users` ADD INDEX `idx_users_channel_code` (`channel_code`);

-- 为状态字段添加索引（如果不存在）
SELECT COUNT(*) as status_index_exists
FROM information_schema.statistics 
WHERE table_name = 'users' 
AND index_name = 'idx_users_status' 
AND table_schema = DATABASE();

-- ALTER TABLE `users` ADD INDEX `idx_users_status` (`status`);

-- 4. 添加复合索引优化常用查询组合
-- 渠道+状态复合索引
SELECT COUNT(*) as compound_index_exists
FROM information_schema.statistics 
WHERE table_name = 'users' 
AND index_name = 'idx_users_channel_status' 
AND table_schema = DATABASE();

-- ALTER TABLE `users` ADD INDEX `idx_users_channel_status` (`channel_code`, `status`);

-- 5. 验证当前用户表结构
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = DATABASE()
AND column_name IN ('id', 'phone', 'avatar', 'name', 'nickname', 'channel_code', 'status', 'created_at', 'updated_at')
ORDER BY ordinal_position;

-- 6. 查看当前用户表的索引情况
SELECT 
    index_name,
    column_name,
    seq_in_index,
    index_type,
    non_unique
FROM information_schema.statistics 
WHERE table_name = 'users' 
AND table_schema = DATABASE()
ORDER BY index_name, seq_in_index;

-- 7. 统计当前用户数据情况
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN avatar IS NOT NULL AND avatar != '' THEN 1 END) as users_with_avatar,
    COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as users_with_name,
    COUNT(CASE WHEN nickname IS NOT NULL AND nickname != '' THEN 1 END) as users_with_nickname,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
    COUNT(DISTINCT channel_code) as unique_channels
FROM `users`
WHERE deleted_at IS NULL;

-- 8. 查看各渠道用户分布
SELECT 
    channel_code,
    COUNT(*) as user_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN avatar IS NOT NULL AND avatar != '' THEN 1 END) as avatar_count
FROM `users`
WHERE deleted_at IS NULL
GROUP BY channel_code
ORDER BY user_count DESC;

-- 9. 清理数据建议
-- 查找可能需要清理的数据
SELECT 
    id,
    phone,
    name,
    nickname,
    avatar,
    channel_code,
    status,
    created_at
FROM `users`
WHERE deleted_at IS NULL
AND (
    phone IS NULL 
    OR phone = ''
    OR LENGTH(phone) < 11
    OR LENGTH(phone) > 11
)
LIMIT 10;

-- 10. 回滚脚本（备用，慎用）
-- 如果需要回滚索引修改：
-- DROP INDEX `idx_users_phone` ON `users`;
-- DROP INDEX `idx_users_channel_code` ON `users`;
-- DROP INDEX `idx_users_status` ON `users`;
-- DROP INDEX `idx_users_channel_status` ON `users`;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 在生产环境执行前先在测试环境验证
-- 3. 索引的添加可能会影响写入性能，请在业务低峰期执行
-- 4. 字段长度修改可能会影响现有数据，请确认数据兼容性 