<template>
	<!-- 我发起的业务 -->
	<view class="business-list">
		<navbar title="我发起的" />
		<view class="container">
			<view class="list" v-if="businessList.length > 0">
				<view class="list-item" v-for="item in businessList" :key="item.id" @click="viewDetail(item)">
					<view class="item-header">
						<view class="customer-name">{{ item.customer_name }}</view>
						<view class="status">{{ getStatusText(item.status) }}</view>
					</view>
					<view class="item-content">
						<view class="info-row">
							<text class="label">申请产品：</text>
							<text class="value">{{ item.product_name }}</text>
						</view>
						<view class="info-row">
							<text class="label">申请金额：</text>
							<text class="value">{{ formatAmount(item.amount) }}</text>
						</view>
						<view class="info-row">
							<text class="label">创建时间：</text>
							<text class="value">{{ formatDate(item.created_at) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="empty" v-else-if="!loading">
				<image 
					class="empty-image"
					src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/empty.png"
				/>
				<view class="empty-text">暂无业务记录</view>
			</view>
			
			<view class="loading" v-if="loading">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import navbar from "@/components/navbar/index.vue";
import { useUserStore } from "@/stores/user";
import request from "@/utils/request";

const userStore = useUserStore();
const businessList = ref([]);
const loading = ref(false);

onMounted(() => {
	loadBusinessList();
});

// 加载业务列表
const loadBusinessList = async () => {
	loading.value = true;
	try {
		const res = await request.get('/business/my-applications');
		if (res.status_code === 200) {
			businessList.value = res.data.list || [];
		}
	} catch (error) {
		console.error('加载业务列表失败:', error);
		uni.showToast({
			title: '加载失败',
			icon: 'error'
		});
	} finally {
		loading.value = false;
	}
};

// 查看详情
const viewDetail = (item) => {
	let url = '';
	switch (item.status) {
		case 'rejected':
			url = `/pages/business/rejected-detail?id=${item.id}`;
			break;
		case 'completed':
			url = `/pages/business/completed-detail?id=${item.id}`;
			break;
		default:
			url = `/pages/business/basic?id=${item.id}&mode=view`;
			break;
	}
	
	uni.navigateTo({
		url: url
	});
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		'draft': '草稿',
		'pending': '待审核',
		'approved': '已通过',
		'rejected': '已驳回',
		'completed': '已完成'
	};
	return statusMap[status] || '未知状态';
};

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0元';
	return `${parseFloat(amount).toLocaleString()}元`;
};

// 格式化日期
const formatDate = (dateString) => {
	if (!dateString) return '';
	const date = new Date(dateString);
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
.business-list {
	background: #f5f5f5;
	min-height: 100vh;
	
	.container {
		padding: 20rpx;
		
		.list {
			.list-item {
				background: #ffffff;
				border-radius: 16rpx;
				padding: 30rpx;
				margin-bottom: 20rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				
				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;
					
					.customer-name {
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
					}
					
					.status {
						font-size: 24rpx;
						padding: 8rpx 16rpx;
						border-radius: 20rpx;
						background: #f0f0f0;
						color: #333333;
					}
				}
				
				.item-content {
					.info-row {
						display: flex;
						margin-bottom: 12rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.label {
							font-size: 28rpx;
							color: #666666;
							width: 180rpx;
						}
						
						.value {
							font-size: 28rpx;
							color: #333333;
							flex: 1;
						}
					}
				}
			}
		}
		
		.empty {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;
			
			.empty-image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 30rpx;
			}
			
			.empty-text {
				font-size: 28rpx;
				color: #999999;
			}
		}
		
		.loading {
			display: flex;
			justify-content: center;
			padding: 60rpx 0;
			font-size: 28rpx;
			color: #999999;
		}
	}
}
</style> 