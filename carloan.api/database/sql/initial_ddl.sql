-- carloan.admins definition

CREATE TABLE `admins` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户状态 0锁定 1正常',
  `login_time` timestamp NULL DEFAULT NULL COMMENT '最近登录时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `admins_name_index` (`name`),
  <PERSON><PERSON>Y `admins_phone_index` (`phone`),
  KEY `admins_status_index` (`status`),
  KEY `admins_login_time_index` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台用户表';


-- carloan.agreements definition

CREATE TABLE `agreements` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '协议名称',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '协议内容',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agreements_name_index` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='协议表';


-- carloan.business_applications definition

CREATE TABLE `business_applications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请单号',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '业务员ID',
  `channel_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道码',
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户手机号',
  `customer_id_card` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户身份证号',
  `product_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `loan_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '贷款金额',
  `loan_period` int(11) NOT NULL DEFAULT '0' COMMENT '贷款期限(月)',
  `interest_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '利率',
  `vehicle_brand` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '车辆品牌',
  `vehicle_model` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '车辆型号',
  `vehicle_vin` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '车架号',
  `vehicle_year` year(4) DEFAULT NULL COMMENT '车辆年份',
  `vehicle_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '车辆价格',
  `status` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'submitted' COMMENT '业务状态',
  `status_text` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '已提交' COMMENT '状态文本',
  `approval_notes` text COLLATE utf8mb4_unicode_ci COMMENT '审批备注',
  `approval_history` json DEFAULT NULL COMMENT '审批历史',
  `submit_time` timestamp NULL DEFAULT NULL COMMENT '提交时间',
  `approval_time` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `need_supplement` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否需要补件:0否,1是',
  `supplement_reason` text COLLATE utf8mb4_unicode_ci COMMENT '补件原因',
  `supplement_deadline` timestamp NULL DEFAULT NULL COMMENT '补件截止时间',
  `customer_data` json DEFAULT NULL COMMENT '客户详细数据',
  `risk_assessment` json DEFAULT NULL COMMENT '风险评估结果',
  `attachments` json DEFAULT NULL COMMENT '附件列表',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `business_applications_application_no_unique` (`application_no`),
  KEY `business_applications_user_id_index` (`user_id`),
  KEY `business_applications_channel_code_index` (`channel_code`),
  KEY `business_applications_customer_phone_index` (`customer_phone`),
  KEY `business_applications_customer_id_card_index` (`customer_id_card`),
  KEY `business_applications_status_index` (`status`),
  KEY `business_applications_status_user_id_index` (`status`,`user_id`),
  KEY `business_applications_customer_phone_customer_id_card_index` (`customer_phone`,`customer_id_card`),
  KEY `business_applications_submit_time_index` (`submit_time`),
  KEY `business_applications_need_supplement_index` (`need_supplement`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_customer_search` (`customer_name`,`customer_phone`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_supplement` (`need_supplement`,`supplement_deadline`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务申请表';


-- carloan.channels definition

CREATE TABLE `channels` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道码',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道名称',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `channels_code_unique` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='渠道表';


-- carloan.customer_risk_queries definition

CREATE TABLE `customer_risk_queries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '业务员ID',
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
  `customer_id_card` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户身份证号',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'querying' COMMENT '查询状态:querying查询中,completed已完成,failed查询失败',
  `risk_score` int(11) DEFAULT NULL COMMENT '风险评分(0-100)',
  `risk_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '风险等级:low低风险,medium中风险,high高风险',
  `query_time` timestamp NULL DEFAULT NULL COMMENT '查询时间',
  `query_results` json DEFAULT NULL COMMENT '查询结果详情',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_risk_queries_user_id_index` (`user_id`),
  KEY `customer_risk_queries_customer_phone_index` (`customer_phone`),
  KEY `customer_risk_queries_customer_id_card_index` (`customer_id_card`),
  KEY `customer_risk_queries_status_index` (`status`),
  KEY `customer_risk_queries_user_id_status_index` (`user_id`,`status`),
  KEY `customer_risk_queries_customer_phone_customer_id_card_index` (`customer_phone`,`customer_id_card`),
  KEY `customer_risk_queries_query_time_index` (`query_time`),
  KEY `customer_risk_queries_created_at_index` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户风险查询表';


-- carloan.financial_products definition

CREATE TABLE `financial_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品代码',
  `product_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `product_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'loan' COMMENT '产品类型:loan贷款,lease租赁',
  `annual_rate` decimal(6,4) NOT NULL DEFAULT '0.0000' COMMENT '年利率',
  `min_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最低贷款额度',
  `max_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最高贷款额度',
  `supported_periods` json NOT NULL COMMENT '支持的期数列表',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '产品描述',
  `features` json DEFAULT NULL COMMENT '产品特色',
  `conditions` json DEFAULT NULL COMMENT '申请条件',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `financial_products_product_code_unique` (`product_code`),
  KEY `financial_products_product_type_index` (`product_type`),
  KEY `financial_products_status_index` (`status`),
  KEY `financial_products_status_sort_index` (`status`,`sort_order`),
  KEY `financial_products_annual_rate_index` (`annual_rate`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金融产品表';


-- carloan.loan_periods definition

CREATE TABLE `loan_periods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `period_months` int(11) NOT NULL COMMENT '期数（月）',
  `period_label` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '期数标签',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认期数:1是,0否',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `loan_periods_period_months_unique` (`period_months`),
  KEY `loan_periods_status_index` (`status`),
  KEY `loan_periods_is_default_index` (`is_default`),
  KEY `loan_periods_status_sort_index` (`status`,`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='贷款期数表';


-- carloan.migrations definition

CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- carloan.risk_queries definition

CREATE TABLE `risk_queries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `cert_no` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `query_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '查询类型: 1身份证OCR 2涉诉信息 3失信被执行 4限制高消费 5信用风险 6贷前风险 7二要素认证',
  `out_trade_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '交易流水号',
  `tran_amt` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '交易金额',
  `tran_time` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '交易时间',
  `request_params` text COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
  `code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '响应码',
  `result_msg` text COLLATE utf8mb4_unicode_ci COMMENT '响应消息',
  `result_data` text COLLATE utf8mb4_unicode_ci COMMENT '响应数据',
  `sign` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '签名',
  `pdf_url` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PDF报告链接',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态: 0失败 1成功',
  `error_msg` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `query_time` timestamp NULL DEFAULT NULL COMMENT '查询时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `risk_queries_query_type_status_index` (`query_type`,`status`),
  KEY `risk_queries_query_time_index` (`query_time`),
  KEY `risk_queries_out_trade_no_index` (`out_trade_no`),
  KEY `risk_queries_user_id_index` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风险查询记录表';


-- carloan.settings definition

CREATE TABLE `settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `value` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '值',
  `desc` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `settings_name_index` (`name`),
  KEY `settings_value_index` (`value`),
  KEY `settings_desc_index` (`desc`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';


-- carloan.users definition

CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `avatar` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像',
  `nickname` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `name` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `channel_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道码',
  `wechat_openid` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '微信openid',
  `wechat_session_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '微信session_key',
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'password',
  `token` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '当前登录token',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户状态 0锁定 1正常 2已注销',
  `register_time` timestamp NULL DEFAULT NULL COMMENT '注册时间',
  `login_time` timestamp NULL DEFAULT NULL COMMENT '最近登录时间',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `users_phone_index` (`phone`),
  KEY `users_nickname_index` (`nickname`),
  KEY `users_name_index` (`name`),
  KEY `users_channel_code_index` (`channel_code`),
  KEY `users_wechat_openid_index` (`wechat_openid`),
  KEY `users_token_index` (`token`),
  KEY `users_status_index` (`status`),
  KEY `users_sort_index` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';


-- carloan.vehicle_assessments definition

CREATE TABLE `vehicle_assessments` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `model_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '品牌型号',
  `vin` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车架号',
  `condition` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车况:excellent优秀/good良好/normal一般',
  `condition_text` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车况文本',
  `dealer_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车商零售价',
  `individual_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '个人交易价',
  `dealer_buy_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车商收车价',
  `individual_low_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '个人最低交易价',
  `dealer_low_buy_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车商最低收车价',
  `dealer_high_sold_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车商最高零售价',
  `dealer_low_sold_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '车商最低零售价',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '创建用户ID',
  `raw_data` json DEFAULT NULL COMMENT '原始评估数据',
  `assessment_params` json DEFAULT NULL COMMENT '评估参数',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle_assessments_vin_index` (`vin`),
  KEY `vehicle_assessments_condition_index` (`condition`),
  KEY `vehicle_assessments_created_at_index` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- carloan.customer_supplements definition

CREATE TABLE `customer_supplements` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL COMMENT '业务申请ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '业务员ID',
  `supplement_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '补件单号',
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '补件原因',
  `requirements` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '补件要求',
  `required_documents` json DEFAULT NULL COMMENT '需要补充的文档列表',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '补件状态:pending待补件,submitted已提交,approved已审核,rejected已驳回',
  `status_text` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '待补件' COMMENT '状态文本',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `deadline` timestamp NULL DEFAULT NULL COMMENT '补件截止时间',
  `submitted_time` timestamp NULL DEFAULT NULL COMMENT '客户提交时间',
  `reviewed_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `submitted_documents` json DEFAULT NULL COMMENT '客户提交的文档',
  `customer_notes` text COLLATE utf8mb4_unicode_ci COMMENT '客户备注',
  `reviewer_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `review_notes` text COLLATE utf8mb4_unicode_ci COMMENT '审核备注',
  `review_result` tinyint(4) DEFAULT NULL COMMENT '审核结果:1通过,0驳回',
  `operation_log` json DEFAULT NULL COMMENT '操作日志',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customer_supplements_supplement_no_unique` (`supplement_no`),
  KEY `customer_supplements_application_id_index` (`application_id`),
  KEY `customer_supplements_user_id_index` (`user_id`),
  KEY `customer_supplements_status_index` (`status`),
  KEY `customer_supplements_status_user_id_index` (`status`,`user_id`),
  KEY `customer_supplements_deadline_index` (`deadline`),
  KEY `customer_supplements_submitted_time_index` (`submitted_time`),
  CONSTRAINT `customer_supplements_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户补件表';


-- carloan.esign_contracts definition

CREATE TABLE `esign_contracts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL COMMENT '业务申请ID',
  `flow_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'e签宝流程ID',
  `account_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'e签宝账户ID',
  `contract_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '合同名称',
  `contract_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'processing' COMMENT '合同状态:processing处理中,completed已完成,failed失败',
  `sign_url` text COLLATE utf8mb4_unicode_ci COMMENT '签署链接',
  `contract_file_url` text COLLATE utf8mb4_unicode_ci COMMENT '已签署合同文件URL',
  `initiated_time` timestamp NULL DEFAULT NULL COMMENT '发起时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `callback_data` json DEFAULT NULL COMMENT '回调数据',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `esign_contracts_flow_id_unique` (`flow_id`),
  KEY `esign_contracts_application_id_index` (`application_id`),
  KEY `esign_contracts_contract_status_index` (`contract_status`),
  CONSTRAINT `esign_contracts_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='e签宝签约记录表';


-- carloan.id_card_ocr_results definition

CREATE TABLE `id_card_ocr_results` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `risk_query_id` bigint(20) unsigned NOT NULL COMMENT '关联risk_queries表ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `cert_no` varchar(18) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `gender` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '性别',
  `nationality` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '民族',
  `birth_date` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '出生日期',
  `address` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '住址',
  `issue_authority` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '签发机关',
  `valid_from` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '有效期开始时间',
  `valid_to` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '有效期结束时间',
  `front_img` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证正面图片',
  `back_img` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证背面图片',
  `face_img` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '人脸照片',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态: 0失败 1成功',
  `error_msg` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_card_ocr_results_risk_query_id_index` (`risk_query_id`),
  KEY `id_card_ocr_results_user_id_index` (`user_id`),
  CONSTRAINT `id_card_ocr_results_risk_query_id_foreign` FOREIGN KEY (`risk_query_id`) REFERENCES `risk_queries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='身份证OCR识别结果表';


-- carloan.interview_appointments definition

CREATE TABLE `interview_appointments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) unsigned NOT NULL COMMENT '业务申请ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '业务员ID',
  `appointment_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预约单号',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态:pending待预约,scheduled已预约,completed已完成,cancelled已取消',
  `status_text` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '待预约' COMMENT '状态文本',
  `appointment_time` timestamp NULL DEFAULT NULL COMMENT '预约面审时间',
  `appointment_location` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '面审地点',
  `appointment_notes` text COLLATE utf8mb4_unicode_ci COMMENT '预约备注',
  `interview_result` tinyint(4) DEFAULT NULL COMMENT '面审结果:1通过,0不通过',
  `interview_notes` text COLLATE utf8mb4_unicode_ci COMMENT '面审备注',
  `interview_materials` json DEFAULT NULL COMMENT '面审材料',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `scheduled_time` timestamp NULL DEFAULT NULL COMMENT '预约时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `cancelled_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `interviewer_id` bigint(20) unsigned DEFAULT NULL COMMENT '面审官ID',
  `cancel_reason` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '取消原因',
  `operation_log` json DEFAULT NULL COMMENT '操作日志',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `interview_appointments_appointment_no_unique` (`appointment_no`),
  KEY `interview_appointments_application_id_index` (`application_id`),
  KEY `interview_appointments_user_id_index` (`user_id`),
  KEY `interview_appointments_status_index` (`status`),
  KEY `interview_appointments_status_user_id_index` (`status`,`user_id`),
  KEY `interview_appointments_appointment_time_index` (`appointment_time`),
  KEY `interview_appointments_scheduled_time_index` (`scheduled_time`),
  CONSTRAINT `interview_appointments_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='面审预约表';