-- 为客户补件表添加撤销相关字段
-- 执行时间：2025-01-12
-- 说明：支持撤销功能，记录撤销原因和时间

-- 1. 添加撤销相关字段
ALTER TABLE `customer_supplements` 
ADD COLUMN `withdrawn_time` TIMESTAMP NULL DEFAULT NULL COMMENT '撤销时间' AFTER `reviewed_time`,
ADD COLUMN `withdraw_reason` TEXT NULL COMMENT '撤销原因' AFTER `review_result`,
ADD COLUMN `withdrawn_by` BIGINT(20) UNSIGNED NULL COMMENT '撤销人ID' AFTER `withdraw_reason`;

-- 2. 添加索引
ALTER TABLE `customer_supplements` 
ADD INDEX `idx_withdrawn_time` (`withdrawn_time`),
ADD INDEX `idx_withdrawn_by` (`withdrawn_by`);

-- 3. 验证字段是否添加成功
SELECT 
    column_name, 
    data_type, 
    column_default, 
    column_comment,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'customer_supplements' 
AND column_name IN ('withdrawn_time', 'withdraw_reason', 'withdrawn_by');

-- 4. 回滚脚本（备用，慎用）
-- ALTER TABLE `customer_supplements` DROP COLUMN `withdrawn_by`;
-- ALTER TABLE `customer_supplements` DROP COLUMN `withdraw_reason`;
-- ALTER TABLE `customer_supplements` DROP COLUMN `withdrawn_time`; 