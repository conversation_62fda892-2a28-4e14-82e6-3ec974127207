<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ThirdPartyApiLog extends Model
{
    protected $table = 'third_party_api_logs';
    
    protected $fillable = [
        'service_name',
        'api_name',
        'method',
        'request_url',
        'request_headers',
        'request_params',
        'response_status',
        'response_headers',
        'response_body',
        'duration_ms',
        'error_message',
        'is_success',
        'business_id',
        'business_type',
        'request_time'
    ];

    protected $casts = [
        'request_headers' => 'json',
        'request_params' => 'json',
        'response_headers' => 'json',
        'response_body' => 'json',
        'is_success' => 'boolean',
        'request_time' => 'datetime'
    ];

    /**
     * 作用域：按服务名称筛选
     */
    public function scopeByService($query, $serviceName)
    {
        return $query->where('service_name', $serviceName);
    }

    /**
     * 作用域：按成功状态筛选
     */
    public function scopeBySuccess($query, $isSuccess = true)
    {
        return $query->where('is_success', $isSuccess);
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeByTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('request_time', [$startTime, $endTime]);
    }

    /**
     * 作用域：按业务ID筛选
     */
    public function scopeByBusiness($query, $businessId, $businessType = null)
    {
        $query->where('business_id', $businessId);
        if ($businessType) {
            $query->where('business_type', $businessType);
        }
        return $query;
    }
} 