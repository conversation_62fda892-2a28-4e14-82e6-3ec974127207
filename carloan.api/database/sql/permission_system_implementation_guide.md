# 车贷系统权限管理实现指南

## 设计原则

根据用户需求，重新设计了一个简化但实用的权限系统：

1. **保持前端路由配置不变** - 菜单路由配置仍在前端，后端只控制权限
2. **基于现有表结构** - 利用现有的 `users`、`admins`、`channels` 表
3. **权限码机制** - 采用简单的权限码系统，支持精确权限和通配符权限
4. **角色分类明确** - 四种角色类型：渠道（车商）、销售（业务员）、金融公司、平台管理员

## 表结构设计

### 新增表

1. **roles** - 角色表
   - 存储角色信息和权限码列表（JSON格式）
   - 支持三种类型：admin（管理员）、user（业务员）、finance（金融）

2. **permission_codes** - 权限码说明表
   - 存储权限码的说明信息
   - 用于前端展示和权限验证

### 修改现有表

1. **users** 表新增字段：
   - `role_id` - 角色ID
   - `can_login_admin` - 是否可登录后台
   - `admin_permissions` - 额外管理权限（JSON）

2. **admins** 表新增字段：
   - `role_id` - 角色ID
   - `admin_permissions` - 额外管理权限（JSON）

## 权限码设计

采用层级权限码设计：

- `*` - 所有权限（超级管理员）
- `dashboard` - 首页权限
- `business.*` - 业务管理所有权限
- `business.view` - 查看业务
- `business.create` - 创建业务
- `customer.*` - 客户管理所有权限
- `customer.view` - 查看客户
- 等等...

## 角色权限分配

### 1. 超级管理员 (super_admin)
- 权限：`['*']`
- 用户类型：管理员
- 说明：拥有所有权限

### 2. 平台管理员 (platform_admin)
- 权限：`['dashboard', 'business.*', 'customer.*', 'approval.*', 'channel.*', 'report.*', 'setting.basic']`
- 用户类型：管理员
- 说明：平台整体运营管理

### 3. 财务管理员 (finance_admin)
- 权限：`['dashboard', 'business.view', 'business.approve', 'customer.view', 'customer.risk', 'approval.*', 'report.business', 'report.finance']`
- 用户类型：管理员
- 说明：负责审批和风控

### 4. 渠道管理员 (channel_manager)
- 权限：`['dashboard', 'business.view', 'business.create', 'business.edit', 'customer.view', 'customer.create', 'customer.leads', 'report.channel', 'report.personal']`
- 用户类型：业务员（可登录后台）
- 说明：渠道负责人，可登录后台

### 5. 高级业务员 (senior_sales)
- 权限：`['dashboard', 'business.view', 'business.create', 'customer.view', 'customer.create', 'report.personal']`
- 用户类型：业务员（可登录后台）
- 说明：高级业务员，可登录后台

### 6. 普通业务员 (sales)
- 权限：`[]`
- 用户类型：业务员（不能登录后台）
- 说明：普通业务员，只能使用APP

## 后端实现

### Model 类

1. **Role** - 角色模型
   - 包含权限验证方法 `hasPermission()`
   - 获取所有权限方法 `getAllPermissions()`

2. **PermissionCode** - 权限码模型
   - 管理权限码说明信息

3. **User** - 更新用户模型
   - 添加权限相关字段和方法
   - 权限验证方法

4. **Admin** - 更新管理员模型
   - 添加权限相关字段和方法

### Controller 类

1. **AuthController** - 权限认证控制器
   - 管理员登录 `adminLogin()`
   - 业务员登录 `userLogin()`
   - 权限检查 `checkPermission()`
   - 获取菜单权限 `getMenus()`

2. **RoleController** - 角色管理控制器
   - 角色的增删改查
   - 权限分配

3. **AdminController** - 管理员管理控制器
   - 管理员的增删改查
   - 角色分配

4. **UserController** - 业务员管理控制器
   - 业务员的增删改查
   - 后台登录权限设置

## 前端集成

### 登录流程

1. 管理员使用 `POST /admin-api/auth/admin-login`
2. 业务员使用 `POST /admin-api/auth/user-login`
3. 获取用户信息和权限列表
4. 前端根据权限列表控制菜单显示

### 权限验证

1. 前端可调用 `POST /admin-api/auth/check-permission` 验证具体权限
2. 获取菜单权限 `GET /admin-api/auth/menus`
3. 前端根据返回的路由列表控制菜单访问

### 菜单控制

前端路由配置保持不变，只需在菜单渲染时检查权限：

```javascript
// 示例：检查用户是否有某个菜单的权限
const hasMenuPermission = (route) => {
  const userPermissions = store.getters.permissions;
  return userPermissions.includes('*') || userPermissions.includes(route);
};
```

## 部署步骤

1. 执行数据库脚本：
   ```bash
   mysql < carloan.api/database/sql/create_simplified_permission_system.sql
   ```

2. 确保新增的 Model 和 Controller 文件已部署

3. 检查路由配置是否正确

4. 测试登录和权限验证功能

## 注意事项

1. 超级管理员账号不能被删除或禁用
2. 权限码支持通配符匹配（如 `business.*`）
3. 业务员默认不能登录后台，需要手动开启权限
4. 前端菜单配置保持原有结构，只需添加权限检查逻辑

## 扩展性

系统设计支持未来扩展：

1. 可以轻松添加新的权限码
2. 可以创建新的角色
3. 支持用户级别的额外权限分配
4. 权限码表可用于前端权限说明展示 