<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ESignService;
use App\Models\BusinessApplication;
use App\Models\BusinessApplicationContract;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ContractController extends Controller
{
    protected $esignService;
    
    public function __construct(ESignService $esignService)
    {
        $this->esignService = $esignService;
    }
    
    /**
     * 生成合同并发起签署
     */
    public function generateContract(Request $request, $applicationId)
    {
        try {
            $application = BusinessApplication::with('currentContract')->findOrFail($applicationId);
            
            // 检查申请状态
            if ($application->status !== BusinessApplication::STATUS_FINAL_REVIEW) {
                return response()->json([
                    'success' => false,
                    'message' => '申请状态不符合签约条件'
                ], 400);
            }
            
            // 检查是否已存在有效合同
            $existingContract = $application->activeContract;
            if ($existingContract) {
                return response()->json([
                    'success' => false,
                    'message' => '该申请已存在有效合同',
                    'data' => [
                        'contract_id' => $existingContract->id,
                        'contract_status' => $existingContract->contract_status,
                        'sign_url' => $existingContract->sign_url
                    ]
                ], 400);
            }
            
            return DB::transaction(function () use ($application, $request) {
                // 准备合同数据
                $contractData = $this->prepareContractData($application);
                
                // 创建合同记录
                $contract = BusinessApplicationContract::create([
                    'application_id' => $application->id,
                    'contract_type' => $contractData['contract_type'],
                    'contract_title' => $contractData['contract_title'],
                    'contract_status' => BusinessApplicationContract::STATUS_DRAFT,
                    'contract_data' => $contractData
                ]);
                
                // 生成合同并发起签署
                $flowId = $this->esignService->createSignFlow($contractData);
                
                // 获取签署链接
                $accountId = $this->esignService->createPersonalAccount(
                    $contractData['customer_name'],
                    $contractData['customer_id_card'],
                    $contractData['customer_phone'],
                    $application->id
                );
                
                $fileId = $contractData['file_id'] ?? null;
                $signUrl = $this->esignService->getSignUrl($flowId, $accountId, $application->id);
                
                // 更新合同记录
                $contract->markAsGenerated($flowId, $accountId, $fileId, $signUrl);
                
                // 更新业务申请状态
                $application->update([
                    'status' => BusinessApplication::STATUS_CONTRACT_PENDING
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => '合同生成成功',
                    'data' => [
                        'contract_id' => $contract->id,
                        'flow_id' => $flowId,
                        'sign_url' => $signUrl,
                        'contract_type' => $contractData['contract_type'],
                        'contract_status' => $contract->contract_status
                    ]
                ]);
            });
            
        } catch (\Exception $e) {
            Log::error('合同生成失败', [
                'application_id' => $applicationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '合同生成失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 查询签署状态
     */
    public function getSignStatus($applicationId)
    {
        try {
            $application = BusinessApplication::with('currentContract')->findOrFail($applicationId);
            $contract = $application->currentContract;
            
            if (!$contract || !$contract->esign_flow_id) {
                return response()->json([
                    'success' => false,
                    'message' => '该申请尚未生成合同'
                ], 400);
            }
            
            $signStatus = $this->esignService->getSignStatus(
                $contract->esign_flow_id,
                $application->id
            );
            
            // 更新合同状态（如果需要）
            $this->updateContractStatusFromESign($contract, $signStatus);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'application_id' => $applicationId,
                    'contract_id' => $contract->id,
                    'flow_id' => $contract->esign_flow_id,
                    'contract_type' => $contract->contract_type,
                    'contract_status' => $contract->contract_status,
                    'esign_status' => $signStatus['flowStatus'],
                    'sign_url' => $contract->sign_url,
                    'generated_at' => $contract->generated_at,
                    'signed_at' => $contract->signed_at,
                    'expired_at' => $contract->expired_at
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('查询签署状态失败', [
                'application_id' => $applicationId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '查询签署状态失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取签署链接
     */
    public function getSignUrl($applicationId)
    {
        try {
            $application = BusinessApplication::with('currentContract')->findOrFail($applicationId);
            $contract = $application->currentContract;
            
            if (!$contract || !$contract->esign_flow_id || !$contract->esign_account_id) {
                return response()->json([
                    'success' => false,
                    'message' => '签署信息不完整'
                ], 400);
            }
            
            // 检查合同是否可以签署
            if (!$contract->canSign()) {
                return response()->json([
                    'success' => false,
                    'message' => '当前合同状态不允许签署',
                    'data' => [
                        'contract_status' => $contract->contract_status,
                        'contract_status_text' => $contract->status_text
                    ]
                ], 400);
            }
            
            // 重新获取签署链接（链接有有效期）
            $signUrl = $this->esignService->getSignUrl(
                $contract->esign_flow_id,
                $contract->esign_account_id,
                $application->id
            );
            
            // 更新签署链接和有效期
            $contract->refreshSignUrl($signUrl);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'contract_id' => $contract->id,
                    'sign_url' => $signUrl,
                    'flow_id' => $contract->esign_flow_id,
                    'expired_at' => $contract->expired_at
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取签署链接失败', [
                'application_id' => $applicationId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '获取签署链接失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 处理e签宝回调
     */
    public function handleCallback(Request $request)
    {
        try {
            $callbackData = $request->all();
            
            Log::info('收到e签宝回调', $callbackData);
            
            // 处理回调
            $this->esignService->handleCallback($callbackData);
            
            // 根据回调更新业务状态
            $action = $callbackData['action'] ?? '';
            $flowId = $callbackData['flowId'] ?? '';
            
            if ($flowId) {
                $contract = BusinessApplicationContract::where('esign_flow_id', $flowId)->first();
                
                if ($contract) {
                    $contract->update([
                        'esign_callback_data' => $callbackData
                    ]);
                    
                    switch ($action) {
                        case 'SIGN_FLOW_FINISH':
                            $this->handleSignFlowFinish($contract, $callbackData);
                            break;
                            
                        case 'SIGN_FLOW_REVOKE':
                            $this->handleSignFlowRevoke($contract, $callbackData);
                            break;
                            
                        case 'SIGNER_SIGN_FINISH':
                            $this->handleSignerSignFinish($contract, $callbackData);
                            break;
                            
                        case 'SIGN_FLOW_START':
                            $this->handleSignFlowStart($contract, $callbackData);
                            break;
                    }
                }
            }
            
            return response()->json(['success' => true]);
            
        } catch (\Exception $e) {
            Log::error('e签宝回调处理失败', [
                'error' => $e->getMessage(),
                'callback_data' => $request->all()
            ]);
            
            return response()->json(['success' => false], 500);
        }
    }
    
    /**
     * 创建合同模板
     */
    public function createTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:100',
            'template_type' => 'required|in:standard,table',
            'template_file' => 'required|file|mimes:pdf,doc,docx|max:10240'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }
        
        try {
            $templateFile = $request->file('template_file');
            $templateName = $request->input('template_name');
            $templateType = $request->input('template_type');
            
            // 保存上传的文件
            $filePath = $templateFile->store('templates', 'local');
            $fullPath = storage_path('app/' . $filePath);
            
            // 上传文件到e签宝
            $fileResult = $this->esignService->uploadFile(
                $fullPath,
                $templateFile->getClientOriginalName()
            );
            
            // 创建模板
            $templateResult = $this->esignService->createDocTemplate(
                $templateName,
                $fileResult['fileId'],
                $templateType === 'table' ? 1 : 0 // 1: HTML模板, 0: PDF模板
            );
            
            // 清理临时文件
            unlink($fullPath);
            
            return response()->json([
                'success' => true,
                'message' => '模板创建成功',
                'data' => [
                    'template_id' => $templateResult['docTemplateId'],
                    'template_create_url' => $templateResult['docTemplateCreateUrl'],
                    'template_name' => $templateName,
                    'template_type' => $templateType
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('创建合同模板失败', [
                'error' => $e->getMessage(),
                'template_name' => $request->input('template_name')
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '创建模板失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 准备合同数据
     */
    private function prepareContractData($application)
    {
        $contractType = $this->getContractType($application);
        
        return [
            'application_id' => $application->id,
            'application_no' => $application->application_no,
            'customer_name' => $application->customer_name,
            'customer_id_card' => $application->customer_id_card,
            'customer_phone' => $application->customer_phone,
            'loan_amount' => $application->loan_amount,
            'loan_period' => $application->loan_period,
            'interest_rate' => $application->interest_rate,
            'vehicle_brand' => $application->vehicle['brand'] ?? '',
            'vehicle_model' => $application->vehicle['model'] ?? '',
            'vehicle_vin' => $application->vehicle['vin'] ?? '',
            'vehicle_price' => $application->vehicle['price'] ?? 0,
            'vehicle_year' => $application->vehicle['year'] ?? '',
            'contacts' => $application->contacts ?? [],
            'assets' => $application->assets ?? [],
            'liabilities' => $application->liabilities ?? [],
            'contract_type' => $contractType,
            'contract_title' => $this->generateContractTitle($application, $contractType)
        ];
    }
    
    /**
     * 判断合同类型
     */
    private function getContractType($application)
    {
        // 如果有联系人、资产或负债信息，使用表格合同
        if (!empty($application->contacts) || 
            !empty($application->assets) || 
            !empty($application->liabilities)) {
            return 'table';
        }
        
        return 'standard';
    }
    
    /**
     * 生成合同标题
     */
    private function generateContractTitle($application, $contractType)
    {
        $typeText = $contractType === 'table' ? '详细' : '标准';
        return sprintf(
            '%s车贷%s合同-%s',
            $application->customer_name,
            $typeText,
            $application->application_no
        );
    }
    
    /**
     * 根据e签宝状态更新合同状态
     */
    private function updateContractStatusFromESign($contract, $signStatus)
    {
        $esignStatus = $signStatus['flowStatus'] ?? '';
        
        switch ($esignStatus) {
            case 'WAITING':
                if ($contract->contract_status === BusinessApplicationContract::STATUS_GENERATED) {
                    $contract->markAsSigning();
                }
                break;
            case 'FINISHED':
                if (!$contract->isSigned()) {
                    $contract->markAsSigned();
                }
                break;
            case 'REVOKED':
                $contract->markAsRevoked('签署流程已撤销');
                break;
            case 'FAILED':
                $contract->markAsFailed('签署流程失败');
                break;
        }
    }
    
    /**
     * 处理签署流程开始
     */
    private function handleSignFlowStart($contract, $callbackData)
    {
        $contract->markAsSigning();
        
        Log::info('签署流程开始', [
            'contract_id' => $contract->id,
            'application_id' => $contract->application_id,
            'flow_id' => $contract->esign_flow_id
        ]);
    }
    
    /**
     * 处理签署流程完成
     */
    private function handleSignFlowFinish($contract, $callbackData)
    {
        $contract->markAsSigned();
        
        // 更新业务申请状态
        $contract->businessApplication->update([
            'status' => BusinessApplication::STATUS_CONTRACT_COMPLETED
        ]);
        
        Log::info('签署流程完成', [
            'contract_id' => $contract->id,
            'application_id' => $contract->application_id,
            'flow_id' => $contract->esign_flow_id
        ]);
        
        // 这里可以添加其他业务逻辑，如发送通知、更新状态等
    }
    
    /**
     * 处理签署流程撤销
     */
    private function handleSignFlowRevoke($contract, $callbackData)
    {
        $contract->markAsRevoked('e签宝流程撤销');
        
        // 更新业务申请状态
        $contract->businessApplication->update([
            'status' => BusinessApplication::STATUS_FINAL_REVIEW // 回到终审状态
        ]);
        
        Log::info('签署流程撤销', [
            'contract_id' => $contract->id,
            'application_id' => $contract->application_id,
            'flow_id' => $contract->esign_flow_id
        ]);
    }
    
    /**
     * 处理签署人签署完成
     */
    private function handleSignerSignFinish($contract, $callbackData)
    {
        // 这里可以处理单个签署人完成签署的逻辑
        // 如果是多人签署，可能需要检查是否所有人都已签署
        
        // 更新业务申请状态为签署中
        $contract->businessApplication->update([
            'status' => BusinessApplication::STATUS_CONTRACT_PROCESSING
        ]);
        
        Log::info('签署人签署完成', [
            'contract_id' => $contract->id,
            'application_id' => $contract->application_id,
            'flow_id' => $contract->esign_flow_id,
            'signer' => $callbackData['signerAccountId'] ?? ''
        ]);
    }
} 