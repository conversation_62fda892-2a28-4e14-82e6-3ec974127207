import { defineStore } from "pinia";
import { debug } from "@/utils/debug";

export const useContractStore = defineStore("contract", {
	// 状态：用于存储签约相关信息
	state: () => ({
		// 签约列表缓存
		contractList: [],
		// 待更新的签约项ID和数据
		pendingUpdates: new Map(),
	}),

	// 动作：用于修改状态
	actions: {
		// 设置签约列表
		setContractList(list) {
			this.contractList = list || [];
		},

		// 更新单个签约项
		updateContractItem(id, updates) {
			console.log('更新签约项', id, updates);
			
			// 更新缓存列表中的项
			const index = this.contractList.findIndex(item => item.id === id);
			if (index !== -1) {
				this.contractList[index] = { ...this.contractList[index], ...updates };
			}
			
			// 记录待更新项，供列表页面使用
			this.pendingUpdates.set(id, updates);
		},

		// 获取并清除待更新项
		getPendingUpdate(id) {
			const update = this.pendingUpdates.get(id);
			if (update) {
				this.pendingUpdates.delete(id);
			}
			return update;
		},

		// 清除所有待更新项
		clearPendingUpdates() {
			this.pendingUpdates.clear();
		},

		// 发起签约
		initiateContract(id, contractData) {
			const updates = {
				status: 'contract_processing',
				status_text: '签约中',
				contract_data: contractData,
				can_initiate: false,
				can_view_progress: true
			};
			
			this.updateContractItem(id, updates);
		},

		// 完成签约
		completeContract(id) {
			const updates = {
				status: 'contract_completed',
				status_text: '签约完成',
				completion_time: new Date().toISOString(),
				can_initiate: false,
				can_view_progress: true
			};
			
			this.updateContractItem(id, updates);
		}
	},

	// 获取器
	getters: {
		// 获取签约列表
		getContractList: (state) => state.contractList,
		
		// 获取特定签约项
		getContractById: (state) => (id) => {
			return state.contractList.find(item => item.id === id);
		},

		// 检查是否有待更新项
		hasPendingUpdates: (state) => state.pendingUpdates.size > 0
	},
}); 