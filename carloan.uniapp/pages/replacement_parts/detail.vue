<template>
	<!-- 客户补件详情 -->
	<view class="supplement-detail">
		<navbar title="补件详情" />
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading" :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载完成' }" />
		</view>
		
		<!-- 详情内容 -->
		<view v-else-if="supplementDetail" class="detail-content">
			<!-- 基本信息 -->
			<view class="info-card">
				<view class="card-header">
					<view class="card-title">补件信息</view>
					<view class="status-badge" :class="getStatusClass(supplementDetail.status)">
						{{ supplementDetail.status_text }}
					</view>
				</view>
				<view class="info-list">
					<view class="info-item">
						<view class="label">补件单号：</view>
						<view class="value">{{ supplementDetail.supplement_no }}</view>
					</view>
					<view class="info-item">
						<view class="label">客户姓名：</view>
						<view class="value">{{ supplementDetail.application?.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="label">联系电话：</view>
						<view class="value">{{ supplementDetail.application?.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="label">业务单号：</view>
						<view class="value">{{ supplementDetail.application?.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="label">产品名称：</view>
						<view class="value">{{ supplementDetail.application?.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="label">贷款金额：</view>
						<view class="value">{{ formatAmount(supplementDetail.application?.loan_amount) }}万元</view>
					</view>
					<view class="info-item">
						<view class="label">创建时间：</view>
						<view class="value">{{ supplementDetail.created_time }}</view>
					</view>
					<view class="info-item" v-if="supplementDetail.deadline">
						<view class="label">截止时间：</view>
						<view class="value" :class="{ 'text-danger': supplementDetail.is_overdue }">
							{{ supplementDetail.deadline }}
							<text v-if="supplementDetail.remaining_days !== null" class="remaining-days">
								({{ supplementDetail.is_overdue ? '已逾期' : `剩余${supplementDetail.remaining_days}天` }})
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 补件原因和要求 -->
			<view class="info-card">
				<view class="card-title">补件要求</view>
				<view class="requirement-content">
					<view class="requirement-item">
						<view class="requirement-label">补件原因：</view>
						<view class="requirement-text">{{ supplementDetail.reason }}</view>
					</view>
					<view class="requirement-item">
						<view class="requirement-label">补件要求：</view>
						<view class="requirement-text">{{ supplementDetail.requirements }}</view>
					</view>
				</view>
			</view>

			<!-- 需要补充的文档 -->
			<view class="info-card" v-if="supplementDetail.required_documents && supplementDetail.required_documents.length > 0">
				<view class="card-title">需要补充的材料</view>
				<view class="document-list">
					<view 
						class="document-item" 
						v-for="(doc, index) in supplementDetail.required_documents" 
						:key="index"
					>
						<view class="doc-icon">
							<image 
								class="icon" 
								:src="getDocIcon(doc.type)" 
							/>
						</view>
						<view class="doc-info">
							<view class="doc-name">{{ doc.name }}</view>
							<view class="doc-type">{{ getDocTypeText(doc.type) }}</view>
						</view>
						<view class="doc-status">
							<text class="required-text" v-if="doc.required">必需</text>
							<text class="optional-text" v-else>可选</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 已提交的材料 -->
			<view class="info-card" v-if="supplementDetail.submitted_documents && supplementDetail.submitted_documents.length > 0">
				<view class="card-title">已提交的材料</view>
				<view class="submitted-list">
					<view 
						class="submitted-item" 
						v-for="(doc, index) in supplementDetail.submitted_documents" 
						:key="index"
						@click="previewDocument(doc)"
					>
						<view class="doc-icon">
							<image 
								class="icon" 
								:src="getDocIcon(doc.type)" 
							/>
						</view>
						<view class="doc-info">
							<view class="doc-name">{{ doc.name }}</view>
							<view class="doc-type">{{ getDocTypeText(doc.type) }}</view>
						</view>
						<view class="doc-action">
							<image 
								class="action-icon" 
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-preview.png"
							/>
						</view>
					</view>
				</view>
				
				<!-- 客户备注 -->
				<view class="customer-notes" v-if="supplementDetail.customer_notes">
					<view class="notes-label">客户备注：</view>
					<view class="notes-content">{{ supplementDetail.customer_notes }}</view>
				</view>
			</view>

			<!-- 审核信息 -->
			<view class="info-card" v-if="supplementDetail.status === 'approved' || supplementDetail.status === 'rejected'">
				<view class="card-title">审核信息</view>
				<view class="review-info">
					<view class="info-item" v-if="supplementDetail.reviewer">
						<view class="label">审核人：</view>
						<view class="value">{{ supplementDetail.reviewer.name }}</view>
					</view>
					<view class="info-item" v-if="supplementDetail.reviewed_time">
						<view class="label">审核时间：</view>
						<view class="value">{{ supplementDetail.reviewed_time }}</view>
					</view>
					<view class="info-item" v-if="supplementDetail.review_notes">
						<view class="label">审核意见：</view>
						<view class="value">{{ supplementDetail.review_notes }}</view>
					</view>
				</view>
			</view>

			<!-- 操作记录 -->
			<view class="info-card" v-if="supplementDetail.operation_log && supplementDetail.operation_log.length > 0">
				<view class="card-title">操作记录</view>
				<view class="operation-list">
					<view 
						class="operation-item" 
						v-for="(log, index) in supplementDetail.operation_log" 
						:key="index"
					>
						<view class="operation-dot"></view>
						<view class="operation-content">
							<view class="operation-desc">{{ log.description }}</view>
							<view class="operation-time">{{ log.created_at }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="action-bar" v-if="showActionButtons">
				<button 
					class="btn btn-white" 
					@click="onWithdraw"
					v-if="canWithdraw"
				>
					撤销补件
				</button>
				<button 
					class="btn btn-primary" 
					@click="onSubmit"
					v-if="canSubmit"
				>
					补充资料
				</button>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-else class="error-container">
			<view class="error-icon">
				<image src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/error.png" />
			</view>
			<view class="error-text">获取详情失败</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 页面参数
const supplementId = ref('')
const loading = ref(true)
const supplementDetail = ref(null)

// 计算属性
const canWithdraw = computed(() => {
	if (!supplementDetail.value) return false
	return ['pending', 'rejected'].includes(supplementDetail.value.status)
})

const canSubmit = computed(() => {
	if (!supplementDetail.value) return false
	return supplementDetail.value.status === 'pending'
})

const showActionButtons = computed(() => {
	return canWithdraw.value || canSubmit.value
})

// 页面加载
onMounted(() => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options
	
	if (options.id) {
		supplementId.value = options.id
		loadDetail()
	} else {
		loading.value = false
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		const response = await request.get(`/supplements/${supplementId.value}`)
		
		if (response && response.status_code === 200) {
			supplementDetail.value = response.data
			console.log('Supplement detail loaded:', response.data)
		} else {
			console.error('获取补件详情失败:', response)
			uni.showToast({
				title: response?.message || '获取详情失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('获取补件详情失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		'pending': 'warning',
		'submitted': 'info',
		'approved': 'success',
		'rejected': 'error'
	}
	return classMap[status] || 'default'
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return (parseFloat(amount) / 10000).toFixed(1)
}

// 获取文档图标
const getDocIcon = (type) => {
	const iconMap = {
		'image': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-image.png',
		'pdf': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-pdf.png',
		'doc': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-doc.png',
		'excel': 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-excel.png'
	}
	return iconMap[type] || 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-file.png'
}

// 获取文档类型文本
const getDocTypeText = (type) => {
	const typeMap = {
		'image': '图片',
		'pdf': 'PDF文档',
		'doc': 'Word文档',
		'excel': 'Excel文档'
	}
	return typeMap[type] || '文件'
}

// 预览文档
const previewDocument = (doc) => {
	if (doc.type === 'image') {
		// 图片预览
		uni.previewImage({
			urls: [doc.url],
			current: doc.url
		})
	} else {
		// 其他类型文档下载预览
		uni.downloadFile({
			url: doc.url,
			success: (res) => {
				if (res.statusCode === 200) {
					uni.openDocument({
						filePath: res.tempFilePath,
						success: () => {
							console.log('文档打开成功')
						},
						fail: (error) => {
							console.error('文档打开失败:', error)
							uni.showToast({
								title: '无法打开文档',
								icon: 'none'
							})
						}
					})
				}
			},
			fail: (error) => {
				console.error('文档下载失败:', error)
				uni.showToast({
					title: '下载失败',
					icon: 'none'
				})
			}
		})
	}
}

// 撤销补件
const onWithdraw = async () => {
	try {
		const result = await uni.showModal({
			title: '确认撤销',
			content: '确定要撤销这个补件吗？撤销后将无法恢复。',
			confirmText: '确认撤销',
			cancelText: '取消'
		})
		
		if (!result.confirm) {
			return
		}
		
		uni.showLoading({ title: '撤销中...' })
		
		const response = await request.delete(`/supplements/${supplementId.value}`)
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '撤销成功',
				icon: 'success'
			})
			
			// 返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			uni.showToast({
				title: response.message || '撤销失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('撤销补件失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}

// 补充资料
const onSubmit = () => {
	uni.navigateTo({
		url: `/pages/replacement_parts/submit?id=${supplementId.value}`
	})
}
</script>

<style lang="scss" scoped>
.supplement-detail {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 20rpx;
	
	.error-icon {
		margin-bottom: 20rpx;
		
		image {
			width: 120rpx;
			height: 120rpx;
		}
	}
	
	.error-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
	}
}

.detail-content {
	padding: 20rpx;
}

.info-card {
	background: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.card-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		
		.status-badge {
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			
			&.warning {
				background: #fff7e6;
				color: #fa8c16;
			}
			
			&.info {
				background: #e6f7ff;
				color: #1890ff;
			}
			
			&.success {
				background: #f6ffed;
				color: #52c41a;
			}
			
			&.error {
				background: #fff2f0;
				color: #ff4d4f;
			}
		}
	}
	
	.card-title {
		padding: 30rpx 30rpx 20rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
	}
}

.info-list {
	padding: 20rpx 30rpx 30rpx;
	
	.info-item {
		display: flex;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			width: 180rpx;
			font-size: 28rpx;
			color: #666;
			flex-shrink: 0;
		}
		
		.value {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			
			&.text-danger {
				color: #ff4d4f;
			}
			
			.remaining-days {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.requirement-content {
	padding: 20rpx 30rpx 30rpx;
	
	.requirement-item {
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.requirement-label {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 10rpx;
		}
		
		.requirement-text {
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}
	}
}

.document-list, .submitted-list {
	padding: 20rpx 30rpx 30rpx;
}

.document-item, .submitted-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.doc-icon {
		margin-right: 20rpx;
		
		.icon {
			width: 48rpx;
			height: 48rpx;
		}
	}
	
	.doc-info {
		flex: 1;
		
		.doc-name {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.doc-type {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.doc-status {
		.required-text {
			font-size: 24rpx;
			color: #ff4d4f;
		}
		
		.optional-text {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.doc-action {
		margin-left: 20rpx;
		
		.action-icon {
			width: 32rpx;
			height: 32rpx;
		}
	}
}

.customer-notes {
	padding: 20rpx 30rpx 0;
	border-top: 1rpx solid #f0f0f0;
	margin-top: 20rpx;
	
	.notes-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
	}
	
	.notes-content {
		font-size: 28rpx;
		color: #333;
		line-height: 1.6;
		padding: 20rpx;
		background: #f8f8f8;
		border-radius: 8rpx;
	}
}

.review-info {
	padding: 20rpx 30rpx 30rpx;
	
	.info-item {
		display: flex;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			width: 180rpx;
			font-size: 28rpx;
			color: #666;
			flex-shrink: 0;
		}
		
		.value {
			flex: 1;
			font-size: 28rpx;
			color: #333;
		}
	}
}

.operation-list {
	padding: 20rpx 30rpx 30rpx;
	
	.operation-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 30rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.operation-dot {
			width: 12rpx;
			height: 12rpx;
			background: #1890ff;
			border-radius: 50%;
			margin-right: 20rpx;
			margin-top: 10rpx;
			flex-shrink: 0;
		}
		
		.operation-content {
			flex: 1;
			
			.operation-desc {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.operation-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.action-bar {
	display: flex;
	justify-content: flex-end;
	gap: 16rpx;
	padding: 30rpx;
	background: #fff;
	margin-top: 20rpx;
	
	.btn {
		/* 重置按钮默认样式 */
		appearance: none;
		-webkit-appearance: none;
		outline: none;
		box-sizing: border-box;
		
		/* 统一尺寸和布局 */
		height: 76rpx;
		padding: 20rpx 32rpx;
		min-width: 160rpx;
		border-radius: 8rpx;
		border: none;
		
		/* 统一文字样式 */
		font-size: 28rpx;
		line-height: 1;
		text-align: center;
		white-space: nowrap;
		
		/* 统一交互效果 */
		cursor: pointer;
		transition: opacity 0.2s;
		
		&:active {
			opacity: 0.8;
		}
		
		&.btn-white {
			background: #fff;
			color: #666;
			border: 2rpx solid #d9d9d9;
		}
		
		&.btn-primary {
			background: #333;
			color: #fff;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 