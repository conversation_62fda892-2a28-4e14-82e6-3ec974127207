import Layout from '@/layout'

const customerRouter = {
  path: '/customer',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Customer',
  meta: {
    title: '客户管理',
    icon: 'el-icon-user-solid'
  },
  children: [
    {
      path: 'list',
      component: () => import('@/views/customer/CustomerList'),
      name: 'CustomerList',
      meta: { title: '客户列表' }
    },
    {
      path: 'bigdata',
      component: () => import('@/views/customer/BigDataList'),
      name: 'CustomerBigdata',
      meta: { title: '大数据查询管理' }
    }
  ]
}

export default customerRouter
