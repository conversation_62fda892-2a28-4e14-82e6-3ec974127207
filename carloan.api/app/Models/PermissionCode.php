<?php

namespace App\Models;

class PermissionCode extends BaseModel
{
    protected $fillable = [
        'code',
        'name',
        'group_name',
        'description',
        'frontend_route',
        'status'
    ];

    protected $casts = [
        'status' => 'integer'
    ];

    /**
     * 状态常量
     */
    const STATUS_DISABLED = 0;  // 禁用
    const STATUS_ENABLED = 1;   // 启用

    /**
     * 获取启用的权限码
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 根据分组获取权限码
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $groupName
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByGroup($query, $groupName)
    {
        return $query->where('group_name', $groupName);
    }

    /**
     * 获取所有权限分组
     *
     * @return array
     */
    public static function getGroups()
    {
        return self::enabled()
            ->distinct()
            ->pluck('group_name')
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * 获取分组的权限码
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getGroupedPermissions()
    {
        return self::enabled()
            ->orderBy('group_name')
            ->orderBy('code')
            ->get()
            ->groupBy('group_name');
    }

    /**
     * 检查权限码是否存在
     *
     * @param string $code
     * @return bool
     */
    public static function exists($code)
    {
        return self::where('code', $code)->exists();
    }
} 