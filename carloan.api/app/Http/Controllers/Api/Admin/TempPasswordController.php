<?php

namespace App\Http\Controllers\Api\Admin;

use Illuminate\Http\Request;
use App\Models\Admin;

class TempPasswordController extends BaseController
{
    /**
     * 生成临时密码（用于重置密码）
     * @param Request $request
     * @return \Dingo\Api\Http\Response|mixed
     */
    public function generatePassword(Request $request)
    {
        $plainPassword = $request->get('password', 'admin123');
        $hashedPassword = md5($plainPassword);
        
        return $this->response->array([
            'plain_password' => $plainPassword,
            'md5_password' => $hashedPassword,
            'message' => '请将 md5_password 的值保存到数据库的 password 字段中'
        ]);
    }
    
    /**
     * 更新管理员密码
     * @param Request $request
     * @return \Dingo\Api\Http\Response|mixed
     */
    public function updatePassword(Request $request)
    {
        $name = $request->post('name', '');
        $password = $request->post('password', '');
        
        if (!$name) {
            return $this->errorHandler('请输入用户名', 400);
        }
        
        if (!$password) {
            return $this->errorHandler('请输入新密码', 400);
        }
        
        $admin = Admin::where('name', $name)->first();
        if (!$admin) {
            return $this->errorHandler('用户不存在', 404);
        }
        
        $hashedPassword = md5($password);
        $admin->password = $hashedPassword;
        $admin->save();
        
        return $this->response->array([
            'message' => '密码更新成功',
            'admin_name' => $admin->name,
            'new_password' => $password
        ]);
    }
} 