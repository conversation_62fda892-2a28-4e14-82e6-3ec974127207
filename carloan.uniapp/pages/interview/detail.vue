<template>
	<!-- 面审详情 -->
	<view class="interview-detail">
		<navbar :title="pageTitle" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 详情内容 -->
		<view class="content" v-else-if="detail">
			<!-- 基本信息 -->
			<view class="section">
				<view class="section-title">基本信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">业务单号：</view>
						<view class="info-value">{{ detail.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">客户姓名：</view>
						<view class="info-value">{{ detail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">联系电话：</view>
						<view class="info-value">{{ detail.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">产品名称：</view>
						<view class="info-value">{{ detail.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">申请金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_amount) }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">提交时间：</view>
						<view class="info-value">{{ detail.submit_time || detail.created_time }}</view>
					</view>
				</view>
			</view>
			
			<!-- 车辆信息 -->
			<view class="section">
				<view class="section-title">车辆信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">车辆品牌：</view>
						<view class="info-value">{{ detail.vehicle_brand }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">车辆型号：</view>
						<view class="info-value">{{ detail.vehicle_model }}</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_year">
						<view class="info-label">车辆年份：</view>
						<view class="info-value">{{ detail.vehicle_year }}年</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_vin">
						<view class="info-label">车架号：</view>
						<view class="info-value">{{ detail.vehicle_vin }}</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_price">
						<view class="info-label">车辆价格：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.vehicle_price) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 面审状态 -->
			<view class="section">
				<view class="section-title">面审状态</view>
				<view class="section-content">
					<view class="status-info">
						<view class="status-badge" :class="getStatusClass(detail.status)">
							{{ detail.status_text }}
						</view>
						<view class="status-desc" v-if="detail.status === 'interview_pending'">
							您的申请已进入面审阶段，请及时预约面审时间
						</view>
						<view class="status-desc" v-else-if="detail.status === 'interview_scheduled'">
							面审已预约成功，请按时参加面审
						</view>
					</view>
				</view>
			</view>
			
			<!-- 预约信息 -->
			<view class="section" v-if="detail.appointment">
				<view class="section-title">预约信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">预约单号：</view>
						<view class="info-value">{{ detail.appointment.appointment_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">预约状态：</view>
						<view class="info-value">
							<text class="status-badge" :class="getAppointmentStatusClass(detail.appointment.status)">
								{{ detail.appointment.status_text }}
							</text>
						</view>
					</view>
					<view class="info-item" v-if="detail.appointment.appointment_time">
						<view class="info-label">面审时间：</view>
						<view class="info-value important">{{ formatDateTime(detail.appointment.appointment_time) }}</view>
					</view>
					<view class="info-item" v-if="detail.appointment.appointment_location">
						<view class="info-label">面审地点：</view>
						<view class="info-value">{{ detail.appointment.appointment_location }}</view>
					</view>
					<view class="info-item" v-if="detail.appointment.appointment_notes">
						<view class="info-label">预约备注：</view>
						<view class="info-value">{{ detail.appointment.appointment_notes }}</view>
					</view>
					<view class="info-item" v-if="detail.appointment.scheduled_time">
						<view class="info-label">预约时间：</view>
						<view class="info-value">{{ formatDateTime(detail.appointment.scheduled_time) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button 
					class="btn btn-outline" 
					@click="onCancelAppointment"
					v-if="canCancel"
				>
					取消预约
				</button>
				<button 
					class="btn btn-primary" 
					@click="onScheduleAppointment"
					v-if="canSchedule"
				>
					预约面审
				</button>
				<button 
					class="btn btn-primary" 
					@click="onModifyAppointment"
					v-if="canModify"
				>
					修改预约
				</button>
				<button 
					class="btn btn-success" 
					@click="onCompleteInterview"
					v-if="canComplete"
				>
					完成面审
				</button>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else>
			<view class="error-icon">❌</view>
			<view class="error-text">{{ errorMessage || '数据加载失败' }}</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"
import { useInterviewStore } from "@/stores/interview"

// 响应式数据
const loading = ref(true)
const detail = ref(null)
const errorMessage = ref('')
const interviewId = ref('')

// 状态管理
const interviewStore = useInterviewStore()

// 计算属性
const pageTitle = computed(() => {
	return detail.value ? '面审详情' : '面审详情'
})

// 判断是否可以预约
const canSchedule = computed(() => {
	return detail.value && (
		detail.value.can_schedule || 
		detail.value.status === 'interview_pending'
	) && !detail.value.appointment
})

// 判断是否可以取消预约
const canCancel = computed(() => {
	return detail.value && detail.value.appointment && (
		detail.value.can_cancel || 
		detail.value.appointment.status === 'scheduled'
	)
})

// 判断是否可以修改预约
const canModify = computed(() => {
	return detail.value && detail.value.appointment && 
		detail.value.appointment.status === 'scheduled'
})

// 判断是否可以完成面审
const canComplete = computed(() => {
	return detail.value && detail.value.appointment && 
		detail.value.appointment.status === 'scheduled' &&
		detail.value.status === 'interview_scheduled'
})

// 页面加载
onLoad((options) => {
	if (options.id) {
		interviewId.value = options.id
		loadDetail()
	} else {
		errorMessage.value = '参数错误'
		loading.value = false
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		errorMessage.value = ''
		
		const response = await request.get(`/interviews/${interviewId.value}`)
		
		console.log('面审详情API Response:', response)
		
		if (response && response.status_code === 200) {
			detail.value = response.data
		} else {
			errorMessage.value = response?.message || '获取数据失败'
		}
	} catch (error) {
		console.error('获取面审详情失败:', error)
		errorMessage.value = '网络错误，请重试'
	} finally {
		loading.value = false
	}
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return Number(amount).toLocaleString()
}

// 格式化日期时间
const formatDateTime = (timeStr) => {
	if (!timeStr) return ''
	try {
		const date = new Date(timeStr)
		return date.toLocaleString('zh-CN', {
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit'
		})
	} catch (e) {
		return timeStr
	}
}

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		'interview_pending': 'warning',
		'interview_scheduled': 'info',
		'interview_completed': 'success',
		'rejected': 'error'
	}
	return classMap[status] || 'default'
}

// 获取预约状态样式类
const getAppointmentStatusClass = (status) => {
	const classMap = {
		'pending': 'warning',
		'scheduled': 'info',
		'completed': 'success',
		'cancelled': 'error'
	}
	return classMap[status] || 'default'
}

// 预约面审
const onScheduleAppointment = () => {
	uni.navigateTo({
		url: `/pages/interview/schedule?id=${interviewId.value}`
	})
}

// 修改预约
const onModifyAppointment = () => {
	uni.navigateTo({
		url: `/pages/interview/schedule?id=${interviewId.value}&mode=edit`
	})
}

// 取消预约
const onCancelAppointment = async () => {
	try {
		const result = await uni.showModal({
			title: '确认取消',
			content: '确定要取消面审预约吗？',
			confirmText: '确认取消',
			cancelText: '取消'
		})
		
		if (!result.confirm) {
			return
		}
		
		// 询问取消原因
		const reasonResult = await uni.showModal({
			title: '取消原因',
			content: '请说明取消预约的原因',
			editable: true,
			placeholderText: '请输入取消原因',
			confirmText: '确认',
			cancelText: '取消'
		})
		
		if (!reasonResult.confirm) {
			return
		}
		
		const cancelReason = reasonResult.content || '用户主动取消'
		
		uni.showLoading({ title: '取消中...' })
		
		const response = await request.post(`/interviews/${interviewId.value}/cancel`, {
			cancel_reason: cancelReason
		})
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '取消预约成功',
				icon: 'success'
			})
			
			// 更新 store 状态
			interviewStore.cancelAppointment(interviewId.value)
			
			// 重新加载详情
			setTimeout(() => {
				loadDetail()
			}, 1000)
		} else {
			const errorMessage = response.message || '取消预约失败'
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('取消预约失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}

// 完成面审
const onCompleteInterview = async () => {
	try {
		const result = await uni.showModal({
			title: '完成面审',
			content: '请确认面审结果',
			confirmText: '通过',
			cancelText: '不通过'
		})
		
		// result.confirm 为 true 表示面审通过，false 表示面审不通过
		const interviewResult = result.confirm
		
		// 如果面审不通过，询问原因
		let interviewNotes = ''
		if (!interviewResult) {
			const reasonResult = await uni.showModal({
				title: '面审不通过原因',
				content: '请说明面审不通过的原因',
				editable: true,
				placeholderText: '请输入不通过原因',
				confirmText: '确认',
				cancelText: '取消'
			})
			
			if (!reasonResult.confirm) {
				return
			}
			
			interviewNotes = reasonResult.content || '面审不通过'
		} else {
			// 面审通过，可以添加备注
			const notesResult = await uni.showModal({
				title: '面审备注',
				content: '请添加面审备注（可选）',
				editable: true,
				placeholderText: '请输入面审备注',
				confirmText: '确认',
				cancelText: '跳过'
			})
			
			if (notesResult.confirm) {
				interviewNotes = notesResult.content || '面审通过'
			}
		}
		
		uni.showLoading({ title: '处理中...' })
		
		const response = await request.post(`/interviews/${interviewId.value}/complete`, {
			interview_result: interviewResult,
			interview_notes: interviewNotes
		})
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: response.message || '面审完成',
				icon: 'success'
			})
			
			// 更新 store 状态
			interviewStore.completeInterview(interviewId.value, interviewResult)
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			const errorMessage = response.message || '完成面审失败'
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('完成面审失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
.interview-detail {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		background: #f8f9fa;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.section-content {
		padding: 30rpx;
	}
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.info-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
		flex-shrink: 0;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		
		&.amount {
			color: #e6a23c;
			font-weight: 500;
		}
		
		&.important {
			color: #409eff;
			font-weight: 500;
		}
	}
}

.status-info {
	text-align: center;
	
	.status-badge {
		display: inline-block;
		padding: 12rpx 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		
		&.warning {
			background: #fff7e6;
			color: #fa8c16;
		}
		
		&.info {
			background: #e6f7ff;
			color: #1890ff;
		}
		
		&.success {
			background: #f6ffed;
			color: #52c41a;
		}
		
		&.error {
			background: #fff2f0;
			color: #ff4d4f;
		}
		
		&.default {
			background: #f0f0f0;
			color: #666;
		}
	}
	
	.status-desc {
		margin-top: 20rpx;
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
}

.action-buttons {
	padding: 40rpx 20rpx;
	display: flex;
	gap: 20rpx;
	
	.btn {
		flex: 1;
		height: 88rpx;
		border-radius: 16rpx;
		border: none;
		font-size: 32rpx;
		font-weight: 500;
		
		&.btn-primary {
			background: #007aff;
			color: #fff;
		}
		
		&.btn-success {
			background: #52c41a;
			color: #fff;
		}
		
		&.btn-outline {
			background: #fff;
			color: #666;
			border: 2rpx solid #ddd;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.error-state {
	padding: 200rpx 40rpx;
	text-align: center;
	
	.error-icon {
		font-size: 100rpx;
		margin-bottom: 40rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 60rpx;
	}
	
	.btn {
		width: 200rpx;
		height: 80rpx;
		background: #007aff;
		color: #fff;
		border: none;
		border-radius: 12rpx;
		font-size: 28rpx;
	}
}
</style>

<style lang="scss">
page {
	background: #f5f5f5;
}
</style> 