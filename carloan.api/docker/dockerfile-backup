FROM php:7.4-fpm

# 更改为更快的镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装必要的 PHP 扩展和依赖
RUN apt-get update --fix-missing && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libwebp-dev \
    libxpm-dev \
    libzip-dev \
    zlib1g-dev \
    libonig-dev \
    libssl-dev \
    libxml2-dev \
    build-essential \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp --with-xpm \
    && docker-php-ext-install -j$(nproc) bcmath gd zip pdo_mysql

# 安装 Redis 扩展
RUN pecl install redis && docker-php-ext-enable redis

# 安装 Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# 安装其他必要的软件包
RUN apt-get update --fix-missing && apt-get install -y git

# 复制项目文件
COPY . /carloan.api
WORKDIR /carloan.api

# 安装项目依赖
RUN composer install --no-interaction --no-dev --optimize-autoloader

# 设置权限
RUN chmod -R 777 /carloan.api
