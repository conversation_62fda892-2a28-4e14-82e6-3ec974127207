<?php

namespace App\Transformers\Web;

use App\Models\Customer;
use League\Fractal\TransformerAbstract;

class CustomerTransformer extends TransformerAbstract
{
    public function transform(Customer $customer)
    {
        // 获取最新的风险查询记录
        $latestRiskQuery = $customer->riskQueries()->latest()->first();
        
        return [
            'id' => $customer->id,
            'phone' => $customer->phone,
            'name' => $customer->name,
            'id_card' => $customer->id_card,
            'gender' => $customer->gender,
            'gender_text' => $customer->gender_text,
            'nation' => $customer->nation,
            'address' => $customer->address,
            'authority' => $customer->authority,
            'valid_start' => $customer->valid_start ? $customer->valid_start->toDateString() : '',
            'valid_end' => $customer->valid_end ? $customer->valid_end->toDateString() : '',
            'status' => $customer->status,
            'status_text' => $customer->status_text,
            'age' => $customer->age,
            'is_id_card_valid' => $customer->isIdCardValid(),
            'created_by' => $customer->created_by,
            'creator_name' => $customer->creator ? $customer->creator->name : '',
            'created_at' => $customer->created_at ? $customer->created_at->toDateTimeString() : '',
            'updated_at' => $customer->updated_at ? $customer->updated_at->toDateTimeString() : '',
            // 风险查询相关信息
            'risk_status' => $latestRiskQuery ? $latestRiskQuery->status : null,
            'risk_score' => $latestRiskQuery ? $latestRiskQuery->risk_score : null,
            'risk_level' => $latestRiskQuery ? $latestRiskQuery->risk_level : null,
            'latest_query_time' => $latestRiskQuery && $latestRiskQuery->query_time ? $latestRiskQuery->query_time->toDateTimeString() : null,
        ];
    }
} 