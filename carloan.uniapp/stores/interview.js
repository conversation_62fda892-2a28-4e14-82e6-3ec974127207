import { defineStore } from "pinia";
import { debug } from "@/utils/debug";

export const useInterviewStore = defineStore("interview", {
	// 状态：用于存储面审相关信息
	state: () => ({
		// 面审列表缓存
		interviewList: [],
		// 待更新的面审项ID和数据
		pendingUpdates: new Map(),
	}),

	// 动作：用于修改状态
	actions: {
		// 设置面审列表
		setInterviewList(list) {
			this.interviewList = list || [];
		},

		// 更新单个面审项
		updateInterviewItem(id, updates) {
			debug.interview.updateList('更新面审项', id, updates);
			
			// 更新缓存列表中的项
			const index = this.interviewList.findIndex(item => item.id === id);
			if (index !== -1) {
				this.interviewList[index] = { ...this.interviewList[index], ...updates };
			}
			
			// 记录待更新项，供列表页面使用
			this.pendingUpdates.set(id, updates);
			
			debug.interview.storeState(this);
		},

		// 获取并清除待更新项
		getPendingUpdate(id) {
			const update = this.pendingUpdates.get(id);
			if (update) {
				this.pendingUpdates.delete(id);
			}
			return update;
		},

		// 清除所有待更新项
		clearPendingUpdates() {
			this.pendingUpdates.clear();
		},

		// 完成面审
		completeInterview(id, result) {
			const updates = {
				status: result ? 'interview_completed' : 'rejected',
				status_text: result ? '面审完成' : '面审不通过',
				interview_result: result,
				can_schedule: false,
				can_cancel: false,
				can_modify: false,
				can_complete: false
			};
			
			this.updateInterviewItem(id, updates);
		},

		// 取消预约
		cancelAppointment(id) {
			const updates = {
				status: 'interview_pending',
				status_text: '待面审',
				appointment: null,
				can_schedule: true,
				can_cancel: false,
				can_modify: false
			};
			
			this.updateInterviewItem(id, updates);
		},

		// 预约面审
		scheduleAppointment(id, appointmentData) {
			const updates = {
				status: 'interview_scheduled',
				status_text: '已预约面签',
				appointment: appointmentData,
				can_schedule: false,
				can_cancel: true,
				can_modify: true
			};
			
			this.updateInterviewItem(id, updates);
		},

		// 修改预约
		modifyAppointment(id, appointmentData) {
			const updates = {
				appointment: appointmentData
			};
			
			this.updateInterviewItem(id, updates);
		}
	},

	// 获取器
	getters: {
		// 获取面审列表
		getInterviewList: (state) => state.interviewList,
		
		// 获取特定面审项
		getInterviewById: (state) => (id) => {
			return state.interviewList.find(item => item.id === id);
		},

		// 检查是否有待更新项
		hasPendingUpdates: (state) => state.pendingUpdates.size > 0
	},
}); 