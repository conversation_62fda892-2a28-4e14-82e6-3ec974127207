-- 测试签约功能的完整业务数据
-- 客户信息：郝云 ***********
-- 目的：测试e签宝签约短信功能

-- 设置会话字符集
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 开始事务
START TRANSACTION;

-- 设置变量用于回滚检查
SET @error_count = 0;

-- 1. 确保有测试用户（业务员）
INSERT IGNORE INTO `users` (
    `id`, `phone`, `nickname`, `name`, `channel_code`, `status`, 
    `register_time`, `login_time`, `created_at`, `updated_at`
) VALUES 
(99, '13900139999', '测试业务员', '测试业务员', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW());

-- 2. 确保有测试渠道
INSERT IGNORE INTO `channels` (
    `code`, `name`, `status`, `sort`, `created_at`, `updated_at`
) VALUES 
('DIRECT', '直营渠道', 1, 1, NOW(), NOW());

-- 3. 确保有测试金融产品
INSERT IGNORE INTO `financial_products` (
    `id`, `product_code`, `product_name`, `product_type`, `annual_rate`, 
    `min_amount`, `max_amount`, `supported_periods`, `status`, `sort_order`,
    `created_at`, `updated_at`
) VALUES 
(1, 'CAR_LOAN_24', '24个月车贷产品', 'loan', 0.0850, 50000.00, 500000.00, 
 JSON_ARRAY(12, 24, 36), 1, 1, NOW(), NOW());

-- 4. 生成唯一的申请编号
SET @application_no = CONCAT('APP', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), FLOOR(1000 + RAND() * 9000));

-- 5. 创建业务申请记录（状态为待签约）
INSERT INTO `business_applications` (
    `application_no`,
    `user_id`,
    `channel_code`,
    `customer_name`,
    `customer_phone`,
    `customer_id_card`,
    `product_name`,
    `loan_amount`,
    `loan_period`,
    `interest_rate`,
    `vehicle_brand`,
    `vehicle_model`,
    `vehicle_vin`,
    `vehicle_year`,
    `vehicle_price`,
    `status`,
    `status_text`,
    `approval_notes`,
    `approval_history`,
    `submit_time`,
    `approval_time`,
    `need_supplement`,
    `customer_data`,
    `risk_assessment`,
    `attachments`,
    `created_at`,
    `updated_at`
) VALUES (
    @application_no,                    -- 申请编号
    99,                                 -- 业务员ID
    'DIRECT',                           -- 渠道代码
    '郝云',                             -- 客户姓名
    '***********',                      -- 客户手机号（测试用）
    '320106199001011234',               -- 测试身份证号
    '24个月车贷产品',                    -- 产品名称
    180000.00,                          -- 贷款金额
    24,                                 -- 贷款期限
    0.0850,                             -- 利率
    '奥迪',                             -- 车辆品牌
    'A4L',                              -- 车辆型号
    'WAUZZZ8K1MA123456',               -- 车架号
    2023,                               -- 车辆年份
    220000.00,                          -- 车辆价格
    'contract_pending',                 -- 状态：待签约
    '待签约',                           -- 状态文本
    '终审通过，客户资质良好，可以进行合同签约', -- 审批备注
    CAST('[
        {
            "stage": "submitted",
            "status": "completed", 
            "operator_id": 99,
            "operator_name": "测试业务员",
            "notes": "客户提交贷款申请",
            "created_at": "2024-12-20 10:00:00"
        },
        {
            "stage": "initial_review",
            "status": "completed",
            "operator_id": 99, 
            "operator_name": "测试业务员",
            "notes": "初审通过，风险评估良好",
            "created_at": "2024-12-21 10:00:00"
        },
        {
            "stage": "interview", 
            "status": "completed",
            "operator_id": 99,
            "operator_name": "测试业务员", 
            "notes": "面审完成，客户收入稳定，还款能力良好",
            "created_at": "2024-12-22 10:00:00"
        },
        {
            "stage": "final_review",
            "status": "completed", 
            "operator_id": 99,
            "operator_name": "测试业务员",
            "notes": "终审通过，准予放贷，可进行合同签约", 
            "created_at": "2024-12-24 10:00:00"
        }
    ]' AS JSON),                        -- 审批历史
    DATE_SUB(NOW(), INTERVAL 5 DAY),    -- 提交时间
    DATE_SUB(NOW(), INTERVAL 1 DAY),    -- 审批时间
    0,                                  -- 不需要补件
    CAST('{
        "basic_info": {
            "name": "郝云",
            "phone": "***********",
            "id_card": "320106199001011234",
            "gender": "male",
            "age": 34,
            "education": "bachelor",
            "marital_status": "married"
        },
        "contact_info": {
            "address": "江苏省南京市鼓楼区测试路123号",
            "emergency_contact": "李梅", 
            "emergency_phone": "13912345678",
            "emergency_relation": "spouse"
        },
        "work_info": {
            "company": "南京测试科技有限公司",
            "position": "软件工程师",
            "monthly_income": 15000,
            "work_years": 8
        },
        "vehicle_info": {
            "brand": "奥迪",
            "model": "A4L", 
            "vin": "WAUZZZ8K1MA123456",
            "year": 2023,
            "price": 220000
        }
    }' AS JSON),                        -- 客户详细数据
    CAST('{
        "total_score": 85,
        "risk_level": "low",
        "assessment_date": "2024-12-25",
        "details": {
            "credit_score": 90,
            "income_stability": 85,
            "debt_ratio": 75,
            "vehicle_value": 88
        }
    }' AS JSON),                        -- 风险评估结果
    CAST('[
        {
            "name": "身份证正面",
            "type": "id_card_front",
            "url": "https://example.com/id_front.jpg",
            "size": 1024000
        },
        {
            "name": "身份证反面", 
            "type": "id_card_back",
            "url": "https://example.com/id_back.jpg",
            "size": 1024000
        },
        {
            "name": "收入证明",
            "type": "income_proof", 
            "url": "https://example.com/income.pdf",
            "size": 2048000
        },
        {
            "name": "银行流水",
            "type": "bank_statement",
            "url": "https://example.com/bank.pdf", 
            "size": 3072000
        }
    ]' AS JSON),                        -- 附件列表
    NOW(),                              -- 创建时间
    NOW()                               -- 更新时间
);

-- 检查业务申请插入是否成功，记录错误
SET @error_count = @error_count + CASE WHEN ROW_COUNT() = 0 THEN 1 ELSE 0 END;

-- 6. 获取刚插入的业务申请ID
SET @application_id = LAST_INSERT_ID();

-- 检查是否获取到有效ID
SET @error_count = @error_count + CASE WHEN @application_id = 0 THEN 1 ELSE 0 END;

-- 7. 创建对应的风险查询记录
INSERT INTO `customer_risk_queries` (
    `user_id`,
    `customer_name`,
    `customer_phone`,
    `customer_id_card`,
    `status`,
    `risk_score`,
    `risk_level`,
    `query_time`,
    `query_results`,
    `notes`,
    `created_at`,
    `updated_at`
) VALUES (
    99,                                 -- 业务员ID
    '郝云',                             -- 客户姓名
    '***********',                      -- 客户手机号
    '320106199001011234',               -- 客户身份证号
    'completed',                        -- 查询状态：已完成
    85,                                 -- 风险评分
    'low',                              -- 风险等级：低风险
    DATE_SUB(NOW(), INTERVAL 4 DAY),    -- 查询时间
    CAST('{
        "credit_check": {
            "score": 90,
            "status": "good"
        },
        "litigation_check": {
            "count": 0,
            "status": "clean"
        },
        "blacklist_check": {
            "found": false,
            "status": "clear"
        }
    }' AS JSON),                        -- 查询结果详情
    '客户信用记录良好，无不良征信记录',    -- 备注
    DATE_SUB(NOW(), INTERVAL 4 DAY),    -- 创建时间
    DATE_SUB(NOW(), INTERVAL 4 DAY)     -- 更新时间
);

-- 检查风险查询记录插入
SET @error_count = @error_count + CASE WHEN ROW_COUNT() = 0 THEN 1 ELSE 0 END;

-- 8. 删除可能存在的空flow_id记录（避免唯一约束冲突）
DELETE FROM `esign_contracts` WHERE `flow_id` = '' OR `flow_id` IS NULL;

-- 9. 生成唯一的临时flow_id用于测试
SET @temp_flow_id = CONCAT('TEST_FLOW_', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '_', FLOOR(1000 + RAND() * 9000));

-- 10. 创建e签宝合同记录
INSERT INTO `esign_contracts` (
    `application_id`,
    `flow_id`,
    `account_id`,
    `contract_name`,
    `contract_status`,
    `sign_url`,
    `initiated_time`,
    `created_at`,
    `updated_at`
) VALUES (
    @application_id,                    -- 业务申请ID
    @temp_flow_id,                      -- 临时唯一的flow_id
    '',                                 -- e签宝账户ID（发起签约时更新）
    CONCAT('车贷合同_', @application_no, '.pdf'), -- 合同名称
    'pending',                          -- 合同状态：待处理
    '',                                 -- 签署链接（发起签约时更新）
    NULL,                               -- 发起时间（发起签约时更新）
    NOW(),                              -- 创建时间
    NOW()                               -- 更新时间
);

-- 检查e签宝合同记录插入
SET @error_count = @error_count + CASE WHEN ROW_COUNT() = 0 THEN 1 ELSE 0 END;

-- 11. 显示生成的测试数据信息
SELECT 
    CASE 
        WHEN @error_count = 0 THEN '=== 测试数据生成成功 ==='
        ELSE '=== 数据生成过程中有错误 ==='
    END as message,
    @application_no as application_no,
    @application_id as application_id,
    @temp_flow_id as temp_flow_id,
    '郝云' as customer_name,
    '***********' as customer_phone,
    'contract_pending' as status,
    @error_count as error_count,
    CASE 
        WHEN @error_count = 0 THEN '可以开始测试签约短信功能'
        ELSE '请检查错误后重试'
    END as note;

-- 12. 验证数据（只在成功时显示）
SELECT 
    id,
    application_no,
    customer_name,
    customer_phone,
    status,
    status_text,
    loan_amount,
    created_at
FROM business_applications 
WHERE application_no = @application_no;

-- 13. 验证e签宝合同记录
SELECT 
    id,
    application_id,
    flow_id,
    contract_name,
    contract_status,
    created_at
FROM esign_contracts 
WHERE application_id = @application_id;

-- 显示错误计数
SELECT 
    @error_count as total_errors,
    CASE 
        WHEN @error_count = 0 THEN '没有错误发生'
        ELSE CONCAT('发生了 ', @error_count, ' 个错误')
    END as error_status;

-- 根据错误计数决定提交或回滚
SET @sql = CASE 
    WHEN @error_count = 0 THEN 'COMMIT'
    ELSE 'ROLLBACK'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示最终结果
SELECT 
    CASE 
        WHEN @error_count = 0 THEN '✅ 事务已提交，测试数据创建成功！'
        ELSE '❌ 事务已回滚，请检查上述错误！'
    END as final_result,
    @error_count as error_count; 