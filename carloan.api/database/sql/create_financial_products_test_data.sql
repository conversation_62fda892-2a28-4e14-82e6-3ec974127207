-- 金融产品测试数据
-- 创建时间: 2024-03-17
-- 说明: 车贷金融产品测试数据，包含多种产品类型和利率

-- 0. 创建表（如果不存在）
CREATE TABLE IF NOT EXISTS `financial_products` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品代码',
  `product_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `product_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'loan' COMMENT '产品类型:loan贷款,lease租赁',
  `annual_rate` decimal(6,4) NOT NULL DEFAULT '0.0000' COMMENT '年利率',
  `min_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最低贷款额度',
  `max_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最高贷款额度',
  `supported_periods` json NOT NULL COMMENT '支持的期数列表',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '产品描述',
  `features` json DEFAULT NULL COMMENT '产品特色',
  `conditions` json DEFAULT NULL COMMENT '申请条件',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `financial_products_product_code_unique` (`product_code`),
  KEY `financial_products_product_type_index` (`product_type`),
  KEY `financial_products_status_index` (`status`),
  KEY `financial_products_status_sort_index` (`status`,`sort_order`),
  KEY `financial_products_annual_rate_index` (`annual_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金融产品表';

CREATE TABLE IF NOT EXISTS `loan_periods` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `period_months` int(11) NOT NULL COMMENT '期数（月）',
  `period_label` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '期数标签',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认期数:1是,0否',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `loan_periods_period_months_unique` (`period_months`),
  KEY `loan_periods_status_index` (`status`),
  KEY `loan_periods_is_default_index` (`is_default`),
  KEY `loan_periods_status_sort_index` (`status`,`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='贷款期数表';

-- 1. 清理现有测试数据（可选）
-- DELETE FROM `financial_products` WHERE `product_code` LIKE 'TEST_%';
-- DELETE FROM `loan_periods`;

-- 2. 插入贷款期数数据
INSERT IGNORE INTO `loan_periods` (
  `period_months`, `period_label`, `is_default`, `status`, `sort_order`, `created_at`, `updated_at`
) VALUES 
(12, '12期', 0, 1, 1, NOW(), NOW()),
(24, '24期', 1, 1, 2, NOW(), NOW()),
(36, '36期', 1, 1, 3, NOW(), NOW()),
(48, '48期', 0, 1, 4, NOW(), NOW()),
(60, '60期', 0, 1, 5, NOW(), NOW());

-- 3. 插入金融产品测试数据
INSERT IGNORE INTO `financial_products` (
  `product_code`, `product_name`, `product_type`, `annual_rate`, `min_amount`, `max_amount`,
  `supported_periods`, `description`, `features`, `conditions`, `status`, `sort_order`,
  `created_at`, `updated_at`
) VALUES 

-- 标准贷款产品
(
  'STANDARD_LOAN', '标准贷', 'loan', 0.0800, 10000.00, 500000.00,
  JSON_ARRAY(12, 24, 36, 48, 60),
  '标准车贷产品，利率优惠，审批快速',
  JSON_ARRAY(
    '利率优惠，最低8%起',
    '审批快速，3个工作日内放款',
    '支持多种还款方式',
    '提前还款无违约金'
  ),
  JSON_OBJECT(
    'min_income', 5000,
    'min_work_years', 1,
    'min_age', 22,
    'max_age', 60,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '银行流水', '驾驶证')
  ),
  1, 1, NOW(), NOW()
),

-- 快速贷款产品
(
  'QUICK_LOAN', '快速贷', 'loan', 0.1000, 5000.00, 300000.00,
  JSON_ARRAY(12, 24, 36),
  '快速审批车贷产品，当天放款',
  JSON_ARRAY(
    '极速审批，1小时内初审',
    '当天放款',
    '门槛相对较低',
    '手续简便'
  ),
  JSON_OBJECT(
    'min_income', 3000,
    'min_work_years', 0.5,
    'min_age', 20,
    'max_age', 55,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '驾驶证')
  ),
  1, 2, NOW(), NOW()
),

-- 优享贷款产品
(
  'PREMIUM_LOAN', '优享贷', 'loan', 0.0600, 50000.00, 1000000.00,
  JSON_ARRAY(24, 36, 48, 60),
  '优质客户专享，超低利率车贷产品',
  JSON_ARRAY(
    '超低利率，6%起',
    '高额度，最高100万',
    '优质客户专享',
    '专属客户经理服务',
    '灵活还款期限'
  ),
  JSON_OBJECT(
    'min_income', 10000,
    'min_work_years', 3,
    'min_age', 25,
    'max_age', 50,
    'min_credit_score', 700,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '银行流水', '工作证明', '资产证明', '驾驶证')
  ),
  1, 3, NOW(), NOW()
),

-- 中期贷款产品
(
  'MEDIUM_LOAN', '中期贷', 'loan', 0.0750, 20000.00, 400000.00,
  JSON_ARRAY(18, 30, 42),
  '中期车贷产品，平衡利率与期限',
  JSON_ARRAY(
    '合理利率7.5%',
    '灵活期限选择',
    '适中门槛要求',
    '稳定还款计划'
  ),
  JSON_OBJECT(
    'min_income', 6000,
    'min_work_years', 2,
    'min_age', 23,
    'max_age', 58,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '银行流水', '驾驶证')
  ),
  1, 4, NOW(), NOW()
),

-- 以租代售产品
(
  'LEASE_TO_OWN', '以租代售', 'lease', 0.0850, 30000.00, 800000.00,
  JSON_ARRAY(24, 36, 48),
  '以租代售车贷产品，租期结束可选择购买',
  JSON_ARRAY(
    '低首付，降低资金压力',
    '租期结束可选择购买',
    '灵活的车辆使用方案',
    '包含车辆保险和维护'
  ),
  JSON_OBJECT(
    'min_income', 7000,
    'min_work_years', 2,
    'min_age', 25,
    'max_age', 55,
    'down_payment_ratio', 0.2,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '银行流水', '驾驶证', '居住证明')
  ),
  1, 5, NOW(), NOW()
),

-- 融资租赁产品
(
  'FINANCE_LEASE', '融资租赁', 'lease', 0.0900, 50000.00, 1200000.00,
  JSON_ARRAY(36, 48, 60),
  '融资租赁车贷产品，适合企业客户',
  JSON_ARRAY(
    '适合企业客户',
    '税务优惠',
    '资产负债表优化',
    '灵活的租赁条款',
    '专业化服务'
  ),
  JSON_OBJECT(
    'min_income', 15000,
    'min_work_years', 3,
    'min_age', 25,
    'max_age', 60,
    'client_type', 'enterprise',
    'required_documents', JSON_ARRAY('营业执照', '税务登记证', '财务报表', '银行流水', '法人身份证')
  ),
  1, 6, NOW(), NOW()
),

-- 新能源车专享产品
(
  'NEW_ENERGY_LOAN', '新能源专享贷', 'loan', 0.0550, 15000.00, 600000.00,
  JSON_ARRAY(24, 36, 48, 60),
  '新能源车专享贷款产品，政策支持超低利率',
  JSON_ARRAY(
    '新能源车专享',
    '政策支持超低利率5.5%',
    '绿色金融产品',
    '环保贡献奖励',
    '快速审批通道'
  ),
  JSON_OBJECT(
    'min_income', 6000,
    'min_work_years', 1,
    'min_age', 22,
    'max_age', 60,
    'vehicle_type', 'new_energy',
    'required_documents', JSON_ARRAY('身份证', '收入证明', '银行流水', '驾驶证', '车辆环保证明')
  ),
  1, 7, NOW(), NOW()
),

-- 二手车贷款产品
(
  'USED_CAR_LOAN', '二手车贷', 'loan', 0.0950, 8000.00, 300000.00,
  JSON_ARRAY(12, 24, 36),
  '二手车专用贷款产品，手续简便',
  JSON_ARRAY(
    '二手车专用',
    '手续相对简便',
    '快速评估',
    '灵活额度',
    '短期为主'
  ),
  JSON_OBJECT(
    'min_income', 4000,
    'min_work_years', 1,
    'min_age', 20,
    'max_age', 55,
    'vehicle_age_limit', 8,
    'required_documents', JSON_ARRAY('身份证', '收入证明', '驾驶证', '车辆评估报告', '车辆登记证')
  ),
  1, 8, NOW(), NOW()
);

-- 4. 验证数据插入结果
SELECT 
  id,
  product_code,
  product_name,
  product_type,
  annual_rate,
  min_amount,
  max_amount,
  JSON_EXTRACT(supported_periods, '$') as supported_periods,
  status,
  sort_order
FROM financial_products
WHERE status = 1
ORDER BY sort_order;

-- 5. 统计各产品类型数量
SELECT 
  product_type,
  COUNT(*) as count,
  MIN(annual_rate) as min_rate,
  MAX(annual_rate) as max_rate,
  AVG(annual_rate) as avg_rate
FROM financial_products 
WHERE status = 1
GROUP BY product_type
ORDER BY count DESC;

-- 6. 查看期数数据
SELECT 
  period_months,
  period_label,
  is_default,
  status,
  sort_order
FROM loan_periods
WHERE status = 1
ORDER BY sort_order;

-- 7. 清理脚本（备用，慎用）
-- DELETE FROM financial_products WHERE product_code IN ('STANDARD_LOAN', 'QUICK_LOAN', 'PREMIUM_LOAN', 'MEDIUM_LOAN', 'LEASE_TO_OWN', 'FINANCE_LEASE', 'NEW_ENERGY_LOAN', 'USED_CAR_LOAN');
-- DELETE FROM loan_periods WHERE period_months IN (12, 24, 36, 48, 60); 