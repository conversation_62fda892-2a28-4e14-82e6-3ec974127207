<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateThirdPartyApiLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('third_party_api_logs', function (Blueprint $table) {
            $table->id();
            $table->string('service_name', 50)->comment('服务名称(如:esign,jizhengyun)');
            $table->string('api_name', 100)->comment('接口名称');
            $table->string('method', 10)->comment('请求方法(GET/POST/PUT等)');
            $table->text('request_url')->comment('请求地址');
            $table->json('request_headers')->nullable()->comment('请求头');
            $table->json('request_params')->nullable()->comment('请求参数');
            $table->integer('response_status')->nullable()->comment('响应状态码');
            $table->json('response_headers')->nullable()->comment('响应头');
            $table->json('response_body')->nullable()->comment('响应内容');
            $table->integer('duration_ms')->nullable()->comment('请求耗时(毫秒)');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->boolean('is_success')->default(false)->comment('是否成功');
            $table->bigInteger('business_id')->nullable()->comment('关联业务ID');
            $table->string('business_type', 50)->nullable()->comment('业务类型');
            $table->timestamp('request_time')->comment('请求时间');
            $table->timestamps();
            
            // 索引
            $table->index(['service_name', 'api_name']);
            $table->index('request_time');
            $table->index(['business_id', 'business_type']);
            $table->index('is_success');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('third_party_api_logs');
    }
} 