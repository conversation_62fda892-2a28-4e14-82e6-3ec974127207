-- 签约功能相关表结构和测试数据
-- 创建时间: 2024-03-16
-- 说明: 包含签约相关的业务申请测试数据

-- 1. 插入签约相关的测试业务申请数据
INSERT INTO `business_applications` (
  `application_no`, `user_id`, `channel_code`, `customer_name`, `customer_phone`, `customer_id_card`,
  `product_name`, `loan_amount`, `loan_period`, `interest_rate`, `vehicle_brand`, `vehicle_model`,
  `vehicle_vin`, `vehicle_year`, `vehicle_price`, `status`, `status_text`, `approval_notes`,
  `approval_history`, `submit_time`, `approval_time`, `need_supplement`, `customer_data`, 
  `risk_assessment`, `created_at`, `updated_at`
) VALUES 

-- 待签约状态
(
  'APP20240316100001', 1, 'DIRECT', '张三', '***********', '110101199001011001',
  '24年以租代售计划', 150000.00, 24, 0.0850, '大众', '帕萨特',
  'WVWZZZ3CZKE123456', 2023, 180000.00, 'contract_pending', '待签约', '终审通过，可以进行签约',
  JSON_ARRAY(
    JSON_OBJECT(
      'stage', 'submitted',
      'status', 'submitted', 
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '客户提交申请',
      'created_at', '2024-03-15 09:00:00'
    ),
    JSON_OBJECT(
      'stage', 'initial_review',
      'status', 'approved',
      'operator_id', 2, 
      'operator_name', '系统',
      'notes', '初审通过',
      'created_at', '2024-03-15 09:30:00'
    ),
    JSON_OBJECT(
      'stage', 'pre_approval',
      'status', 'approved',
      'operator_id', 3,
      'operator_name', '预审员', 
      'notes', '风险评估通过',
      'created_at', '2024-03-15 10:00:00'
    ),
    JSON_OBJECT(
      'stage', 'interview_completed',
      'status', 'completed',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '面审完成，客户资料齐全',
      'created_at', '2024-03-15 14:00:00'
    ),
    JSON_OBJECT(
      'stage', 'final_review',
      'status', 'approved',
      'operator_id', 4,
      'operator_name', '终审员',
      'notes', '终审通过',
      'created_at', '2024-03-15 16:00:00'
    ),
    JSON_OBJECT(
      'stage', 'secondary_review', 
      'status', 'approved',
      'operator_id', 5,
      'operator_name', '复审员',
      'notes', '复审通过，可以签约',
      'created_at', '2024-03-15 17:00:00'
    )
  ),
  '2024-03-15 09:00:00', '2024-03-15 17:00:00', 0,
  JSON_OBJECT('income', 8000, 'work_years', 5, 'education', '本科'),
  JSON_OBJECT('score', 85, 'level', 'low'),
  '2024-03-15 09:00:00', '2024-03-15 17:00:00'
),

-- 签约中状态
(
  'APP20240316100002', 1, 'DIRECT', '李四', '13800138002', '110101199002022002',
  '36年融资租赁计划', 200000.00, 36, 0.0900, '奔驰', 'C级',
  'WDD2050291F123456', 2024, 250000.00, 'contract_processing', '签约中', '已发起签约，等待客户确认',
  JSON_ARRAY(
    JSON_OBJECT(
      'stage', 'submitted',
      'status', 'submitted',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '客户提交申请',
      'created_at', '2024-03-14 10:00:00'
    ),
    JSON_OBJECT(
      'stage', 'initial_review',
      'status', 'approved',
      'operator_id', 2,
      'operator_name', '系统',
      'notes', '初审通过',
      'created_at', '2024-03-14 10:30:00'
    ),
    JSON_OBJECT(
      'stage', 'pre_approval',
      'status', 'approved',
      'operator_id', 3,
      'operator_name', '预审员',
      'notes', '风险评估通过',
      'created_at', '2024-03-14 11:00:00'
    ),
    JSON_OBJECT(
      'stage', 'interview_completed',
      'status', 'completed',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '面审完成',
      'created_at', '2024-03-14 15:00:00'
    ),
    JSON_OBJECT(
      'stage', 'final_review',
      'status', 'approved',
      'operator_id', 4,
      'operator_name', '终审员',
      'notes', '终审通过',
      'created_at', '2024-03-14 16:30:00'
    ),
    JSON_OBJECT(
      'stage', 'secondary_review',
      'status', 'approved',
      'operator_id', 5,
      'operator_name', '复审员',
      'notes', '复审通过',
      'created_at', '2024-03-14 17:30:00'
    ),
    JSON_OBJECT(
      'stage', 'contract_initiated',
      'status', 'processing',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '发起电子签约',
      'created_at', '2024-03-15 09:00:00'
    )
  ),
  '2024-03-14 10:00:00', '2024-03-14 17:30:00', 0,
  JSON_OBJECT('income', 12000, 'work_years', 8, 'education', '硕士'),
  JSON_OBJECT('score', 92, 'level', 'low'),
  '2024-03-14 10:00:00', '2024-03-15 09:00:00'
),

-- 签约完成状态
(
  'APP20240316100003', 1, 'DIRECT', '王五', '13800138003', '110101199003033003',
  '24年以租代售计划', 120000.00, 24, 0.0850, '丰田', '凯美瑞',
  '4T1BE46K37U123456', 2023, 150000.00, 'contract_completed', '签约完成', '签约完成，业务审批通过',
  JSON_ARRAY(
    JSON_OBJECT(
      'stage', 'submitted',
      'status', 'submitted',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '客户提交申请',
      'created_at', '2024-03-13 11:00:00'
    ),
    JSON_OBJECT(
      'stage', 'initial_review',
      'status', 'approved',
      'operator_id', 2,
      'operator_name', '系统',
      'notes', '初审通过',
      'created_at', '2024-03-13 11:30:00'
    ),
    JSON_OBJECT(
      'stage', 'pre_approval',
      'status', 'approved',
      'operator_id', 3,
      'operator_name', '预审员',
      'notes', '风险评估通过',
      'created_at', '2024-03-13 12:00:00'
    ),
    JSON_OBJECT(
      'stage', 'interview_completed',
      'status', 'completed',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '面审完成',
      'created_at', '2024-03-13 16:00:00'
    ),
    JSON_OBJECT(
      'stage', 'final_review',
      'status', 'approved',
      'operator_id', 4,
      'operator_name', '终审员',
      'notes', '终审通过',
      'created_at', '2024-03-13 17:00:00'
    ),
    JSON_OBJECT(
      'stage', 'secondary_review',
      'status', 'approved',
      'operator_id', 5,
      'operator_name', '复审员',
      'notes', '复审通过',
      'created_at', '2024-03-14 09:00:00'
    ),
    JSON_OBJECT(
      'stage', 'contract_initiated',
      'status', 'processing',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '发起电子签约',
      'created_at', '2024-03-14 10:00:00'
    ),
    JSON_OBJECT(
      'stage', 'contract_completed',
      'status', 'approved',
      'operator_id', 1,
      'operator_name', '张三业务员',
      'notes', '客户已完成签约',
      'created_at', '2024-03-14 16:00:00'
    )
  ),
  '2024-03-13 11:00:00', '2024-03-14 16:00:00', 0,
  JSON_OBJECT('income', 6500, 'work_years', 3, 'education', '大专'),
  JSON_OBJECT('score', 78, 'level', 'medium'),
  '2024-03-13 11:00:00', '2024-03-14 16:00:00'
),

-- 更多待签约测试数据
(
  'APP20240316100004', 1, 'DIRECT', '赵六', '13800138004', '110101199004044004',
  '48年长期贷款', 300000.00, 48, 0.0950, '宝马', '3系',
  'WBAUE71050AB12345', 2024, 350000.00, 'contract_pending', '待签约', '终审通过，可以签约',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', '2024-03-12 09:00:00'),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', '2024-03-12 09:30:00'),
    JSON_OBJECT('stage', 'pre_approval', 'status', 'approved', 'operator_id', 3, 'operator_name', '预审员', 'notes', '风险评估通过', 'created_at', '2024-03-12 10:00:00'),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', '2024-03-12 15:00:00'),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', '2024-03-12 17:00:00'),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 5, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', '2024-03-13 09:00:00')
  ),
  '2024-03-12 09:00:00', '2024-03-13 09:00:00', 0,
  JSON_OBJECT('income', 15000, 'work_years', 10, 'education', '硕士'),
  JSON_OBJECT('score', 95, 'level', 'low'),
  '2024-03-12 09:00:00', '2024-03-13 09:00:00'
),

(
  'APP20240316100005', 1, 'DIRECT', '孙七', '13800138005', '110101199005055005',
  '18年中期贷款', 180000.00, 18, 0.0800, '奥迪', 'A4L',
  'WAUZZZF4XGA123456', 2023, 220000.00, 'contract_processing', '签约中', '已发起电子签约，等待客户完成',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', '2024-03-11 14:00:00'),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', '2024-03-11 14:30:00'),
    JSON_OBJECT('stage', 'pre_approval', 'status', 'approved', 'operator_id', 3, 'operator_name', '预审员', 'notes', '风险评估通过', 'created_at', '2024-03-11 15:00:00'),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', '2024-03-12 10:00:00'),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', '2024-03-12 14:00:00'),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 5, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', '2024-03-12 16:00:00'),
    JSON_OBJECT('stage', 'contract_initiated', 'status', 'processing', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '发起电子签约', 'created_at', '2024-03-13 10:00:00')
  ),
  '2024-03-11 14:00:00', '2024-03-12 16:00:00', 0,
  JSON_OBJECT('income', 9000, 'work_years', 6, 'education', '本科'),
  JSON_OBJECT('score', 88, 'level', 'low'),
  '2024-03-11 14:00:00', '2024-03-13 10:00:00'
),

-- 已审批通过（签约完成后的状态）
(
  'APP20240316100006', 1, 'DIRECT', '周八', '13800138006', '110101199006066006',
  '30年超长期贷款', 250000.00, 30, 0.0880, '凯迪拉克', 'XT5',
  'LSGUE23526S123456', 2024, 300000.00, 'approved', '审批通过', '签约完成，业务审批通过，等待放款',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', '2024-03-10 09:00:00'),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', '2024-03-10 09:30:00'),
    JSON_OBJECT('stage', 'pre_approval', 'status', 'approved', 'operator_id', 3, 'operator_name', '预审员', 'notes', '风险评估通过', 'created_at', '2024-03-10 10:00:00'),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', '2024-03-10 15:00:00'),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', '2024-03-10 17:00:00'),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 5, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', '2024-03-11 09:00:00'),
    JSON_OBJECT('stage', 'contract_initiated', 'status', 'processing', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '发起电子签约', 'created_at', '2024-03-11 10:00:00'),
    JSON_OBJECT('stage', 'contract_completed', 'status', 'approved', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户完成签约', 'created_at', '2024-03-11 18:00:00')
  ),
  '2024-03-10 09:00:00', '2024-03-11 18:00:00', 0,
  JSON_OBJECT('income', 11000, 'work_years', 7, 'education', '本科'),
  JSON_OBJECT('score', 90, 'level', 'low'),
  '2024-03-10 09:00:00', '2024-03-11 18:00:00'
);

-- 2. 创建用户测试数据（如果不存在）
INSERT IGNORE INTO `users` (
  `id`, `phone`, `nickname`, `name`, `channel_code`, `status`, 
  `register_time`, `login_time`, `created_at`, `updated_at`
) VALUES 
(1, '13900139001', '张三业务员', '张三', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW()),
(2, '13900139002', '李四业务员', '李四', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW());

-- 3. 创建渠道测试数据（如果不存在）
INSERT IGNORE INTO `channels` (
  `code`, `name`, `status`, `sort`, `created_at`, `updated_at`
) VALUES 
('DIRECT', '直营渠道', 1, 1, NOW(), NOW()),
('PARTNER', '合作伙伴', 1, 2, NOW(), NOW());

-- 4. 创建e签宝签约记录表（可选，用于记录签约流程信息）
CREATE TABLE IF NOT EXISTS `esign_contracts` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务申请ID',
  `flow_id` varchar(50) NOT NULL COMMENT 'e签宝流程ID',
  `account_id` varchar(50) DEFAULT NULL COMMENT 'e签宝账户ID',
  `contract_name` varchar(200) DEFAULT NULL COMMENT '合同名称',
  `contract_status` varchar(20) DEFAULT 'processing' COMMENT '合同状态:processing处理中,completed已完成,failed失败',
  `sign_url` text COMMENT '签署链接',
  `contract_file_url` text COMMENT '已签署合同文件URL',
  `initiated_time` timestamp NULL DEFAULT NULL COMMENT '发起时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `callback_data` json DEFAULT NULL COMMENT '回调数据',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `esign_contracts_flow_id_unique` (`flow_id`),
  KEY `esign_contracts_application_id_index` (`application_id`),
  KEY `esign_contracts_contract_status_index` (`contract_status`),
  CONSTRAINT `esign_contracts_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='e签宝签约记录表';

-- 5. 插入e签宝签约记录测试数据
INSERT INTO `esign_contracts` (
  `application_id`, `flow_id`, `account_id`, `contract_name`, `contract_status`, 
  `sign_url`, `initiated_time`, `completed_time`, `created_at`, `updated_at`
) VALUES 
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316100002'),
  CONCAT('esign_flow_', UNIX_TIMESTAMP(), '_001'),
  CONCAT('esign_account_', UNIX_TIMESTAMP(), '_001'),
  '车贷合同_APP20240316100002.pdf',
  'processing',
  CONCAT('https://esign.cn/sign/flow_', UNIX_TIMESTAMP(), '_001'),
  '2024-03-15 09:00:00',
  NULL,
  '2024-03-15 09:00:00',
  '2024-03-15 09:00:00'
),
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316100003'),
  CONCAT('esign_flow_', UNIX_TIMESTAMP(), '_002'), 
  CONCAT('esign_account_', UNIX_TIMESTAMP(), '_002'),
  '车贷合同_APP20240316100003.pdf',
  'completed',
  CONCAT('https://esign.cn/sign/flow_', UNIX_TIMESTAMP(), '_002'),
  '2024-03-14 10:00:00',
  '2024-03-14 16:00:00',
  '2024-03-14 10:00:00',
  '2024-03-14 16:00:00'
),
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316100005'),
  CONCAT('esign_flow_', UNIX_TIMESTAMP(), '_003'),
  CONCAT('esign_account_', UNIX_TIMESTAMP(), '_003'), 
  '车贷合同_APP20240316100005.pdf',
  'processing',
  CONCAT('https://esign.cn/sign/flow_', UNIX_TIMESTAMP(), '_003'),
  '2024-03-13 10:00:00',
  NULL,
  '2024-03-13 10:00:00',
  '2024-03-13 10:00:00'
),
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316100006'),
  CONCAT('esign_flow_', UNIX_TIMESTAMP(), '_004'),
  CONCAT('esign_account_', UNIX_TIMESTAMP(), '_004'),
  '车贷合同_APP20240316100006.pdf', 
  'completed',
  CONCAT('https://esign.cn/sign/flow_', UNIX_TIMESTAMP(), '_004'),
  '2024-03-11 10:00:00',
  '2024-03-11 18:00:00',
  '2024-03-11 10:00:00',
  '2024-03-11 18:00:00'
);

-- 6. 查询验证数据
SELECT 
  ba.application_no,
  ba.customer_name,
  ba.product_name,
  ba.loan_amount,
  ba.status,
  ba.status_text,
  ba.submit_time,
  ba.approval_time,
  ec.flow_id,
  ec.contract_status,
  ec.initiated_time,
  ec.completed_time
FROM business_applications ba
LEFT JOIN esign_contracts ec ON ba.id = ec.application_id
WHERE ba.application_no LIKE 'APP202403161%'
ORDER BY ba.submit_time DESC;

-- 7. 统计各状态数量
SELECT 
  status,
  status_text,
  COUNT(*) as count
FROM business_applications 
WHERE application_no LIKE 'APP202403161%'
GROUP BY status, status_text
ORDER BY count DESC;

-- 8. 查看完整的审批历史
SELECT 
  application_no,
  customer_name,
  JSON_EXTRACT(approval_history, '$[*].stage') as stages,
  JSON_EXTRACT(approval_history, '$[*].created_at') as stage_times
FROM business_applications 
WHERE application_no LIKE 'APP202403161%'
ORDER BY created_at DESC; 