<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Http\Controllers\Controller;
use App\Models\SalesLead;
use App\Models\LeadSource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SalesLeadController extends BaseController
{
    /**
     * 线索列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $page = intval($request->get('page', 1));
            $pageSize = intval($request->get('page_size', 10));
            $status = $request->get('status', '');
            $keyword = trim($request->get('keyword', ''));
            $source_id = $request->get('source_id', '');
            $intent_level = $request->get('intent_level', '');
            $startDate = $request->get('start_date', '');
            $endDate = $request->get('end_date', '');

            $query = SalesLead::with(['source', 'user'])
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // 状态筛选
            if (!empty($status)) {
                $query->where('status', $status);
            }

            // 关键词搜索
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('customer_name', 'like', "%{$keyword}%")
                      ->orWhere('customer_phone', 'like', "%{$keyword}%")
                      ->orWhere('lead_no', 'like', "%{$keyword}%");
                });
            }

            // 线索来源筛选
            if (!empty($source_id)) {
                $query->where('source_id', $source_id);
            }

            // 意向等级筛选
            if (!empty($intent_level)) {
                $query->where('intent_level', $intent_level);
            }

            // 时间范围筛选
            if (!empty($startDate)) {
                $query->whereDate('created_at', '>=', $startDate);
            }
            if (!empty($endDate)) {
                $query->whereDate('created_at', '<=', $endDate);
            }

            // 分页查询
            $leads = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $list = $leads->getCollection()->map(function ($lead) {
                return $this->formatLeadData($lead);
            });

            // 获取各状态统计
            $statistics = $this->getStatusStatistics($user->id);

            return $this->successHandler('获取线索列表成功', 200, [
                'list' => $list,
                'pagination' => [
                    'current_page' => $leads->currentPage(),
                    'last_page' => $leads->lastPage(),
                    'per_page' => $leads->perPage(),
                    'total' => $leads->total(),
                    'has_more' => $leads->hasMorePages()
                ],
                'statistics' => $statistics
            ]);

        } catch (\Exception $e) {
            \Log::error('获取线索列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取线索列表失败', 500);
        }
    }

    /**
     * 新增线索
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $user = Auth::user();

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_name' => 'required|string|max:20',
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'source_id' => 'required|integer|exists:lead_sources,id',
                'intent_level' => 'required|integer|in:1,2,3,4,5',
                'intent_brand' => 'nullable|string|max:50',
                'intent_model' => 'nullable|string|max:50',
                'intent_price_min' => 'nullable|numeric|min:0',
                'intent_price_max' => 'nullable|numeric|min:0',
                'intent_loan_amount' => 'nullable|numeric|min:0',
                'intent_loan_period' => 'nullable|integer|min:1|max:60',
                'purchase_timeline' => 'nullable|string|in:一周内,一个月内,三个月内,半年内,一年内,暂无计划',
                'customer_age' => 'nullable|integer|min:18|max:80',
                'customer_gender' => 'nullable|integer|in:0,1,2',
                'customer_city' => 'nullable|string|max:50',
                'customer_occupation' => 'nullable|string|max:50',
                'customer_income' => 'nullable|numeric|min:0',
                'source_detail' => 'nullable|string|max:200',
                'remarks' => 'nullable|string|max:500'
            ], [
                'customer_name.required' => '客户姓名不能为空',
                'customer_name.max' => '客户姓名不能超过20个字符',
                'customer_phone.required' => '手机号不能为空',
                'customer_phone.regex' => '请输入正确的手机号码',
                'source_id.required' => '请选择线索来源',
                'source_id.exists' => '选择的线索来源不存在',
                'intent_level.required' => '请选择意向等级',
                'intent_level.in' => '意向等级参数错误',
                'intent_price_max.min' => '最高价格不能小于0',
                'intent_loan_amount.min' => '贷款金额不能小于0',
                'intent_loan_period.min' => '贷款期限不能小于1个月',
                'intent_loan_period.max' => '贷款期限不能超过60个月',
                'customer_age.min' => '客户年龄不能小于18岁',
                'customer_age.max' => '客户年龄不能超过80岁',
                'customer_gender.in' => '性别参数错误',
                'customer_income.min' => '月收入不能小于0',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 检查是否已存在相同手机号的线索（同一天内）
            $existingLead = SalesLead::where('customer_phone', $request->customer_phone)
                ->whereDate('created_at', Carbon::today())
                ->first();

            if ($existingLead) {
                return $this->errorHandler('今日已存在该手机号的线索记录', 400);
            }

            // 创建线索数据
            $leadData = [
                'user_id' => $user->id,
                'channel_code' => $user->channel_code ?? 'DEFAULT',
                'source_id' => $request->source_id,
                'source_detail' => $request->source_detail,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'customer_age' => $request->customer_age,
                'customer_gender' => $request->customer_gender,
                'customer_city' => $request->customer_city,
                'customer_occupation' => $request->customer_occupation,
                'customer_income' => $request->customer_income,
                'intent_level' => $request->intent_level,
                'intent_brand' => $request->intent_brand,
                'intent_model' => $request->intent_model,
                'intent_price_min' => $request->intent_price_min,
                'intent_price_max' => $request->intent_price_max,
                'intent_loan_amount' => $request->intent_loan_amount,
                'intent_loan_period' => $request->intent_loan_period,
                'purchase_timeline' => $request->purchase_timeline,
                'status' => 'new',
                'status_text' => '新增线索',
                'priority' => $this->calculatePriority($request->intent_level, $request->purchase_timeline),
                'next_follow_time' => Carbon::now()->addDay(),
                'remarks' => $request->remarks
            ];

            $lead = SalesLead::create($leadData);

            DB::commit();

            return $this->successHandler('线索创建成功', 200, [
                'lead_id' => $lead->id,
                'lead_no' => $lead->lead_no
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('创建线索失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('创建线索失败', 500);
        }
    }

    /**
     * 线索详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            
            $lead = SalesLead::with(['source', 'user', 'followUps' => function($query) {
                $query->with('user')->orderBy('created_at', 'desc');
            }])
            ->where('id', $id)
            ->where('user_id', $user->id)
            ->first();

            if (!$lead) {
                return $this->errorHandler('线索不存在', 404);
            }

            $data = $this->formatLeadData($lead, true);

            return $this->successHandler('获取线索详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取线索详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取线索详情失败', 500);
        }
    }

    /**
     * 获取线索来源列表
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function sources()
    {
        try {
            $sources = LeadSource::where('status', 1)
                ->orderBy('sort_order', 'asc')
                ->get(['id', 'name', 'code', 'description']);

            return $this->successHandler('获取线索来源成功', 200, $sources);

        } catch (\Exception $e) {
            \Log::error('获取线索来源失败: ' . $e->getMessage());
            return $this->errorHandler('获取线索来源失败', 500);
        }
    }

    /**
     * 格式化线索数据
     * 
     * @param SalesLead $lead
     * @param bool $isDetail
     * @return array
     */
    private function formatLeadData($lead, $isDetail = false)
    {
        $data = [
            'id' => $lead->id,
            'lead_no' => $lead->lead_no,
            'customer_name' => $lead->customer_name,
            'customer_phone' => $lead->customer_phone,
            'intent_level' => $lead->intent_level,
            'intent_level_text' => $lead->getIntentLevelText(),
            'intent_brand' => $lead->intent_brand,
            'intent_model' => $lead->intent_model,
            'purchase_timeline' => $lead->purchase_timeline,
            'status' => $lead->status,
            'status_text' => $lead->getStatusText(),
            'priority' => $lead->priority,
            'priority_text' => $lead->getPriorityText(),
            'source_name' => $lead->source ? $lead->source->name : '',
            'created_at' => $lead->created_at->format('Y-m-d H:i'),
            'next_follow_time' => $lead->next_follow_time ? $lead->next_follow_time->format('Y-m-d H:i') : null,
        ];

        if ($isDetail) {
            $data = array_merge($data, [
                'customer_age' => $lead->customer_age,
                'customer_gender' => $lead->customer_gender,
                'customer_gender_text' => $lead->getGenderText(),
                'customer_city' => $lead->customer_city,
                'customer_occupation' => $lead->customer_occupation,
                'customer_income' => $lead->customer_income,
                'intent_price_min' => $lead->intent_price_min,
                'intent_price_max' => $lead->intent_price_max,
                'intent_loan_amount' => $lead->intent_loan_amount,
                'intent_loan_period' => $lead->intent_loan_period,
                'source_detail' => $lead->source_detail,
                'remarks' => $lead->remarks,
                'follow_ups' => $lead->followUps ? $lead->followUps->map(function($followUp) {
                    return [
                        'id' => $followUp->id,
                        'follow_type' => $followUp->follow_type,
                        'follow_content' => $followUp->follow_content,
                        'follow_result' => $followUp->follow_result,
                        'next_follow_time' => $followUp->next_follow_time ? $followUp->next_follow_time->format('Y-m-d H:i') : null,
                        'user_name' => $followUp->user ? $followUp->user->name : '',
                        'created_at' => $followUp->created_at->format('Y-m-d H:i')
                    ];
                })->toArray() : []
            ]);
        }

        return $data;
    }

    /**
     * 获取状态统计
     * 
     * @param int $userId
     * @return array
     */
    private function getStatusStatistics($userId)
    {
        $query = SalesLead::where('user_id', $userId);
        
        return [
            'total' => $query->count(),
            'new' => $query->where('status', 'new')->count(),
            'following' => $query->where('status', 'following')->count(),
            'interested' => $query->where('status', 'interested')->count(),
            'visiting' => $query->where('status', 'visiting')->count(),
            'converted' => $query->where('status', 'converted')->count(),
            'lost' => $query->where('status', 'lost')->count(),
            'invalid' => $query->where('status', 'invalid')->count(),
        ];
    }

    /**
     * 计算优先级
     * 
     * @param int $intentLevel
     * @param string $purchaseTimeline
     * @return string
     */
    private function calculatePriority($intentLevel, $purchaseTimeline)
    {
        // 根据意向等级和购车时间计算优先级
        $timelineScore = 0;
        switch($purchaseTimeline) {
            case '一周内':
                $timelineScore = 5;
                break;
            case '一个月内':
                $timelineScore = 4;
                break;
            case '三个月内':
                $timelineScore = 3;
                break;
            case '半年内':
                $timelineScore = 2;
                break;
            case '一年内':
                $timelineScore = 1;
                break;
            default:
                $timelineScore = 0;
        }

        $totalScore = $intentLevel + $timelineScore;
        
        if ($totalScore >= 8) {
            return 'high';
        } elseif ($totalScore >= 5) {
            return 'medium';
        } else {
            return 'low';
        }
    }
} 