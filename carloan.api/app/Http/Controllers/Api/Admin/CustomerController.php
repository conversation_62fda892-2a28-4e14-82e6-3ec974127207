<?php

namespace App\Http\Controllers\Api\Admin;

use App\Models\Customer;
use App\Transformers\Admin\CustomerTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CustomerController extends BaseController
{
    /**
     * 客户列表
     */
    public function index(Request $request)
    {
        $query = Customer::query();

        // 搜索条件
        if ($request->filled('phone')) {
            $query->where('phone', 'like', '%' . $request->phone . '%');
        }

        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if ($request->filled('id_card')) {
            $query->where('id_card', 'like', '%' . $request->id_card . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 创建时间范围
        if ($request->filled('created_at_start')) {
            $query->whereDate('created_at', '>=', $request->created_at_start);
        }

        if ($request->filled('created_at_end')) {
            $query->whereDate('created_at', '<=', $request->created_at_end);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $perPage = $request->get('per_page', 20);
        $customers = $query->paginate($perPage);

        return $this->response->paginator($customers, new CustomerTransformer());
    }

    /**
     * 客户详情
     */
    public function show($id)
    {
        $customer = Customer::with([
            'contacts',
            'assets',
            'liabilities',
            'vehicles',
            'businessApplications',
            'creator'
        ])->find($id);

        if (!$customer) {
            return $this->response->errorNotFound('客户不存在');
        }

        return $this->response->item($customer, new CustomerTransformer());
    }

    /**
     * 新增客户
     */
    public function store(Request $request)
    {
        $phone = $request->post('phone', '');
        $name = $request->post('name', '');
        $id_card = $request->post('id_card', '');
        $gender = $request->post('gender', '');
        $nation = $request->post('nation', '');
        $address = $request->post('address', '');
        $authority = $request->post('authority', '');
        $valid_start = $request->post('valid_start', '');
        $valid_end = $request->post('valid_end', '');
        $status = $request->post('status', Customer::STATUS_ACTIVE);

        // 验证必填字段
        if (!$phone) {
            return $this->errorHandler('请输入手机号', 400);
        }

        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return $this->errorHandler('手机号格式不正确', 400);
        }

        if (!$name) {
            return $this->errorHandler('请输入客户姓名', 400);
        }

        if (!$id_card) {
            return $this->errorHandler('请输入身份证号', 400);
        }

        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $id_card)) {
            return $this->errorHandler('身份证号格式不正确', 400);
        }

        // 检查唯一性
        $existsPhone = Customer::where('phone', $phone)->first();
        if ($existsPhone) {
            return $this->errorHandler('手机号已存在', 400);
        }

        $existsIdCard = Customer::where('id_card', $id_card)->first();
        if ($existsIdCard) {
            return $this->errorHandler('身份证号已存在', 400);
        }

        try {
            $customer = Customer::create([
                'phone' => $phone,
                'name' => $name,
                'id_card' => $id_card,
                'gender' => $gender ?: Customer::GENDER_MALE,
                'nation' => $nation,
                'address' => $address,
                'authority' => $authority,
                'valid_start' => $valid_start ?: null,
                'valid_end' => $valid_end ?: null,
                'status' => $status,
                'created_by' => Auth::id(),
            ]);

            // 从身份证号解析性别
            if (!$gender) {
                $customer->parseGenderFromIdCard();
                $customer->save();
            }

            return $this->successHandler('客户创建成功', 200);
        } catch (\Exception $e) {
            return $this->errorHandler('客户创建失败', 500);
        }
    }

    /**
     * 更新客户
     */
    public function update($id, Request $request)
    {
        $customer = Customer::find($id);

        if (!$customer) {
            return $this->errorHandler('客户不存在', 404);
        }

        $phone = $request->input('phone', '');
        $name = $request->input('name', '');
        $id_card = $request->input('id_card', '');
        $gender = $request->input('gender', '');
        $nation = $request->input('nation', '');
        $address = $request->input('address', '');
        $authority = $request->input('authority', '');
        $valid_start = $request->input('valid_start', '');
        $valid_end = $request->input('valid_end', '');
        $status = $request->input('status', Customer::STATUS_ACTIVE);

        // 验证必填字段
        if (!$phone) {
            return $this->errorHandler('请输入手机号', 400);
        }

        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return $this->errorHandler('手机号格式不正确', 400);
        }

        if (!$name) {
            return $this->errorHandler('请输入客户姓名', 400);
        }

        if (!$id_card) {
            return $this->errorHandler('请输入身份证号', 400);
        }

        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $id_card)) {
            return $this->errorHandler('身份证号格式不正确', 400);
        }

        // 检查唯一性（排除当前记录）
        $existsPhone = Customer::where('id', '!=', $id)->where('phone', $phone)->first();
        if ($existsPhone) {
            return $this->errorHandler('手机号已存在', 400);
        }

        $existsIdCard = Customer::where('id', '!=', $id)->where('id_card', $id_card)->first();
        if ($existsIdCard) {
            return $this->errorHandler('身份证号已存在', 400);
        }

        try {
            $oldIdCard = $customer->id_card;
            
            $customer->update([
                'phone' => $phone,
                'name' => $name,
                'id_card' => $id_card,
                'gender' => $gender ?: $customer->gender,
                'nation' => $nation,
                'address' => $address,
                'authority' => $authority,
                'valid_start' => $valid_start ?: null,
                'valid_end' => $valid_end ?: null,
                'status' => $status,
            ]);

            // 如果身份证号发生变化，重新解析性别
            if ($oldIdCard !== $id_card && !$gender) {
                $customer->parseGenderFromIdCard();
                $customer->save();
            }

            return $this->successHandler('客户更新成功', 200);
        } catch (\Exception $e) {
            return $this->errorHandler('客户更新失败', 500);
        }
    }

    /**
     * 删除客户
     */
    public function destroy($id)
    {
        $customer = Customer::find($id);

        if (!$customer) {
            return $this->errorHandler('客户不存在', 404);
        }

        try {
            // 检查是否有关联的业务申请
            if ($customer->businessApplications()->exists()) {
                return $this->errorHandler('该客户存在关联的业务申请，无法删除', 400);
            }

            $customer->delete();

            return $this->successHandler('客户删除成功', 200);
        } catch (\Exception $e) {
            return $this->errorHandler('客户删除失败', 500);
        }
    }

    /**
     * 批量删除客户
     */
    public function batchDestroy(Request $request)
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return $this->errorHandler('请选择要删除的客户', 400);
        }

        try {
            // 检查是否有客户存在关联的业务申请
            $customersWithApplications = Customer::whereIn('id', $ids)
                ->whereHas('businessApplications')
                ->pluck('name')
                ->toArray();

            if (!empty($customersWithApplications)) {
                return $this->errorHandler('以下客户存在关联的业务申请，无法删除：' . implode('、', $customersWithApplications), 400);
            }

            Customer::whereIn('id', $ids)->delete();

            return $this->successHandler('批量删除成功', 200);
        } catch (\Exception $e) {
            return $this->errorHandler('批量删除失败', 500);
        }
    }

    /**
     * 导出客户列表
     */
    public function export(Request $request)
    {
        try {
            $query = Customer::query();

            // 应用搜索条件
            if ($request->filled('phone')) {
                $query->where('phone', 'like', '%' . $request->phone . '%');
            }

            if ($request->filled('name')) {
                $query->where('name', 'like', '%' . $request->name . '%');
            }

            if ($request->filled('id_card')) {
                $query->where('id_card', 'like', '%' . $request->id_card . '%');
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            $customers = $query->orderBy('created_at', 'desc')->get();

            // 这里可以使用Excel导出库，如 PhpSpreadsheet
            // 暂时返回JSON格式的客户列表
            return $this->response->collection($customers, new CustomerTransformer());
        } catch (\Exception $e) {
            return $this->errorHandler('导出失败', 500);
        }
    }

    /**
     * 导入客户
     */
    public function import(Request $request)
    {
        if (!$request->hasFile('file')) {
            return $this->errorHandler('请选择要导入的文件', 400);
        }

        $file = $request->file('file');
        if (!$file->isValid()) {
            return $this->errorHandler('文件上传失败', 400);
        }

        try {
            // 这里可以使用Excel导入库处理文件
            // 暂时返回成功信息
            return $this->successHandler('导入成功', 200);
        } catch (\Exception $e) {
            return $this->errorHandler('导入失败', 500);
        }
    }
} 