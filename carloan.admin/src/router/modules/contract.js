import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const contractRouter = {
  path: '/contract',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Contract',
  meta: {
    title: '合同管理',
    icon: 'el-icon-document'
  },
  children: [
    {
      path: 'templates',
      component: PlaceholderPage,
      name: 'ContractTemplates',
      meta: { title: '合同模板' }
    },
    {
      path: 'signing',
      component: PlaceholderPage,
      name: 'ContractSigning',
      meta: { title: '合同签署' }
    },
    {
      path: 'archive',
      component: PlaceholderPage,
      name: 'ContractArchive',
      meta: { title: '合同档案' }
    },
    {
      path: 'esign',
      component: PlaceholderPage,
      name: 'ContractEsign',
      meta: { title: '电子签章' }
    },
    {
      path: 'templates/create',
      component: PlaceholderPage,
      name: 'ContractTemplateCreate',
      meta: { title: '新增模板', hidden: true }
    },
    {
      path: 'templates/edit/:id',
      component: PlaceholderPage,
      name: 'ContractTemplateEdit',
      meta: { title: '编辑模板', hidden: true }
    },
    {
      path: 'detail/:id',
      component: PlaceholderPage,
      name: 'ContractDetail',
      meta: { title: '合同详情', hidden: true }
    }
  ]
}

export default contractRouter
