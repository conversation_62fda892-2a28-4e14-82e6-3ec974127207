<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LeadSource extends Model
{
    protected $table = 'lead_sources';

    protected $fillable = [
        'name',
        'code',
        'description',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'status' => 'integer',
        'sort_order' => 'integer'
    ];

    /**
     * 获取启用状态的来源
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc');
    }
} 