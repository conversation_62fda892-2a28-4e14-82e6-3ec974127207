<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerLiability extends Model
{
    protected $table = 'customer_liabilities';

    protected $fillable = [
        'customer_id',
        'business_application_id',
        'has_credit_card',
        'credit_card_limit',
        'credit_card_used',
        'has_bank_loan',
        'bank_loan_balance',
        'bank_loan_monthly',
        'has_other_debt',
        'other_debt_balance',
        'other_debt_monthly',
        'total_debt',
        'total_monthly_payment',
        'debt_to_asset_ratio'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'business_application_id' => 'integer',
        'has_credit_card' => 'boolean',
        'credit_card_limit' => 'decimal:2',
        'credit_card_used' => 'decimal:2',
        'has_bank_loan' => 'boolean',
        'bank_loan_balance' => 'decimal:2',
        'bank_loan_monthly' => 'decimal:2',
        'has_other_debt' => 'boolean',
        'other_debt_balance' => 'decimal:2',
        'other_debt_monthly' => 'decimal:2',
        'total_debt' => 'decimal:2',
        'total_monthly_payment' => 'decimal:2',
        'debt_to_asset_ratio' => 'decimal:4'
    ];

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'business_application_id');
    }

    /**
     * 计算总负债
     */
    public function calculateTotalDebt()
    {
        $total = 0;
        
        if ($this->credit_card_used) {
            $total += $this->credit_card_used;
        }
        
        if ($this->bank_loan_balance) {
            $total += $this->bank_loan_balance;
        }
        
        if ($this->other_debt_balance) {
            $total += $this->other_debt_balance;
        }
        
        $this->total_debt = $total;
        return $total;
    }

    /**
     * 计算总月供
     */
    public function calculateTotalMonthlyPayment()
    {
        $total = 0;
        
        if ($this->bank_loan_monthly) {
            $total += $this->bank_loan_monthly;
        }
        
        if ($this->other_debt_monthly) {
            $total += $this->other_debt_monthly;
        }
        
        $this->total_monthly_payment = $total;
        return $total;
    }

    /**
     * 计算负债率
     */
    public function calculateDebtToAssetRatio($totalAssets)
    {
        if ($totalAssets <= 0) {
            return 0;
        }
        
        $ratio = $this->total_debt / $totalAssets;
        $this->debt_to_asset_ratio = $ratio;
        return $ratio;
    }

    /**
     * 获取信用卡使用率
     */
    public function getCreditCardUtilizationAttribute()
    {
        if (!$this->credit_card_limit || $this->credit_card_limit <= 0) {
            return 0;
        }
        
        return $this->credit_card_used / $this->credit_card_limit;
    }

    /**
     * 获取信用卡可用额度
     */
    public function getCreditCardAvailableAttribute()
    {
        return $this->credit_card_limit - ($this->credit_card_used ?? 0);
    }

    /**
     * 作用域：按客户ID筛选
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * 作用域：按业务申请ID筛选
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('business_application_id', $applicationId);
    }

    /**
     * 作用域：有信用卡的客户
     */
    public function scopeWithCreditCard($query)
    {
        return $query->where('has_credit_card', true);
    }

    /**
     * 作用域：有银行贷款的客户
     */
    public function scopeWithBankLoan($query)
    {
        return $query->where('has_bank_loan', true);
    }

    /**
     * 作用域：有其他负债的客户
     */
    public function scopeWithOtherDebt($query)
    {
        return $query->where('has_other_debt', true);
    }
} 