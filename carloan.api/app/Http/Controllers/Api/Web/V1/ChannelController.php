<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Http\Controllers\Api\Web\V1\BaseController;
use App\Models\Channel;
use App\Transformers\Web\ChannelTransformer;
use Illuminate\Http\Request;

class ChannelController extends BaseController
{
    /**
     * 渠道列表（移动端）
     * @param Request $request
     * @return \Dingo\Api\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $name = $request->get('name', '');
            $status = $request->get('status', ''); // 改为空字符串，不默认过滤
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 20);

            $query = Channel::orderBy('sort', 'asc')
                ->orderBy('created_at', 'desc');

            if ($name !== '') {
                $query = $query->where('name', 'like', "%{$name}%");
            }

            if ($status !== '') {
                $query = $query->where('status', $status);
            }

            $channels = $query->paginate($pageSize, ['*'], 'page', $page);
            
            // 格式化数据
            $transformer = new ChannelTransformer();
            $list = $channels->getCollection()->map(function ($channel) use ($transformer) {
                return $transformer->transform($channel);
            });

            return $this->successHandler('获取渠道列表成功', 200, [
                'list' => $list,
                'pagination' => [
                    'current_page' => $channels->currentPage(),
                    'last_page' => $channels->lastPage(),
                    'per_page' => $channels->perPage(),
                    'total' => $channels->total(),
                    'has_more' => $channels->hasMorePages()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('获取渠道列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取渠道列表失败', 500);
        }
    }

    /**
     * 新增渠道（移动端）
     * @param Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        $code = $request->post('code', '');
        $name = $request->post('name', '');
        $status = $request->post('status', 1);
        $sort = $request->post('sort', 0);

        if (!$code) {
            return $this->errorHandler('请输入渠道码', 400);
        }

        if (!$name) {
            return $this->errorHandler('请输入渠道名称', 400);
        }

        // 验证渠道码格式（只允许数字和字母）
        if (!preg_match('/^[A-Za-z0-9]+$/', $code)) {
            return $this->errorHandler('渠道码只能包含数字和字母', 400);
        }

        $exists = Channel::where('code', $code)->first();
        if ($exists) {
            return $this->errorHandler('渠道码已存在', 400);
        }

        $result = Channel::create([
            'code' => $code,
            'name' => $name,
            'status' => $status,
            'sort' => $sort
        ]);

        if ($result === false) {
            return $this->errorHandler('新增失败', 500);
        }

        return $this->successHandler('新增成功', 200);
    }

    /**
     * 渠道详情（移动端）
     * @param $id
     * @return \Dingo\Api\Http\Response
     */
    public function show($id)
    {
        try {
            $channel = Channel::find($id);
            if (!$channel) {
                return $this->errorHandler('渠道不存在', 404);
            }

            $transformer = new ChannelTransformer();
            $data = $transformer->transform($channel);

            return $this->successHandler('获取渠道详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取渠道详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取渠道详情失败', 500);
        }
    }

    /**
     * 编辑渠道（移动端）
     * @param $id
     * @param Request $request
     * @return mixed
     */
    public function update($id, Request $request)
    {
        try {
            $code = $request->input('code', '');
            $name = $request->input('name', '');
            $status = $request->input('status', 1);
            $sort = $request->input('sort', 0);

            if (!$code) {
                return $this->errorHandler('请输入渠道码', 400);
            }

            if (!$name) {
                return $this->errorHandler('请输入渠道名称', 400);
            }

            // 验证渠道码格式（只允许数字和字母）
            if (!preg_match('/^[A-Za-z0-9]+$/', $code)) {
                return $this->errorHandler('渠道码只能包含数字和字母', 400);
            }

            $channel = Channel::find($id);
            if (!$channel) {
                return $this->errorHandler('渠道不存在', 404);
            }

            // 检查渠道码是否已被其他渠道使用
            $exists = Channel::where('id', '!=', $id)
                ->where('code', $code)
                ->first();
            if ($exists) {
                return $this->errorHandler('渠道码已存在', 400);
            }

            $result = $channel->update([
                'code' => $code,
                'name' => $name,
                'status' => $status,
                'sort' => $sort
            ]);

            if ($result === false) {
                return $this->errorHandler('编辑失败', 500);
            }

            return $this->successHandler('编辑成功', 200);

        } catch (\Exception $e) {
            \Log::error('编辑渠道失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('编辑失败', 500);
        }
    }

    /**
     * 删除渠道（移动端）
     * @param $id
     * @return mixed
     */
    public function destroy($id)
    {
        try {
            $channel = Channel::find($id);
            if (!$channel) {
                return $this->errorHandler('渠道不存在', 404);
            }

            // 检查是否有用户正在使用这个渠道
            $userCount = \App\Models\User::where('channel_code', $channel->code)
                ->where('status', '!=', 2)
                ->count();
            
            if ($userCount > 0) {
                return $this->errorHandler('该渠道下还有用户，无法删除', 400);
            }

            // 软删除
            $result = $channel->delete();

            if ($result === false) {
                return $this->errorHandler('删除失败', 500);
            }

            return $this->successHandler('删除成功', 200);

        } catch (\Exception $e) {
            \Log::error('删除渠道失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('删除失败', 500);
        }
    }
} 