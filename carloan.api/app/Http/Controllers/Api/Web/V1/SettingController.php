<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\Setting;
use App\Transformers\Web\SettingTransformer;
use Illuminate\Http\Request;

class SettingController extends BaseController
{
    /**
     * 获取指定配置
     * @param string $name
     * @return \Dingo\Api\Http\Response
     */
    public function show($name)
    {
        $setting = Setting::where('name', $name)->first();
        
        if (!$setting) {
            return $this->response->array([
                'data' => [
                    'name' => $name,
                    'value' => '',
                    'desc' => ''
                ]
            ]);
        }

        return $this->response->item($setting, new SettingTransformer());
    }

    /**
     * 批量获取配置（支持通过names参数或params.names指定多个配置）
     * @param Request $request
     * @return \Dingo\Api\Http\Response
     */
    public function index(Request $request)
    {
        $names = '';
        
        // 优先从params参数中获取JSON格式的数据
        $params = $request->get('params', '');
        if (!empty($params)) {
            try {
                $paramsData = json_decode($params, true);
                if (isset($paramsData['names'])) {
                    $names = $paramsData['names'];
                }
            } catch (\Exception $e) {
                // JSON解析失败，尝试其他方式
            }
        }
        
        // 如果params中没有获取到，尝试直接从names参数获取
        if (empty($names)) {
            $names = $request->get('names', '');
        }
        
        if (empty($names)) {
            return $this->response->array(['data' => []]);
        }
        
        $nameArray = explode(',', $names);
        $settings = Setting::whereIn('name', $nameArray)->get();
        
        // 构建k-v格式的结果
        $result = [];
        foreach ($nameArray as $name) {
            $setting = $settings->firstWhere('name', $name);
            if ($setting) {
                $result[$name] = $setting->value;
            } else {
                // 为不存在的配置返回默认值
                $result[$name] = '';
            }
        }
        
        return $this->response->array(['data' => $result]);
    }
} 