<template>
  <div class="placeholder-page">
    <div class="placeholder-content">
      <el-empty
        :image-size="200"
        description="页面开发中，敬请期待..."
      >
        <el-button type="primary" @click="goBack">返回</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PlaceholderPage',
  methods: {
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.placeholder-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.placeholder-content {
  text-align: center;
}
</style>
