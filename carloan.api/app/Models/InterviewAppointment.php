<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InterviewAppointment extends BaseModel
{
    use SoftDeletes;

    protected $table = 'interview_appointments';

    protected $fillable = [
        'application_id',
        'user_id',
        'appointment_no',
        'status',
        'status_text',
        'appointment_time',
        'appointment_location',
        'appointment_notes',
        'interview_result',
        'interview_notes',
        'interview_materials',
        'created_time',
        'scheduled_time',
        'completed_time',
        'cancelled_time',
        'interviewer_id',
        'cancel_reason',
        'operation_log'
    ];

    protected $casts = [
        'interview_materials' => 'array',
        'operation_log' => 'array',
        'interview_result' => 'boolean',
        'appointment_time' => 'datetime',
        'created_time' => 'datetime',
        'scheduled_time' => 'datetime',
        'completed_time' => 'datetime',
        'cancelled_time' => 'datetime'
    ];

    protected $dates = [
        'appointment_time',
        'created_time',
        'scheduled_time',
        'completed_time',
        'cancelled_time',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 预约状态常量
    const STATUS_PENDING = 'pending';       // 待预约
    const STATUS_SCHEDULED = 'scheduled';   // 已预约
    const STATUS_COMPLETED = 'completed';   // 已完成
    const STATUS_CANCELLED = 'cancelled';   // 已取消

    /**
     * 关联业务申请
     */
    public function application()
    {
        return $this->belongsTo(BusinessApplication::class, 'application_id');
    }

    /**
     * 关联业务员
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联面审官
     */
    public function interviewer()
    {
        return $this->belongsTo(User::class, 'interviewer_id');
    }

    /**
     * 作用域：当前用户的记录
     */
    public function scopeForUser($query, $userId = null)
    {
        $userId = $userId ?: Auth::id();
        return $query->where('user_id', $userId);
    }

    /**
     * 生成预约单号
     */
    public static function generateAppointmentNo()
    {
        return 'INT' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute($value)
    {
        if ($value) {
            return $value;
        }

        $statusMap = [
            self::STATUS_PENDING => '待预约',
            self::STATUS_SCHEDULED => '已预约',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 预约面审
     */
    public function scheduleInterview($appointmentTime, $location, $notes = null)
    {
        $this->update([
            'status' => self::STATUS_SCHEDULED,
            'status_text' => '已预约',
            'appointment_time' => $appointmentTime,
            'appointment_location' => $location,
            'appointment_notes' => $notes,
            'scheduled_time' => Carbon::now()
        ]);

        // 更新业务申请状态
        $this->application()->update([
            'status' => BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
            'status_text' => '已预约面审'
        ]);

        // 记录操作日志
        $this->addOperationLog('schedule', '预约面审', Auth::id());
    }

    /**
     * 完成面审
     */
    public function completeInterview($result, $notes = null, $materials = null)
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'status_text' => '已完成',
            'interview_result' => $result,
            'interview_notes' => $notes,
            'interview_materials' => $materials,
            'completed_time' => Carbon::now(),
            'interviewer_id' => Auth::id()
        ]);

        // 更新业务申请状态
        $newStatus = $result ? BusinessApplication::STATUS_INTERVIEW_COMPLETED : BusinessApplication::STATUS_REJECTED;
        $newStatusText = $result ? '面审完成' : '面审不通过';
        
        $this->application()->update([
            'status' => $newStatus,
            'status_text' => $newStatusText
        ]);

        // 记录操作日志
        $this->addOperationLog('complete', '完成面审', Auth::id());
    }

    /**
     * 取消预约
     */
    public function cancelAppointment($reason)
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'status_text' => '已取消',
            'cancel_reason' => $reason,
            'cancelled_time' => Carbon::now()
        ]);

        // 更新业务申请状态
        $this->application()->update([
            'status' => BusinessApplication::STATUS_INTERVIEW_PENDING,
            'status_text' => '待面审'
        ]);

        // 记录操作日志
        $this->addOperationLog('cancel', '取消预约', Auth::id());
    }

    /**
     * 添加操作日志
     */
    protected function addOperationLog($action, $description, $userId)
    {
        $logs = $this->operation_log ?: [];
        $logs[] = [
            'action' => $action,
            'description' => $description,
            'user_id' => $userId,
            'created_at' => Carbon::now()->toDateTimeString()
        ];
        
        $this->update(['operation_log' => $logs]);
    }

    /**
     * 检查是否可以预约
     */
    public function canSchedule()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 检查是否可以取消
     */
    public function canCancel()
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_SCHEDULED]);
    }

    /**
     * 检查是否可以完成
     */
    public function canComplete()
    {
        return $this->status === self::STATUS_SCHEDULED;
    }
} 