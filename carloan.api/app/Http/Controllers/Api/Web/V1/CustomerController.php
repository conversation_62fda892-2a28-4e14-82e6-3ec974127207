<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\Customer;
use App\Transformers\Web\CustomerTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class CustomerController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 客户列表
     * @param Request $request
     * @return \Dingo\Api\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $keyword = $request->get('keyword', '');
            $status = $request->get('status', '');
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 20);

            $query = Customer::with(['riskQueries' => function($query) {
                $query->latest();
            }])->orderBy('created_at', 'desc');

            // 搜索条件
            if ($keyword !== '') {
                $query = $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($status !== '') {
                $query = $query->where('status', $status);
            }

            $customers = $query->paginate($pageSize, ['*'], 'page', $page);
            
            // 格式化数据
            $transformer = new CustomerTransformer();
            $list = $customers->getCollection()->map(function ($customer) use ($transformer) {
                return $transformer->transform($customer);
            });

            return $this->successHandler('获取客户列表成功', 200, [
                'list' => $list,
                'pagination' => [
                    'current_page' => $customers->currentPage(),
                    'last_page' => $customers->lastPage(),
                    'per_page' => $customers->perPage(),
                    'total' => $customers->total(),
                    'has_more' => $customers->hasMorePages()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('获取客户列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取客户列表失败', 500);
        }
    }

    /**
     * 新增客户
     * @param Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/|unique:customers,phone',
                'name' => 'nullable|string|max:20',
                'id_card' => 'nullable|string|size:18|unique:customers,id_card',
                'gender' => 'nullable|integer|in:0,1,2',
                'nation' => 'nullable|string|max:10',
                'address' => 'nullable|string|max:200',
                'authority' => 'nullable|string|max:50',
                'valid_start' => 'nullable|date',
                'valid_end' => 'nullable|date|after:valid_start',
                'status' => 'nullable|string|in:active,inactive,blacklist',
            ], [
                'phone.required' => '手机号不能为空',
                'phone.regex' => '请输入正确的手机号码',
                'phone.unique' => '该手机号已存在',
                'id_card.size' => '身份证号必须为18位',
                'id_card.unique' => '该身份证号已存在',
                'gender.in' => '性别参数错误',
                'valid_end.after' => '有效期结束日期必须大于开始日期',
                'status.in' => '状态参数错误',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            $data = $request->only([
                'phone', 'name', 'id_card', 'gender', 'nation', 
                'address', 'authority', 'valid_start', 'valid_end', 'status'
            ]);

            $data['created_by'] = Auth::id();
            $data['status'] = $data['status'] ?? 'active';

            $customer = Customer::create($data);

            return $this->successHandler('新增客户成功', 200, [
                'customer_id' => $customer->id
            ]);

        } catch (\Exception $e) {
            \Log::error('新增客户失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('新增客户失败', 500);
        }
    }

    /**
     * 客户详情
     * @param $id
     * @return \Dingo\Api\Http\Response
     */
    public function show($id)
    {
        try {
            $customer = Customer::find($id);
            if (!$customer) {
                return $this->errorHandler('客户不存在', 404);
            }

            $transformer = new CustomerTransformer();
            $data = $transformer->transform($customer);

            return $this->successHandler('获取客户详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取客户详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取客户详情失败', 500);
        }
    }

    /**
     * 编辑客户
     * @param $id
     * @param Request $request
     * @return mixed
     */
    public function update($id, Request $request)
    {
        try {
            $customer = Customer::find($id);
            if (!$customer) {
                return $this->errorHandler('客户不存在', 404);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/|unique:customers,phone,' . $id,
                'name' => 'nullable|string|max:20',
                'id_card' => 'nullable|string|size:18|unique:customers,id_card,' . $id,
                'gender' => 'nullable|integer|in:0,1,2',
                'nation' => 'nullable|string|max:10',
                'address' => 'nullable|string|max:200',
                'authority' => 'nullable|string|max:50',
                'valid_start' => 'nullable|date',
                'valid_end' => 'nullable|date|after:valid_start',
                'status' => 'nullable|string|in:active,inactive,blacklist',
            ], [
                'phone.required' => '手机号不能为空',
                'phone.regex' => '请输入正确的手机号码',
                'phone.unique' => '该手机号已存在',
                'id_card.size' => '身份证号必须为18位',
                'id_card.unique' => '该身份证号已存在',
                'gender.in' => '性别参数错误',
                'valid_end.after' => '有效期结束日期必须大于开始日期',
                'status.in' => '状态参数错误',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            $data = $request->only([
                'phone', 'name', 'id_card', 'gender', 'nation', 
                'address', 'authority', 'valid_start', 'valid_end', 'status'
            ]);

            $customer->update($data);

            return $this->successHandler('编辑客户成功', 200);

        } catch (\Exception $e) {
            \Log::error('编辑客户失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('编辑客户失败', 500);
        }
    }

    /**
     * 删除客户
     * @param $id
     * @return mixed
     */
    public function destroy($id)
    {
        try {
            $customer = Customer::find($id);
            if (!$customer) {
                return $this->errorHandler('客户不存在', 404);
            }

            // 检查是否有关联的业务申请
            $hasApplications = $customer->businessApplications()->exists();
            if ($hasApplications) {
                return $this->errorHandler('该客户存在关联的业务申请，无法删除', 400);
            }

            $customer->delete();

            return $this->successHandler('删除客户成功', 200);

        } catch (\Exception $e) {
            \Log::error('删除客户失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('删除客户失败', 500);
        }
    }

    /**
     * 客户验证
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            ], [
                'phone.required' => '手机号不能为空',
                'phone.regex' => '请输入正确的手机号码',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            $phone = $request->input('phone');
            
            // 查找客户记录
            $customer = Customer::where('phone', $phone)->first();
            
            if ($customer) {
                // 客户存在 - 检查是否有进行中的业务申请
                $existingApplication = $customer->businessApplications()
                    ->whereIn('status', ['pending', 'reviewing', 'interview', 'final_review'])
                    ->first();

                if ($existingApplication) {
                    return $this->errorHandler('该客户已有进行中的业务申请，请勿重复创建', 400);
                }

                // 获取渠道信息（如果用户已登录）
                $channelName = '';
                if (Auth::check()) {
                    $user = Auth::user();
                    if ($user->channel) {
                        $channelName = $user->channel->name;
                    }
                }

                return $this->successHandler('验证成功', 200, [
                    'exists' => true,
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'customer_id_card' => $customer->id_card,
                    'phone' => $customer->phone,
                    'channel_name' => $channelName,
                    'created_at' => $customer->created_at->format('Y-m-d H:i:s'),
                    'message' => '客户验证成功'
                ]);
            } else {
                // 客户不存在
                return $this->successHandler('客户不存在', 200, [
                    'exists' => false,
                    'phone' => $phone,
                    'message' => '该手机号尚未注册，请补充客户信息'
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('客户验证失败', [
                'phone' => $request->input('phone'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorHandler('验证失败，请重试', 500);
        }
    }
} 