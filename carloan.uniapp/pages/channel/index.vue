<template>
	<!-- 渠道管理 -->
	<view class="channel-management">
		<!-- 分页列表 -->
		<z-paging 
			ref="paging" 
			v-model="dataList" 
			@query="queryList"
			:refresher-enabled="true"
			:loading-more-enabled="true"
			:empty-view-text="emptyText"
			:empty-view-img="emptyImg"
		>
			<!-- 顶部固定内容 -->
			<template #top>
				<navbar title="渠道管理" />
				<view class="filter">
					<!-- 搜索区域 -->
					<view class="search-section">
						<view class="search-box">
							<image
								class="search-icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-search.png"
							/>
							<input
								class="search-input"
								v-model="searchKeyword"
								placeholder="搜索渠道名称"
								@input="onSearchInput"
							/>
						</view>
						<picker 
							:value="statusIndex" 
							:range="statusOptions" 
							range-key="label"
							@change="handleStatusChange"
						>
							<view class="filter-item">
								<text>{{ statusOptions[statusIndex].label }}</text>
								<wd-icon name="arrow-down" size="22px"></wd-icon>
							</view>
						</picker>
					</view>
					
					<!-- 新增按钮 -->
					<view class="add-section">
						<button class="btn btn-primary" @click="goCreateChannel">
							<image
								class="btn-icon"
								src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-add-white.png"
							/>
							新增渠道
						</button>
					</view>
				</view>
			</template>

			<!-- 列表内容 -->
			<view class="list">
				<view class="channel-card" v-for="item in dataList" :key="item.id" @click="onItemClick(item)">
					<view class="card-header">
						<view class="channel-info">
							<view class="channel-name">{{ item.name }}</view>
							<view class="channel-code">渠道码：{{ item.code }}</view>
						</view>
						<view class="status-tag" :class="item.status === 1 ? 'active' : 'inactive'">
							{{ item.status_text }}
						</view>
					</view>
					<view class="card-footer">
						<view class="info-row">
							<view class="info-item">
								<text class="label">排序：</text>
								<text class="value">{{ item.sort }}</text>
							</view>
							<view class="info-item">
								<text class="label">创建时间：</text>
								<text class="value">{{ formatDate(item.created_at) }}</text>
							</view>
						</view>
						<view class="action-row">
							<button class="btn btn-edit" @click.stop="onEditChannel(item)">
								编辑
							</button>
							<button class="btn btn-delete" @click.stop="onDeleteChannel(item)">
								删除
							</button>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 响应式数据
const dataList = ref([]);
const isInitialLoading = ref(true);
const searchKeyword = ref('');
const searchTimer = ref(null);

// 状态选项
const statusOptions = ref([
	{ label: '全部状态', value: '' },
	{ label: '启用', value: 1 },
	{ label: '禁用', value: 0 }
]);
const statusIndex = ref(0);

// 空状态配置
const emptyText = computed(() => {
	if (isInitialLoading.value) {
		return '加载中...';
	}
	return '暂无渠道数据';
});

const emptyImg = 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/empty.png';

// 分页组件引用
const paging = ref(null);

// 页面显示时刷新数据
onShow(() => {
	if (paging.value) {
		paging.value.reload();
	}
});

// 查询列表数据
const queryList = async (pageNo, pageSize) => {
	try {
		const params = {
			page: pageNo,
			page_size: pageSize,
			name: searchKeyword.value,
			status: statusOptions.value[statusIndex.value].value
		};
		
		const response = await request.get('/channels', params);
		
		// 添加调试信息
		console.log('渠道管理API Response:', response);
		
		// 根据实际API格式判断成功状态
		let isSuccess = false;
		let list = [];
		
		if (response && response.status_code === 200) {
			// 正确的响应格式: { status_code: 200, message: "...", data: { list: [], pagination: {} } }
			isSuccess = true;
			list = response.data?.list || [];
		}
		
		console.log('渠道管理处理后数据:', { isSuccess, list });
		
		if (isSuccess) {
			// 确保 list 是数组
			const dataList = Array.isArray(list) ? list : [];
			
			console.log('渠道管理最终数据:', dataList);
			
			// 完成分页加载
			if (paging.value) {
				paging.value.complete(dataList);
				
				// 标记初始加载完成
				isInitialLoading.value = false;
			}
		} else {
			console.error('渠道管理API调用失败:', response);
			isInitialLoading.value = false;
			if (paging.value) {
				paging.value.complete(false);
			}
			// 显示错误信息
			const errorMessage = response?.message || '获取数据失败';
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('查询渠道列表失败:', error);
		isInitialLoading.value = false;
		if (paging.value) {
			paging.value.complete(false);
		}
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		});
	}
};

// 搜索输入处理
const onSearchInput = () => {
	// 防抖处理，减少频繁请求
	if (searchTimer.value) {
		clearTimeout(searchTimer.value);
	}
	searchTimer.value = setTimeout(() => {
		if (paging.value) {
			paging.value.reload();
		}
	}, 500);
};

// 状态筛选处理
const handleStatusChange = (e) => {
	statusIndex.value = e.detail.value;
	if (paging.value) {
		paging.value.reload();
	}
};

// 点击列表项
const onItemClick = (item) => {
	// 可以在这里添加编辑功能
	console.log('点击渠道:', item);
};

// 跳转新增渠道页面
const goCreateChannel = () => {
	uni.navigateTo({
		url: '/pages/channel/create'
	});
};

// 格式化日期
const formatDate = (dateString) => {
	if (!dateString) return '';
	const date = new Date(dateString);
	return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 编辑渠道
const onEditChannel = (item) => {
	console.log('编辑渠道:', item);
	uni.navigateTo({
		url: `/pages/channel/create?id=${item.id}`
	});
};

// 删除渠道
const onDeleteChannel = async (item) => {
	try {
		const result = await uni.showModal({
			title: '确认删除',
			content: `确定要删除渠道"${item.name}"吗？删除后无法恢复。`,
			confirmText: '删除',
			cancelText: '取消',
			confirmColor: '#ff4757'
		});
		
		if (!result.confirm) {
			return;
		}
		
		uni.showLoading({ title: '删除中...' });
		
		const response = await request.delete(`/channels/${item.id}`);
		
		uni.hideLoading();
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '删除成功',
				icon: 'success'
			});
			
			// 刷新列表
			if (paging.value) {
				paging.value.reload();
			}
		} else {
			const errorMessage = response.message || '删除失败';
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			});
		}
	} catch (error) {
		uni.hideLoading();
		console.error('删除渠道失败:', error);
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		});
	}
};
</script>

<style lang="scss" scoped>
.channel-management {
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
	
	.filter {
		background: #f3f3f3;
		z-index: 999;
		
		.search-section {
			display: flex;
			align-items: center;
			padding: 32rpx 40rpx;
			background: #ffffff;
			gap: 20rpx;
			
			.search-box {
				flex: 1;
				position: relative;
				
				.search-icon {
					position: absolute;
					left: 24rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 32rpx;
					height: 32rpx;
					z-index: 1;
				}
				
				.search-input {
					width: 100%;
					height: 72rpx;
					background: #f5f5f5;
					border-radius: 36rpx;
					padding: 0 24rpx 0 68rpx;
					font-size: 28rpx;
					color: #000000;
					box-sizing: border-box;
					border: 2rpx solid #d0d0d0;
					
					&::placeholder {
						color: #999999;
					}
				}
			}
			
			.filter-item {
				display: flex;
				align-items: center;
				background: #ffffff;
				padding: 16rpx 24rpx;
				border-radius: 36rpx;
				font-size: 28rpx;
				color: #000000;
				border: 2rpx solid #d0d0d0;
				
				.arrow {
					width: 24rpx;
					height: 24rpx;
					margin-left: 12rpx;
				}
			}
		}
		
		.add-section {
			padding: 0 40rpx 32rpx;
			
			.btn {
				width: 100%;
				height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
				font-weight: 500;
				border: none;
				display: flex;
				align-items: center;
				justify-content: center;
				
				&.btn-primary {
					background: #000000;
					color: #ffffff;
					
					.btn-icon {
						width: 32rpx;
						height: 32rpx;
						margin-right: 12rpx;
					}
				}
			}
		}
	}
	
	.list {
		.channel-card {
			background: #ffffff;
			border-radius: 16rpx;
			padding: 32rpx;
			margin: 20rpx 40rpx;
			box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0, 0, 0, 0.06);
			
			.card-header {
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				margin-bottom: 0rpx;
				
				.channel-info {
					flex: 1;
					
					.channel-name {
						font-weight: 500;
						font-size: 32rpx;
						color: #000000;
						margin-bottom: 8rpx;
					}
					
					.channel-code {
						font-size: 24rpx;
						color: #666666;
					}
				}
				
				.status-tag {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;
					font-weight: 500;
					
					&.active {
						background: #e8f5e8;
						color: #52c41a;
					}
					
					&.inactive {
						background: #f0f0f0;
						color: #999999;
					}
				}
			}
			
			.card-footer {
				.info-row {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 8rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.label {
						font-size: 24rpx;
						color: #666666;
					}
					
					.value {
						font-size: 24rpx;
						color: #000000;
					}
				}
				
				.action-row {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					gap: 16rpx;
					margin-top: 20rpx;
					
					.btn {
						padding: 6rpx 34rpx;
						border-radius: 24rpx;
						font-size: 24rpx;
						font-weight: 500;
						border: none;
						min-width: 80rpx;
						
						&.btn-edit {
							background: #000000;
							color: #ffffff;
						}
						
						&.btn-delete {
							background: #8b0000;
							color: #ffffff;
						}
					}
				}
			}
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 