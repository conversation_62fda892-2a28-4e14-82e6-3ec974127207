-- 客户补件功能相关表结构
-- 创建时间: 2024-03-15
-- 说明: 包含业务申请表、客户补件表及测试数据

-- 1. 创建业务申请表
CREATE TABLE `business_applications` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_no` varchar(32) NOT NULL COMMENT '申请单号',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `channel_code` varchar(20) NOT NULL DEFAULT '' COMMENT '渠道码',
  `customer_name` varchar(50) NOT NULL DEFAULT '' COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '客户手机号',
  `customer_id_card` varchar(18) NOT NULL DEFAULT '' COMMENT '客户身份证号',
  `product_name` varchar(100) NOT NULL DEFAULT '' COMMENT '产品名称',
  `loan_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '贷款金额',
  `loan_period` int(11) NOT NULL DEFAULT '0' COMMENT '贷款期限(月)',
  `interest_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '利率',
  `vehicle_brand` varchar(50) NOT NULL DEFAULT '' COMMENT '车辆品牌',
  `vehicle_model` varchar(100) NOT NULL DEFAULT '' COMMENT '车辆型号',
  `vehicle_vin` varchar(50) NOT NULL DEFAULT '' COMMENT '车架号',
  `vehicle_year` year(4) DEFAULT NULL COMMENT '车辆年份',
  `vehicle_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '车辆价格',
  `status` varchar(30) NOT NULL DEFAULT 'submitted' COMMENT '业务状态',
  `status_text` varchar(50) NOT NULL DEFAULT '已提交' COMMENT '状态文本',
  `approval_notes` text COMMENT '审批备注',
  `approval_history` json DEFAULT NULL COMMENT '审批历史',
  `submit_time` timestamp NULL DEFAULT NULL COMMENT '提交时间',
  `approval_time` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `need_supplement` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否需要补件:0否,1是',
  `supplement_reason` text COMMENT '补件原因',
  `supplement_deadline` timestamp NULL DEFAULT NULL COMMENT '补件截止时间',
  `customer_data` json DEFAULT NULL COMMENT '客户详细数据',
  `risk_assessment` json DEFAULT NULL COMMENT '风险评估结果',
  `attachments` json DEFAULT NULL COMMENT '附件列表',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `business_applications_application_no_unique` (`application_no`),
  KEY `business_applications_user_id_index` (`user_id`),
  KEY `business_applications_channel_code_index` (`channel_code`),
  KEY `business_applications_customer_phone_index` (`customer_phone`),
  KEY `business_applications_customer_id_card_index` (`customer_id_card`),
  KEY `business_applications_status_index` (`status`),
  KEY `business_applications_status_user_id_index` (`status`,`user_id`),
  KEY `business_applications_customer_phone_customer_id_card_index` (`customer_phone`,`customer_id_card`),
  KEY `business_applications_submit_time_index` (`submit_time`),
  KEY `business_applications_need_supplement_index` (`need_supplement`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务申请表';

-- 2. 创建客户补件表
CREATE TABLE `customer_supplements` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务申请ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `supplement_no` varchar(32) NOT NULL COMMENT '补件单号',
  `reason` text NOT NULL COMMENT '补件原因',
  `requirements` text NOT NULL COMMENT '补件要求',
  `required_documents` json DEFAULT NULL COMMENT '需要补充的文档列表',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '补件状态:pending待补件,submitted已提交,approved已审核,rejected已驳回',
  `status_text` varchar(50) NOT NULL DEFAULT '待补件' COMMENT '状态文本',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `deadline` timestamp NULL DEFAULT NULL COMMENT '补件截止时间',
  `submitted_time` timestamp NULL DEFAULT NULL COMMENT '客户提交时间',
  `reviewed_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `submitted_documents` json DEFAULT NULL COMMENT '客户提交的文档',
  `customer_notes` text COMMENT '客户备注',
  `reviewer_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '审核人ID',
  `review_notes` text COMMENT '审核备注',
  `review_result` tinyint(4) DEFAULT NULL COMMENT '审核结果:1通过,0驳回',
  `operation_log` json DEFAULT NULL COMMENT '操作日志',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customer_supplements_supplement_no_unique` (`supplement_no`),
  KEY `customer_supplements_application_id_index` (`application_id`),
  KEY `customer_supplements_user_id_index` (`user_id`),
  KEY `customer_supplements_status_index` (`status`),
  KEY `customer_supplements_status_user_id_index` (`status`,`user_id`),
  KEY `customer_supplements_deadline_index` (`deadline`),
  KEY `customer_supplements_submitted_time_index` (`submitted_time`),
  CONSTRAINT `customer_supplements_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户补件表';

-- 3. 插入测试数据

-- 插入测试业务申请数据
INSERT INTO `business_applications` (
  `application_no`, `user_id`, `channel_code`, `customer_name`, `customer_phone`, `customer_id_card`,
  `product_name`, `loan_amount`, `loan_period`, `interest_rate`, `vehicle_brand`, `vehicle_model`,
  `vehicle_vin`, `vehicle_year`, `vehicle_price`, `status`, `status_text`, `need_supplement`,
  `supplement_reason`, `submit_time`, `supplement_deadline`, `created_at`, `updated_at`
) VALUES 
(
  'APP20240315001', 1, 'DEFAULT', '张三', '***********', '110101199001011234',
  '24年以租代售计划', 150000.00, 24, 0.0650, '丰田', '凯美瑞',
  'JTDKN3DU5E0123456', 2023, 200000.00, 'supplement_required', '需要补件', 1,
  '收入证明不完整，需要补充银行流水', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_ADD(NOW(), INTERVAL 4 DAY),
  DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
  'APP20240315002', 1, 'DEFAULT', '李四', '***********', '110101199002021234',
  '36年车贷计划', 200000.00, 36, 0.0720, '本田', '雅阁',
  'JHMCR6F70EC123456', 2024, 250000.00, 'supplement_required', '需要补件', 1,
  '身份证照片不清晰，需要重新上传', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)
),
(
  'APP20240315003', 1, 'DEFAULT', '王五', '13800138003', '110101199003031234',
  '12年短期贷款', 80000.00, 12, 0.0580, '大众', '帕萨特',
  'WVWZZZ3CZ9E123456', 2022, 180000.00, 'initial_review', '初审中', 0,
  NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), NULL,
  DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR)
);

-- 插入测试补件数据
INSERT INTO `customer_supplements` (
  `application_id`, `user_id`, `supplement_no`, `reason`, `requirements`, `required_documents`,
  `status`, `status_text`, `created_time`, `deadline`, `created_at`, `updated_at`
) VALUES 
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240315001'),
  1, 'SUP20240315001', '收入证明不完整，需要补充银行流水',
  '请提供最近6个月的银行流水账单，需要加盖银行公章',
  JSON_ARRAY(
    JSON_OBJECT('name', '银行流水', 'type', 'pdf', 'required', true),
    JSON_OBJECT('name', '收入证明', 'type', 'image', 'required', true)
  ),
  'pending', '待补件', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_ADD(NOW(), INTERVAL 4 DAY),
  DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)
),
(
  (SELECT id FROM business_applications WHERE application_no = 'APP20240315002'),
  1, 'SUP20240315002', '身份证照片不清晰，需要重新上传',
  '请重新拍摄身份证正反面照片，确保字迹清晰可见',
  JSON_ARRAY(
    JSON_OBJECT('name', '身份证正面', 'type', 'image', 'required', true),
    JSON_OBJECT('name', '身份证反面', 'type', 'image', 'required', true)
  ),
  'rejected', '已驳回', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)
);

-- 更新已驳回补件的详细信息
UPDATE `customer_supplements` 
SET 
  `submitted_time` = DATE_SUB(NOW(), INTERVAL 3 DAY),
  `reviewed_time` = DATE_SUB(NOW(), INTERVAL 2 DAY),
  `submitted_documents` = JSON_ARRAY(
    JSON_OBJECT('name', '身份证正面.jpg', 'url', 'https://example.com/id_front.jpg', 'type', 'image'),
    JSON_OBJECT('name', '身份证反面.jpg', 'url', 'https://example.com/id_back.jpg', 'type', 'image')
  ),
  `customer_notes` = '已重新拍摄上传',
  `reviewer_id` = 1,
  `review_notes` = '照片仍然不够清晰，请在光线充足的环境下重新拍摄',
  `review_result` = 0,
  `operation_log` = JSON_ARRAY(
    JSON_OBJECT(
      'action', 'create',
      'description', '创建补件记录',
      'user_id', 1,
      'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')
    ),
    JSON_OBJECT(
      'action', 'customer_submit',
      'description', '客户提交补件材料',
      'user_id', 1,
      'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')
    ),
    JSON_OBJECT(
      'action', 'review',
      'description', '补件审核驳回',
      'user_id', 1,
      'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')
    )
  )
WHERE supplement_no = 'SUP20240315002';

-- 4. 创建索引（如果上面的建表语句中的索引创建失败，可以单独执行）

-- 业务申请表索引
-- ALTER TABLE `business_applications` ADD INDEX `idx_user_status` (`user_id`, `status`);
-- ALTER TABLE `business_applications` ADD INDEX `idx_customer_search` (`customer_name`, `customer_phone`);
-- ALTER TABLE `business_applications` ADD INDEX `idx_submit_time` (`submit_time`);
-- ALTER TABLE `business_applications` ADD INDEX `idx_supplement` (`need_supplement`, `supplement_deadline`);

-- 客户补件表索引  
-- ALTER TABLE `customer_supplements` ADD INDEX `idx_user_status` (`user_id`, `status`);
-- ALTER TABLE `customer_supplements` ADD INDEX `idx_deadline_status` (`deadline`, `status`);
-- ALTER TABLE `customer_supplements` ADD INDEX `idx_submitted_time` (`submitted_time`);

-- 5. 查询验证数据

-- 查看业务申请数据
-- SELECT * FROM business_applications;

-- 查看补件数据
-- SELECT * FROM customer_supplements;

-- 查看关联查询
-- SELECT 
--   cs.supplement_no,
--   cs.status as supplement_status,
--   ba.application_no,
--   ba.customer_name,
--   ba.product_name,
--   cs.deadline,
--   cs.created_time
-- FROM customer_supplements cs
-- LEFT JOIN business_applications ba ON cs.application_id = ba.id; 