<template>
	<view class="vehicle-select">
		
		<z-paging 
			ref="paging" 
			v-model="vehicleList" 
			@query="queryList"
			:refresher-enabled="true"
			:loading-more-enabled="true"
			:empty-view-text="'暂无车辆数据'"
			:empty-view-img="'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-empty.png'"
			:paging-style="{ background: '#f3f3f3' }"
		>
            <template #top>
                <navbar title="选择业务车辆" />
            </template>


			<view 
				v-for="item in vehicleList" 
				:key="item.id" 
				class="vehicle-item"
				@click="handleSelectVehicle(item)"
			>
				<!-- 左侧 checkbox -->
				<view class="checkbox-column">
					<view class="checkbox-box" :class="{ checked: selectedVehicleId === item.id }">
						<view class="checkbox-dot" v-if="selectedVehicleId === item.id"></view>
					</view>
				</view>
				
				<!-- 右侧卡片 -->
				<view class="vehicle-card" :class="{ selected: selectedVehicleId === item.id }">
					<view class="card-header">
						<text class="title">车辆评估</text>
						<view class="condition">
							<text class="label">车况：</text>
							<text class="status" :class="item.condition">{{ item.condition_text }}</text>
						</view>
					</view>

					<view class="car-info">
						<view class="info-item">
							<text class="label">品牌型号：</text>
							<text class="value">{{ item.model_name }}</text>
						</view>
						<view class="info-item">
							<text class="label">车架号：</text>
							<text class="value">{{ item.vin }}</text>
						</view>
					</view>

					<view class="price-table">
						<view class="table-row header-row">
							<view class="table-cell">车商零售</view>
							<view class="table-cell">个人交易</view>
							<view class="table-cell">车商收车</view>
							<view class="table-cell">个人最低交易</view>
						</view>
						<view class="table-row data-row">
							<view class="table-cell">¥{{ item.formatted_prices.dealer_price }}万</view>
							<view class="table-cell">¥{{ item.formatted_prices.individual_price }}万</view>
							<view class="table-cell">¥{{ item.formatted_prices.dealer_buy_price }}万</view>
							<view class="table-cell">¥{{ item.formatted_prices.individual_low_price }}万</view>
						</view>
						
						<view class="table-row header-row three-cols">
							<view class="table-cell">车商最低收车</view>
							<view class="table-cell">车商最高零售</view>
							<view class="table-cell">车商最低零售</view>
						</view>
						<view class="table-row data-row three-cols">
							<view class="table-cell">¥{{ item.formatted_prices.dealer_low_buy_price }}万</view>
							<view class="table-cell">¥{{ item.formatted_prices.dealer_high_sold_price }}万</view>
							<view class="table-cell">¥{{ item.formatted_prices.dealer_low_sold_price }}万</view>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onShow } from '@dcloudio/uni-app';
import request from "@/utils/request";
import navbar from "@/components/navbar/index.vue";

const vehicleList = ref([]);
const paging = ref(null);
const fromPage = ref('');
const selectedVehicleId = ref(null);

onLoad((options) => {
	if (options.from) {
		fromPage.value = options.from;
	}
	// 页面加载时也尝试加载已选择的车辆
	loadSelectedVehicle();
});

const queryList = async (pageNo, pageSize) => {
	try {
		const params = {
			page: pageNo,
			per_page: pageSize,
		};
		
		const response = await request.get("/vehicle-assessments", params);
		
		let isSuccess = false;
		let dataList = [];
		
		if (response && response.status_code === 200) {
			isSuccess = true;
			dataList = response.data?.list || [];
		}
		
		if (isSuccess) {
			const list = Array.isArray(dataList) ? dataList : [];
			
			if (paging.value) {
				paging.value.complete(list);
			}
			
			// 设置已选择的车辆
			loadSelectedVehicle();
		} else {
			if (paging.value) {
				paging.value.complete(false);
			}
			const errorMessage = response?.message || "获取车辆列表失败";
			uni.showToast({
				title: errorMessage,
				icon: "none",
			});
		}
	} catch (error) {
		if (paging.value) {
			paging.value.complete(false);
		}
		uni.showToast({
			title: error.message || "网络错误，请重试",
			icon: "none",
		});
	}
};

const handleSelectVehicle = (vehicle) => {
	// 设置选中状态
	selectedVehicleId.value = vehicle.id;
	
	uni.showModal({
		title: '确认选择',
		content: `确定选择车辆：${vehicle.model_name}？`,
		success: (res) => {
			if (res.confirm) {
				try {
					// 保存选择的车辆到业务数据
					const businessData = uni.getStorageSync('business_draft') || {};
					businessData.vehicle_id = vehicle.id;
					businessData.vehicle_model = vehicle.model_name;
					businessData.vehicle_vin = vehicle.vin;
					businessData.vehicle_completed = true;
					
					// 如果是车辆评估模式，也要更新assessmentData
					if (businessData.page_mode === 'vehicle_assessment') {
						if (!businessData.assessment_data) {
							businessData.assessment_data = {};
						}
						businessData.assessment_data.vehicle_info_completed = true;
					}
					
					uni.setStorageSync('business_draft', businessData);
					
					uni.showToast({
						title: `已选择：${vehicle.model_name}`,
						icon: 'success'
					});
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1000);
					
				} catch (error) {
					console.error('保存车辆选择失败:', error);
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				}
			} else {
				// 取消选择时清除选中状态
				selectedVehicleId.value = null;
			}
		}
	});
};

// 加载已选择的车辆
const loadSelectedVehicle = () => {
	try {
		const businessData = uni.getStorageSync('business_draft');
		if (businessData && businessData.vehicle_id) {
			// 先设置选中的车辆ID
			selectedVehicleId.value = businessData.vehicle_id;
			
			// 如果车辆列表已加载，查找对应的车辆信息
			if (vehicleList.value.length > 0) {
				const vehicle = vehicleList.value.find(v => v.id === businessData.vehicle_id);
				if (vehicle) {
					console.log('已设置默认选中车辆:', vehicle.model_name);
				}
			}
		}
	} catch (error) {
		console.error('加载已选车辆失败:', error);
	}
};

onShow(() => {
	if (paging.value) {
		paging.value.reload();
	}
});
</script>

<style lang="scss" scoped>
.vehicle-select {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);

	.vehicle-item {
		margin: 48rpx 40rpx 0 40rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
	
	.checkbox-column {
		flex-shrink: 0;
		
		.checkbox-box {
			width: 44rpx;
			height: 44rpx;
			border-radius: 50%;
			border: 3rpx solid #ddd;
			background: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			
			&.checked {
				border: 3rpx solid #000000;
				background: #000000;
			}
			
			.checkbox-dot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: #ffffff;
			}
		}
	}

	.vehicle-card {
		flex: 1;
		background: #ffffff;
		border-radius: 16rpx;
		padding: 32rpx 24rpx;
		position: relative;
		
		&.selected {
			border: 2rpx solid #000000;
		}
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 32rpx;
		margin-bottom: 12rpx;
		border-bottom: 1rpx solid #ededed;

		.title {
			font-weight: 500;
			font-size: 30rpx;
			color: #000000;
		}
	}

	.condition {
		display: flex;
		align-items: center;

		.label {
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
		}
	}

	.status {
		font-weight: 400;
		font-size: 24rpx;

		&.excellent {
			color: #4793ea;
		}
		&.good {
			color: #c48d2e;
		}
		&.normal {
			color: #e02626;
		}
	}

	.car-info {
		margin-bottom: 22rpx;

		.info-item {
			display: flex;
			align-items: center;
			margin-top: 20rpx;

			.label {
				font-weight: 400;
				font-size: 28rpx;
				color: #999999;
			}

			.value {
				font-weight: 400;
				font-size: 28rpx;
				color: #000000;
			}
		}
	}

	.price-table {
		.table-row {
			display: flex;
			
			&.header-row {
				.table-cell {
					background: #f8f8f8;
					padding: 10rpx 0;
					border-top: 2rpx solid #d7d7d7;
					border-bottom: 2rpx solid #d7d7d7;
					border-right: 2rpx solid #d7d7d7;
					font-weight: 400;
					font-size: 22rpx;
					color: #000000;
					
					&:first-child {
						border-left: 2rpx solid #d7d7d7;
					}
				}
			}
			
			&.data-row {
				.table-cell {
					padding: 34rpx 0;
					border-bottom: 2rpx solid #d7d7d7;
					border-right: 2rpx solid #d7d7d7;
					font-weight: 400;
					font-size: 22rpx;
					color: #000000;
					
					&:first-child {
						border-left: 2rpx solid #d7d7d7;
					}
				}
			}
			
			&:not(.three-cols) {
				.table-cell {
					width: 25%;
					flex: none;
				}
			}
			
			&.three-cols {
				width: 75%;
				
				.table-cell {
					width: 33.33%;
					flex: none;
				}
			}
		}

		.table-cell {
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 