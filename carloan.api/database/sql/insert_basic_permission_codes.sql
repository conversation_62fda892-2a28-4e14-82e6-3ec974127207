-- 基础权限码数据插入
-- 如果permission_codes表为空或者需要重新初始化权限码数据，请运行此SQL

-- 清空现有数据（可选，谨慎使用）
-- TRUNCATE TABLE permission_codes;

-- 插入基础权限码数据
INSERT IGNORE INTO `permission_codes` (`code`, `name`, `group_name`, `description`, `frontend_route`, `status`, `created_at`, `updated_at`) VALUES
-- 通用权限
('*', '所有权限', '系统', '超级管理员权限，包含所有功能', '*', 1, NOW(), NOW()),
('dashboard', '仪表盘', '通用', '访问后台首页', '/dashboard', 1, NOW(), NOW()),

-- 业务管理权限
('business.view', '查看业务', '业务管理', '查看业务申请列表', '/business', 1, NOW(), NOW()),
('business.create', '创建业务', '业务管理', '创建业务申请', '/business/create', 1, NOW(), NOW()),
('business.edit', '编辑业务', '业务管理', '编辑业务申请', '/business/edit', 1, NOW(), NOW()),
('business.delete', '删除业务', '业务管理', '删除业务申请', NULL, 1, NOW(), NOW()),
('business.approve', '审批业务', '业务管理', '审批业务申请', '/business/approve', 1, NOW(), NOW()),

-- 客户管理权限
('customer.view', '查看客户', '客户管理', '查看客户列表', '/customer', 1, NOW(), NOW()),
('customer.create', '创建客户', '客户管理', '创建客户资料', '/customer/create', 1, NOW(), NOW()),
('customer.edit', '编辑客户', '客户管理', '编辑客户资料', '/customer/edit', 1, NOW(), NOW()),
('customer.delete', '删除客户', '客户管理', '删除客户资料', NULL, 1, NOW(), NOW()),
('customer.risk', '风险查询', '客户管理', '查询客户风险信息', '/customer/risk', 1, NOW(), NOW()),

-- 审批管理权限
('approval.pending', '待我审批', '审批管理', '查看待我审批的业务', '/approval/pending', 1, NOW(), NOW()),
('approval.cc', '抄送我的', '审批管理', '查看抄送我的业务', '/approval/cc', 1, NOW(), NOW()),
('approval.workflow', '审批流程', '审批管理', '管理审批流程', '/approval/workflow', 1, NOW(), NOW()),

-- 渠道管理权限
('channel.view', '查看渠道', '渠道管理', '查看渠道列表', '/channel', 1, NOW(), NOW()),
('channel.create', '创建渠道', '渠道管理', '创建渠道', '/channel/create', 1, NOW(), NOW()),
('channel.edit', '编辑渠道', '渠道管理', '编辑渠道信息', '/channel/edit', 1, NOW(), NOW()),

-- 报表权限
('report.business', '业务报表', '报表统计', '查看业务相关报表', '/report/business', 1, NOW(), NOW()),
('report.customer', '客户报表', '报表统计', '查看客户相关报表', '/report/customer', 1, NOW(), NOW()),
('report.channel', '渠道报表', '报表统计', '查看渠道相关报表', '/report/channel', 1, NOW(), NOW()),

-- 系统设置权限
('setting.users', '用户管理', '系统设置', '管理系统用户', '/setting/users', 1, NOW(), NOW()),
('setting.roles', '角色管理', '系统设置', '管理系统角色', '/setting/roles', 1, NOW(), NOW()),
('setting.permissions', '权限管理', '系统设置', '管理权限码', '/setting/permissions', 1, NOW(), NOW()),
('setting.basic', '基础设置', '系统设置', '系统基础设置', '/setting/basic', 1, NOW(), NOW());

-- 验证插入结果
SELECT 
    group_name,
    COUNT(*) as count,
    GROUP_CONCAT(name ORDER BY code SEPARATOR ', ') as permissions
FROM permission_codes 
WHERE status = 1
GROUP BY group_name
ORDER BY group_name; 