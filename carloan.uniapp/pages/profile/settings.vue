<template>
	<view class="settings">
		<navbar title="个人设置" />
		
		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>
		
		<view v-else class="container">
			<!-- 个人信息组 -->
			<view class="setting-group">
				<view class="group-title">个人信息</view>
				
				<!-- 头像设置 -->
				<view class="setting-item avatar-setting first-item">
					<view class="setting-left">
						<image 
							class="avatar" 
							:src="(userInfo && userInfo.avatar) || 'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/member/avatar.png'" 
						/>
						<view class="setting-label">头像</view>
					</view>
					<view class="setting-right">
						<!-- #ifdef MP-WEIXIN -->
						<button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
							<wd-icon name="arrow-right" size="18px" color="#cccccc" />
						</button>
						<!-- #endif -->
						<!-- #ifndef MP-WEIXIN -->
						<view @click="chooseImage">
							<wd-icon name="arrow-right" size="18px" color="#cccccc" />
						</view>
						<!-- #endif -->
					</view>
				</view>

				<!-- 姓名设置 -->
				<view class="setting-item middle-item" @click="editName">
					<view class="setting-left">
						<text class="setting-label">姓名</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">{{ (userInfo && userInfo.name) || '未设置' }}</text>
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>

				<!-- 昵称设置 -->
				<view class="setting-item middle-item" @click="editNickname">
					<view class="setting-left">
						<text class="setting-label">昵称</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">{{ (userInfo && userInfo.nickname) || '未设置' }}</text>
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>

				<!-- 手机号显示（不可修改） -->
				<view class="setting-item last-item">
					<view class="setting-left">
						<text class="setting-label">手机号</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">{{ (userInfo && userInfo.phone) || '未绑定' }}</text>
					</view>
				</view>
			</view>

			<!-- 帮助与支持组 -->
			<view class="setting-group">
				<view class="group-title">帮助与支持</view>
				
				<!-- 关于我们 -->
				<view class="setting-item first-item" @click="aboutUs">
					<view class="setting-left">
						<text class="setting-label">关于我们</text>
					</view>
					<view class="setting-right">
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>

				<!-- 隐私政策 -->
				<view class="setting-item middle-item" @click="privacyPolicy">
					<view class="setting-left">
						<text class="setting-label">隐私政策</text>
					</view>
					<view class="setting-right">
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>

				<!-- 服务条款 -->
				<view class="setting-item middle-item" @click="serviceTerms">
					<view class="setting-left">
						<text class="setting-label">服务条款</text>
					</view>
					<view class="setting-right">
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>

				<!-- 联系我们 -->
				<view class="setting-item last-item" @click="contactUs">
					<view class="setting-left">
						<text class="setting-label">联系我们</text>
					</view>
					<view class="setting-right">
						<wd-icon name="arrow-right" size="18px" color="#cccccc" />
					</view>
				</view>
			</view>

			<!-- 应用信息组 -->
			<view class="setting-group">
				<view class="group-title">应用信息</view>
				
				<!-- 版本信息 -->
				<view class="setting-item single-item">
					<view class="setting-left">
						<text class="setting-label">当前版本</text>
					</view>
					<view class="setting-right">
						<text class="setting-value">v1.0.0</text>
					</view>
				</view>
			</view>

			<!-- 退出登录按钮 -->
			<view class="logout-section">
				<button class="logout-btn" @click="logout">退出登录</button>
			</view>
		</view>

		<!-- 编辑姓名弹窗 -->
		<wd-popup v-model="nameModalVisible" position="center" :closable="true">
			<view class="edit-modal">
				<view class="modal-title">修改姓名</view>
				<view class="modal-content">
					<wd-input 
						v-model="nameForm.name" 
						placeholder="请输入您的姓名"
						:maxlength="10"
						clearable
					/>
				</view>
				<view class="modal-actions">
					<button class="cancel-btn" @click="nameModalVisible = false">取消</button>
					<button class="confirm-btn" @click="saveName" :disabled="!(nameForm.name || '').trim()">确定</button>
				</view>
			</view>
		</wd-popup>

		<!-- 编辑昵称弹窗 -->
		<wd-popup v-model="nicknameModalVisible" position="center" :closable="true">
			<view class="edit-modal">
				<view class="modal-title">修改昵称</view>
				<view class="modal-content">
					<wd-input 
						v-model="nicknameForm.nickname" 
						placeholder="请输入您的昵称"
						:maxlength="20"
						clearable
					/>
				</view>
				<view class="modal-actions">
					<button class="cancel-btn" @click="nicknameModalVisible = false">取消</button>
					<button class="confirm-btn" @click="saveNickname" :disabled="!(nicknameForm.nickname || '').trim()">确定</button>
				</view>
			</view>
		</wd-popup>
	</view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import navbar from "@/components/navbar/index.vue";
import { onShow } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user";
import request from "@/utils/request";

const userStore = useUserStore();

// 响应式数据
const userInfo = ref({
	name: '',
	nickname: '',
	phone: '',
	avatar: ''
});

const nameModalVisible = ref(false);
const nicknameModalVisible = ref(false);
const isLoading = ref(true);

const nameForm = ref({
	name: ''
});

const nicknameForm = ref({
	nickname: ''
});

// 页面显示时加载用户信息
onShow(() => {
	loadUserInfo();
});

onMounted(() => {
	loadUserInfo();
});

// 加载用户信息
const loadUserInfo = async () => {
	try {
		isLoading.value = true;
		const res = await request.get('/profile/info');
		if (res && res.status_code === 200 && res.data && res.data.user_info) {
			// 确保userInfo对象始终存在
			userInfo.value = {
				name: res.data.user_info.name || '',
				nickname: res.data.user_info.nickname || '',
				phone: res.data.user_info.phone || '',
				avatar: res.data.user_info.avatar || ''
			};
		} else {
			console.warn('用户信息数据格式异常:', res);
			// 确保userInfo有默认值
			userInfo.value = {
				name: '',
				nickname: '',
				phone: '',
				avatar: ''
			};
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
		// 确保userInfo有默认值
		userInfo.value = {
			name: '',
			nickname: '',
			phone: '',
			avatar: ''
		};
		uni.showToast({
			title: '获取用户信息失败',
			icon: 'none'
		});
	} finally {
		isLoading.value = false;
	}
};

// 微信小程序选择头像
const onChooseAvatar = (e) => {
	console.log('选择的头像事件:', e);
	
	if (!e || !e.detail) {
		console.error('头像选择事件数据异常');
		return;
	}
	
	const avatarUrl = e.detail.avatarUrl;
	console.log('选择的头像:', avatarUrl);
	
	if (avatarUrl) {
		uploadAvatar(avatarUrl);
	}
};

// 非微信小程序选择图片
const chooseImage = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			const tempFilePath = res.tempFilePaths[0];
			uploadAvatar(tempFilePath);
		},
		fail: (error) => {
			console.error('选择图片失败:', error);
			uni.showToast({
				title: '选择图片失败',
				icon: 'none'
			});
		}
	});
};

// 上传头像
const uploadAvatar = async (filePath) => {
	if (!filePath) {
		uni.showToast({
			title: '无效的图片路径',
			icon: 'none'
		});
		return;
	}

	uni.showLoading({
		title: '上传中...'
	});

	try {
		const uploadRes = await new Promise((resolve, reject) => {
			uni.uploadFile({
				url: request.baseURL + '/profile/avatar',
				filePath: filePath,
				name: 'avatar',
				header: {
					'Authorization': 'Bearer ' + userStore.getToken
				},
				success: (res) => {
					try {
						const data = JSON.parse(res.data);
						if (data.status_code === 200) {
							resolve(data);
						} else {
							reject(new Error(data.message || '上传失败'));
						}
					} catch (parseError) {
						reject(new Error('服务器响应格式异常'));
					}
				},
				fail: reject
			});
		});

		if (uploadRes && uploadRes.data && uploadRes.data.avatar_url) {
			userInfo.value.avatar = uploadRes.data.avatar_url;
			uni.showToast({
				title: '头像更新成功',
				icon: 'success'
			});
		} else {
			throw new Error('上传响应数据异常');
		}

	} catch (error) {
		console.error('头像上传失败:', error);
		uni.showToast({
			title: error.message || '头像上传失败',
			icon: 'none'
		});
	} finally {
		uni.hideLoading();
	}
};

// 编辑姓名
const editName = () => {
	nameForm.value.name = (userInfo.value && userInfo.value.name) || '';
	nameModalVisible.value = true;
};

// 保存姓名
const saveName = async () => {
	const name = (nameForm.value && nameForm.value.name) || '';
	if (!name.trim()) {
		uni.showToast({
			title: '请输入姓名',
			icon: 'none'
		});
		return;
	}

	try {
		const res = await request.put('/profile/info', {
			name: name.trim()
		});

		if (res && res.status_code === 200) {
			if (!userInfo.value) {
				userInfo.value = {};
			}
			userInfo.value.name = name.trim();
			nameModalVisible.value = false;
			uni.showToast({
				title: '姓名更新成功',
				icon: 'success'
			});
		}
	} catch (error) {
		console.error('更新姓名失败:', error);
		uni.showToast({
			title: '更新失败',
			icon: 'none'
		});
	}
};

// 编辑昵称
const editNickname = () => {
	nicknameForm.value.nickname = (userInfo.value && userInfo.value.nickname) || '';
	nicknameModalVisible.value = true;
};

// 保存昵称
const saveNickname = async () => {
	const nickname = (nicknameForm.value && nicknameForm.value.nickname) || '';
	if (!nickname.trim()) {
		uni.showToast({
			title: '请输入昵称',
			icon: 'none'
		});
		return;
	}

	try {
		const res = await request.put('/profile/info', {
			nickname: nickname.trim()
		});

		if (res && res.status_code === 200) {
			if (!userInfo.value) {
				userInfo.value = {};
			}
			userInfo.value.nickname = nickname.trim();
			nicknameModalVisible.value = false;
			uni.showToast({
				title: '昵称更新成功',
				icon: 'success'
			});
		}
	} catch (error) {
		console.error('更新昵称失败:', error);
		uni.showToast({
			title: '更新失败',
			icon: 'none'
		});
	}
};

// 关于我们
const aboutUs = () => {
	uni.navigateTo({
		url: '/pages/profile/agreement?type=about_us'
	});
};

// 隐私政策
const privacyPolicy = () => {
	uni.navigateTo({
		url: '/pages/profile/agreement?type=privacy_policy'
	});
};

// 服务条款
const serviceTerms = () => {
	uni.navigateTo({
		url: '/pages/profile/agreement?type=service_terms'
	});
};

// 联系我们
const contactUs = () => {
	uni.navigateTo({
		url: '/pages/profile/agreement?type=contact_us'
	});
};

// 退出登录
const logout = () => {
	uni.showModal({
		title: '确认退出',
		content: '确定要退出登录吗？',
		success: (res) => {
			if (res.confirm) {
				userStore.logout();
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				});
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/login/index'
					});
				}, 1500);
			}
		}
	});
};
</script>

<style lang="scss" scoped>
.settings {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 50vh;
	padding: 40rpx;

	.loading-text {
		font-size: 32rpx;
		color: #666666;
	}
}

.container {
	padding: 20rpx;
}

.setting-group {
	margin-bottom: 48rpx;
	
	.group-title {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 16rpx;
		margin-left: 16rpx;
		font-weight: 500;
	}
}

.setting-item {
	background: #ffffff;
	padding: 32rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;

	&.avatar-setting {
		padding: 24rpx 40rpx;
	}

	// 单独一个item的样式
	&.single-item {
		border-radius: 16rpx;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
	}

	// 第一个item的样式
	&.first-item {
		border-top-left-radius: 16rpx;
		border-top-right-radius: 16rpx;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
	}

	// 中间item的样式
	&.middle-item {
		border-top: 1rpx solid #f5f5f5;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
	}

	// 最后一个item的样式
	&.last-item {
		border-bottom-left-radius: 16rpx;
		border-bottom-right-radius: 16rpx;
		border-top: 1rpx solid #f5f5f5;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
	}

	.setting-left {
		display: flex;
		align-items: center;
		flex: 1;

		.avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			margin-right: 32rpx;
		}

		.setting-label {
			font-size: 32rpx;
			color: #000000;
			font-weight: 500;
		}
	}

	.setting-right {
		display: flex;
		align-items: center;

		.setting-value {
			font-size: 28rpx;
			color: #666666;
			margin-right: 16rpx;
		}

		.avatar-btn {
			background: transparent;
			border: none;
			padding: 0;
			margin: 0;
			display: flex;
			align-items: center;
			justify-content: center;

			&::after {
				border: none;
			}
		}
	}
}

.logout-section {
	margin-top: 80rpx;
	padding: 0 20rpx;
}

.logout-btn {
	width: 100%;
	height: 88rpx;
	background: #ff4757;
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;

	&:active {
		background: #ff3742;
	}
}

.edit-modal {
	width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	padding: 48rpx 40rpx;

	.modal-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #000000;
		text-align: center;
		margin-bottom: 48rpx;
	}

	.modal-content {
		margin-bottom: 48rpx;
	}

	.modal-actions {
		display: flex;
		justify-content: space-between;
		gap: 32rpx;

		button {
			flex: 1;
			height: 80rpx;
			border: none;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			&::after {
				border: none !important;
			}
		}

		.cancel-btn {
			background: #f5f5f5;
			color: #666666;

			&:active {
				background: #e5e5e5;
			}
		}

		.confirm-btn {
			background: #000000;
			color: #ffffff;

			&:active {
				background: #333333;
			}

			&:disabled {
				background: #cccccc;
				color: #999999;
			}
		}
	}
}
</style> 