<template>
	<view class="agreement">
		<navbar :title="agreementTitle" />
		
		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 协议内容 -->
		<view v-else-if="agreementData" class="content-container">
			<view class="agreement-content">
				<!-- 如果有content字段，显示富文本内容 -->
				<rich-text v-if="agreementData.content" :nodes="agreementData.content"></rich-text>
				<!-- 如果没有content字段但有其他文本字段，显示文本内容 -->
				<view v-else-if="agreementData.description || agreementData.text" class="text-content">
					<text>{{ agreementData.description || agreementData.text }}</text>
				</view>
				<!-- 如果只有基本信息，显示基本信息 -->
				<view v-else class="basic-info">
					<view class="info-item">
						<text class="label">名称：</text>
						<text class="value">{{ agreementData.name || agreementData.title }}</text>
					</view>
					<view v-if="agreementData.created_at" class="info-item">
						<text class="label">创建时间：</text>
						<text class="value">{{ agreementData.created_at }}</text>
					</view>
					<view v-if="agreementData.updated_at" class="info-item">
						<text class="label">更新时间：</text>
						<text class="value">{{ agreementData.updated_at }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view v-else class="empty-container">
			<text class="empty-text">暂无内容</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import navbar from "@/components/navbar/index.vue";
import { onLoad } from "@dcloudio/uni-app";
import request from "@/utils/request";

// 响应式数据
const agreementData = ref(null);
const agreementTitle = ref('协议详情');
const isLoading = ref(true);
const agreementType = ref('');

// 页面加载时获取参数
onLoad((options) => {
	if (options.type) {
		agreementType.value = options.type;
		loadAgreement();
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		});
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	}
});

// 加载协议内容
const loadAgreement = async () => {
	try {
		isLoading.value = true;
		console.log('正在请求协议:', agreementType.value);
		
		const res = await request.get(`/agreement/${agreementType.value}`);
		console.log('接口返回数据:', res);
		
		if (res && res.data) {
			agreementData.value = res.data;
			agreementTitle.value = res.data.title || res.data.name || '协议详情';
			
			console.log('设置协议数据:', agreementData.value);
			console.log('协议标题:', agreementTitle.value);
			
			// 动态设置页面标题
			uni.setNavigationBarTitle({
				title: agreementTitle.value
			});
		} else {
			console.warn('协议数据格式异常:', res);
			uni.showToast({
				title: '获取协议内容失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取协议内容失败:', error);
		uni.showToast({
			title: '获取协议内容失败',
			icon: 'none'
		});
	} finally {
		isLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.agreement {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 50vh;
	padding: 40rpx;

	.loading-text {
		font-size: 32rpx;
		color: #666666;
	}
}

.content-container {
	padding: 20rpx;
}

.agreement-content {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 40rpx;
	margin-bottom: 20rpx;
	box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
	line-height: 1.6;
	
	/* 富文本样式 */
	:deep(.rich-text) {
		font-size: 30rpx;
		color: #333333;
		line-height: 1.8;
		
		h1, h2, h3, h4, h5, h6 {
			font-weight: bold;
			margin: 40rpx 0 20rpx 0;
			color: #000000;
		}
		
		h1 {
			font-size: 40rpx;
		}
		
		h2 {
			font-size: 36rpx;
		}
		
		h3 {
			font-size: 34rpx;
		}
		
		p {
			margin: 20rpx 0;
			text-align: justify;
			text-indent: 2em;
		}
		
		ul, ol {
			padding-left: 40rpx;
			margin: 20rpx 0;
		}
		
		li {
			margin: 10rpx 0;
		}
		
		strong, b {
			font-weight: bold;
			color: #000000;
		}
		
		em, i {
			font-style: italic;
		}
		
		a {
			color: #007aff;
			text-decoration: underline;
		}
		
		img {
			max-width: 100%;
			height: auto;
			margin: 20rpx 0;
			border-radius: 8rpx;
		}
		
		blockquote {
			border-left: 8rpx solid #007aff;
			padding-left: 20rpx;
			margin: 20rpx 0;
			background-color: #f8f9fa;
			padding: 20rpx;
			border-radius: 8rpx;
		}
		
		table {
			width: 100%;
			border-collapse: collapse;
			margin: 20rpx 0;
		}
		
		th, td {
			border: 1px solid #ddd;
			padding: 16rpx;
			text-align: left;
		}
		
		th {
			background-color: #f5f5f5;
			font-weight: bold;
		}
	}
}

.text-content {
	font-size: 30rpx;
	color: #333333;
	line-height: 1.8;
	text-align: justify;
	text-indent: 2em;
}

.basic-info {
	.info-item {
		display: flex;
		margin-bottom: 24rpx;
		align-items: flex-start;
		
		.label {
			font-size: 28rpx;
			color: #666666;
			min-width: 120rpx;
			flex-shrink: 0;
		}
		
		.value {
			font-size: 28rpx;
			color: #333333;
			flex: 1;
			word-break: break-all;
		}
	}
}

.empty-container {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 50vh;
	padding: 40rpx;

	.empty-text {
		font-size: 32rpx;
		color: #999999;
	}
}
</style> 