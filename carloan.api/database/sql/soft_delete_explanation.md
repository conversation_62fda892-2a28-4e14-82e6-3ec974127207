# 审批工作流表软删除设计说明

## 为什么审批工作流表需要软删除？

### 1. **法规合规要求**
- **金融业务特殊性**：车贷业务属于金融行业，需要严格的审批记录保存
- **监管要求**：监管部门要求完整保留审批流程和决策历史
- **法律责任**：审批决策涉及法律责任，删除记录可能导致法律风险

### 2. **业务安全考虑**
- **误删防护**：防止管理员误删重要审批数据
- **数据完整性**：保证审批流程的完整性和可追溯性
- **审计需求**：支持内外部审计对审批流程的检查

### 3. **数据分析价值**
- **历史分析**：分析审批效率、通过率等业务指标
- **流程优化**：基于历史数据优化审批流程
- **风险控制**：分析拒绝原因，完善风控策略

### 4. **技术架构一致性**
- **Laravel 规范**：遵循 Laravel 软删除最佳实践
- **系统一致性**：与项目中其他表的软删除保持一致
- **ORM 支持**：利用 Eloquent ORM 的软删除功能

## 软删除字段设计

### 数据库层面
```sql
-- 所有审批相关表都添加 deleted_at 字段
`deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间'

-- 并创建索引优化查询性能
KEY `xxx_deleted_at_index` (`deleted_at`)
```

### 模型层面
```php
// 使用 Laravel 软删除 Trait
use Illuminate\Database\Eloquent\SoftDeletes;

class ApprovalWorkflow extends BaseModel
{
    use SoftDeletes;
    
    protected $casts = [
        'deleted_at' => 'datetime'
    ];
}
```

### 查询层面
```php
// 默认查询会自动排除软删除记录
ApprovalTask::where('approver_id', $userId)->get();

// 包含软删除记录
ApprovalTask::withTrashed()->get();

// 只查询软删除记录
ApprovalTask::onlyTrashed()->get();

// 恢复软删除记录
$task->restore();

// 永久删除（谨慎使用）
$task->forceDelete();
```

## 业务场景举例

### 场景1：审批任务"删除"
```php
// 管理员取消某个审批任务时，使用软删除
$task = ApprovalTask::find($id);
$task->delete(); // 软删除，数据仍在数据库中

// 后续如需恢复
$task->restore();
```

### 场景2：审批流程配置调整
```php
// 停用某个审批流程配置时
$workflow = ApprovalWorkflow::find($id);
$workflow->delete(); // 软删除，历史数据保留

// 新建类似配置无需担心数据冲突
```

### 场景3：数据分析和审计
```php
// 分析所有历史审批数据（包括已删除的）
$allTasks = ApprovalTask::withTrashed()
    ->whereBetween('created_at', [$startDate, $endDate])
    ->get();

// 审计查看已删除的审批记录
$deletedTasks = ApprovalTask::onlyTrashed()->get();
```

## 性能优化建议

### 1. **查询优化**
- 为 `deleted_at` 字段创建索引
- 在复合索引中包含 `deleted_at` 字段

### 2. **数据清理策略**
```php
// 定期清理N年前的软删除记录（可选）
// 谨慎执行，建议备份后再清理
ApprovalTask::onlyTrashed()
    ->where('deleted_at', '<', Carbon::now()->subYears(7))
    ->forceDelete();
```

### 3. **视图和报表**
- 创建的数据库视图都加入 `WHERE deleted_at IS NULL` 条件
- 报表查询默认排除软删除数据

## 注意事项

1. **外键约束**：软删除的记录仍占用外键关系，需要合理设计
2. **唯一性约束**：软删除记录可能影响唯一性约束，需要特殊处理
3. **存储空间**：软删除会占用更多存储空间
4. **查询性能**：需要合理设计索引和查询条件

通过软删除设计，我们确保了审批工作流数据的安全性、完整性和合规性，同时保持了良好的技术架构。 