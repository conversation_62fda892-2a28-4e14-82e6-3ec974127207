<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FinancialProduct extends BaseModel
{
    use SoftDeletes;

    protected $table = 'financial_products';

    protected $fillable = [
        'product_code',
        'product_name',
        'product_type',
        'annual_rate',
        'min_amount',
        'max_amount',
        'supported_periods',
        'description',
        'features',
        'conditions',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'annual_rate' => 'decimal:4',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'supported_periods' => 'array',
        'features' => 'array',
        'conditions' => 'array',
        'status' => 'integer',
        'sort_order' => 'integer'
    ];

    // 产品类型常量
    const TYPE_LOAN = 'loan';
    const TYPE_LEASE = 'lease';

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 获取启用的产品
     *
     * @param $query
     * @return mixed
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 按排序权重排序
     *
     * @param $query
     * @return mixed
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * 按产品类型筛选
     *
     * @param $query
     * @param $type
     * @return mixed
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('product_type', $type);
    }

    /**
     * 获取支持的期数选项
     *
     * @return array
     */
    public function getSupportedPeriodsAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 获取产品特色
     *
     * @return array
     */
    public function getFeaturesAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 获取申请条件
     *
     * @return array
     */
    public function getConditionsAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 验证贷款金额是否在范围内
     *
     * @param float $amount
     * @return bool
     */
    public function isAmountValid(float $amount): bool
    {
        return $amount >= $this->min_amount && $amount <= $this->max_amount;
    }

    /**
     * 验证期数是否支持
     *
     * @param int $periods
     * @return bool
     */
    public function isPeriodSupported(int $periods): bool
    {
        return in_array($periods, $this->supported_periods);
    }

    /**
     * 获取月利率
     *
     * @return float
     */
    public function getMonthlyRate(): float
    {
        return $this->annual_rate / 12;
    }

    /**
     * 计算等额本息还款
     *
     * @param float $amount 贷款金额
     * @param int $periods 期数
     * @return array
     */
    public function calculateEqualPayment(float $amount, int $periods): array
    {
        $monthlyRate = $this->getMonthlyRate();
        
        // 等额本息计算公式
        $monthlyPayment = ($amount * $monthlyRate * pow(1 + $monthlyRate, $periods)) / 
                         (pow(1 + $monthlyRate, $periods) - 1);

        $totalPayment = $monthlyPayment * $periods;
        $totalInterest = $totalPayment - $amount;

        // 生成还款计划表
        $schedule = [];
        $remainingPrincipal = $amount;

        for ($i = 1; $i <= $periods; $i++) {
            $interestPayment = $remainingPrincipal * $monthlyRate;
            $principalPayment = $monthlyPayment - $interestPayment;
            $remainingPrincipal -= $principalPayment;

            $schedule[] = [
                'period' => $i,
                'monthly_payment' => round($monthlyPayment, 2),
                'principal_payment' => round($principalPayment, 2),
                'interest_payment' => round($interestPayment, 2),
                'remaining_principal' => round(max(0, $remainingPrincipal), 2)
            ];
        }

        return [
            'loan_amount' => $amount,
            'periods' => $periods,
            'annual_rate' => $this->annual_rate,
            'monthly_rate' => round($monthlyRate, 6),
            'monthly_payment' => round($monthlyPayment, 2),
            'total_payment' => round($totalPayment, 2),
            'total_interest' => round($totalInterest, 2),
            'product_name' => $this->product_name,
            'repayment_type' => '等额本息',
            'schedule' => $schedule
        ];
    }

    /**
     * 计算等额本金还款
     *
     * @param float $amount 贷款金额
     * @param int $periods 期数
     * @return array
     */
    public function calculateEqualPrincipal(float $amount, int $periods): array
    {
        $monthlyRate = $this->getMonthlyRate();
        $monthlyPrincipal = $amount / $periods; // 每月还本金
        $totalInterest = 0;
        $schedule = [];

        for ($i = 1; $i <= $periods; $i++) {
            $remainingPrincipal = $amount - ($i - 1) * $monthlyPrincipal;
            $interestPayment = $remainingPrincipal * $monthlyRate;
            $monthlyPayment = $monthlyPrincipal + $interestPayment;
            $totalInterest += $interestPayment;

            $schedule[] = [
                'period' => $i,
                'monthly_payment' => round($monthlyPayment, 2),
                'principal_payment' => round($monthlyPrincipal, 2),
                'interest_payment' => round($interestPayment, 2),
                'remaining_principal' => round($remainingPrincipal - $monthlyPrincipal, 2)
            ];
        }

        $totalPayment = $amount + $totalInterest;

        return [
            'loan_amount' => $amount,
            'periods' => $periods,
            'annual_rate' => $this->annual_rate,
            'monthly_rate' => round($monthlyRate, 6),
            'total_payment' => round($totalPayment, 2),
            'total_interest' => round($totalInterest, 2),
            'product_name' => $this->product_name,
            'repayment_type' => '等额本金',
            'schedule' => $schedule
        ];
    }

    /**
     * 获取所有启用的产品列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActiveProducts()
    {
        return self::enabled()
            ->ordered()
            ->select([
                'id',
                'product_name as name',
                'annual_rate as rate',
                'min_amount',
                'max_amount',
                'supported_periods as periods',
                'description',
                'product_type',
                'features'
            ])
            ->get();
    }
} 