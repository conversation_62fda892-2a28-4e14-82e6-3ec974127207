<?php

namespace App\Http\Controllers\Api\Admin;

use App\Models\PermissionCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PermissionCodeController extends BaseController
{
    /**
     * 获取权限码列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = PermissionCode::query();

        // 按权限组筛选
        if ($request->has('group_name') && $request->group_name) {
            $query->where('group_name', $request->group_name);
        }

        // 按状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('code', 'like', "%{$keyword}%")
                  ->orWhere('name', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }

        // 分页
        $pageSize = $request->get('page_size', 20);
        $permissionCodes = $query->orderBy('group_name', 'asc')
                                 ->orderBy('code', 'asc')
                                 ->paginate($pageSize);

        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $permissionCodes
        ]);
    }

    /**
     * 获取权限码详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $permissionCode = PermissionCode::findOrFail($id);
        
        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $permissionCode
        ]);
    }

    /**
     * 创建权限码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:100|unique:permission_codes,code',
            'name' => 'required|string|max:100',
            'group_name' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:200',
            'frontend_route' => 'nullable|string|max:100',
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $permissionCode = PermissionCode::create([
            'code' => $request->code,
            'name' => $request->name,
            'group_name' => $request->group_name,
            'description' => $request->description,
            'frontend_route' => $request->frontend_route,
            'status' => $request->status
        ]);

        return $this->response->array([
            'code' => 0,
            'message' => '权限码创建成功',
            'data' => $permissionCode
        ]);
    }

    /**
     * 更新权限码
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $permissionCode = PermissionCode::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:100|unique:permission_codes,code,' . $id,
            'name' => 'required|string|max:100',
            'group_name' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:200',
            'frontend_route' => 'nullable|string|max:100',
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $permissionCode->update([
            'code' => $request->code,
            'name' => $request->name,
            'group_name' => $request->group_name,
            'description' => $request->description,
            'frontend_route' => $request->frontend_route,
            'status' => $request->status
        ]);

        return $this->response->array([
            'code' => 0,
            'message' => '权限码更新成功',
            'data' => $permissionCode
        ]);
    }

    /**
     * 删除权限码
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $permissionCode = PermissionCode::findOrFail($id);

        // 检查是否被角色使用
        $roleCount = \App\Models\Role::whereJsonContains('permissions', $permissionCode->code)->count();
        if ($roleCount > 0) {
            return $this->response->array([
                'code' => -1,
                'message' => '该权限码正在被 ' . $roleCount . ' 个角色使用，无法删除'
            ]);
        }

        $permissionCode->delete();

        return $this->response->array([
            'code' => 0,
            'message' => '权限码删除成功'
        ]);
    }

    /**
     * 获取权限组列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groups()
    {
        $groups = PermissionCode::select('group_name')
                               ->where('status', 1)
                               ->whereNotNull('group_name')
                               ->groupBy('group_name')
                               ->orderBy('group_name', 'asc')
                               ->pluck('group_name');

        return $this->response->array([
            'code' => 0,
            'message' => 'success',
            'data' => $groups
        ]);
    }

    /**
     * 启用/禁用权限码
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatus(Request $request, $id)
    {
        $permissionCode = PermissionCode::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $permissionCode->update(['status' => $request->status]);

        $statusText = $request->status ? '启用' : '禁用';
        return $this->response->array([
            'code' => 0,
            'message' => "权限码{$statusText}成功",
            'data' => $permissionCode
        ]);
    }

    /**
     * 批量导入权限码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'permission_codes' => 'required|array',
            'permission_codes.*.code' => 'required|string|max:100',
            'permission_codes.*.name' => 'required|string|max:100',
            'permission_codes.*.group_name' => 'nullable|string|max:50',
            'permission_codes.*.description' => 'nullable|string|max:200',
            'permission_codes.*.frontend_route' => 'nullable|string|max:100'
        ]);

        if ($validator->fails()) {
            return $this->response->array([
                'code' => -1,
                'message' => '参数错误',
                'data' => $validator->errors()
            ]);
        }

        $permissionCodes = $request->permission_codes;
        $successCount = 0;
        $failureCount = 0;
        $errors = [];

        foreach ($permissionCodes as $index => $permissionData) {
            try {
                // 检查权限码是否已存在
                if (PermissionCode::where('code', $permissionData['code'])->exists()) {
                    $errors[] = "第" . ($index + 1) . "条记录：权限码 '{$permissionData['code']}' 已存在";
                    $failureCount++;
                    continue;
                }

                PermissionCode::create([
                    'code' => $permissionData['code'],
                    'name' => $permissionData['name'],
                    'group_name' => $permissionData['group_name'] ?? null,
                    'description' => $permissionData['description'] ?? null,
                    'frontend_route' => $permissionData['frontend_route'] ?? null,
                    'status' => 1
                ]);

                $successCount++;
            } catch (\Exception $e) {
                $errors[] = "第" . ($index + 1) . "条记录导入失败：" . $e->getMessage();
                $failureCount++;
            }
        }

        return $this->response->array([
            'code' => 0,
            'message' => "批量导入完成，成功 {$successCount} 条，失败 {$failureCount} 条",
            'data' => [
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'errors' => $errors
            ]
        ]);
    }
} 