<template>
	<!-- 补充资料 -->
	<view class="supplement-submit">
		<navbar title="补充资料" />
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading" :content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载完成' }" />
		</view>
		
		<!-- 提交表单 -->
		<view v-else-if="supplementInfo" class="submit-content">
			<!-- 驳回信息显示 -->
			<view class="reject-reason-section" v-if="supplementInfo.status === 'rejected' && (supplementInfo.reason || supplementInfo.review_notes)">
				<view class="reject-time">驳回时间：{{ supplementInfo.reviewed_time || supplementInfo.created_time }}</view>
				<view class="reject-reason">驳回原因：{{ supplementInfo.reason || supplementInfo.review_notes }}</view>
			</view>

			<!-- 资料完善状态列表 -->
			<view class="info-modules">
				<view 
					class="module-item" 
					v-for="(module, index) in infoModules" 
					:key="index"
					@click="handleModuleClick(module)"
				>
					<view class="module-name">{{ module.name }}</view>
					<view class="module-right">
						<view class="module-status" :class="module.statusClass">
							{{ module.statusText }}
						</view>
						<view class="module-arrow">
							<wd-icon name="arrow-right" size="18px" color="#cccccc" />
						</view>
					</view>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="notes-section">
				<view class="notes-title">备注</view>
				<view class="notes-content">
					<textarea 
						v-model="customerNotes"
						placeholder="请输入备注内容"
						class="notes-textarea"
						maxlength="500"
					/>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-actions">
				<button 
					class="btn btn-primary" 
					@click="submitSupplement"
					:disabled="!canSubmit || submitting"
				>
					{{ submitting ? '提交中...' : '提交' }}
				</button>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-else class="error-container">
			<view class="error-icon">
				<image src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/error.png" />
			</view>
			<view class="error-text">获取补件信息失败</view>
			<button class="btn btn-primary" @click="loadSupplementInfo">重新加载</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 页面参数
const supplementId = ref('')
const loading = ref(true)
const submitting = ref(false)
const supplementInfo = ref(null)
const customerNotes = ref('')
const applicationData = ref(null)

// 信息模块定义
const infoModules = ref([
	{
		name: '借款信息',
		key: 'loan_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '实名信息',
		key: 'identity_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '基本信息',
		key: 'basic_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '基础资料',
		key: 'basic_data',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '单位信息',
		key: 'company_info',
		statusClass: 'status-incomplete',
		statusText: '信息不全',
		isComplete: false
	},
	{
		name: '直系亲属联系人信息',
		key: 'contact_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '资产信息',
		key: 'asset_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '负债信息',
		key: 'liability_info',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	},
	{
		name: '附件',
		key: 'attachments',
		statusClass: 'status-complete',
		statusText: '已完善',
		isComplete: true
	}
])

// 计算属性
const canSubmit = computed(() => {
	// 检查是否所有模块都已完善
	const allComplete = infoModules.value.every(module => module.isComplete)
	return allComplete && !submitting.value
})

// 页面加载
onMounted(() => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	const options = currentPage.options
	
	if (options.id) {
		supplementId.value = options.id
		loadSupplementInfo()
	} else {
		loading.value = false
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
	}
})

// 加载补件信息
const loadSupplementInfo = async () => {
	try {
		loading.value = true
		const response = await request.get(`/supplements/${supplementId.value}`)
		
		if (response && response.status_code === 200) {
			supplementInfo.value = response.data
			applicationData.value = response.data.application
			console.log('Supplement info loaded:', response.data)
			
			// 如果有客户备注，显示它们
			if (supplementInfo.value.customer_notes) {
				customerNotes.value = supplementInfo.value.customer_notes
			}
			
			// 根据业务申请数据更新模块状态
			updateModuleStatus()
		} else {
			console.error('获取补件信息失败:', response)
			uni.showToast({
				title: response?.message || '获取信息失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('获取补件信息失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 更新模块状态
const updateModuleStatus = () => {
	if (!applicationData.value) return
	
	const app = applicationData.value
	
	// 借款信息 - 检查贷款金额等
	const loanComplete = !!(app.loan_amount && app.loan_period && app.product_name)
	updateSingleModuleStatus('loan_info', loanComplete)
	
	// 实名信息 - 检查身份证信息
	const identityComplete = !!(app.customer_id_card && app.customer_name)
	updateSingleModuleStatus('identity_info', identityComplete)
	
	// 基本信息 - 检查客户基本信息
	const basicComplete = !!(app.customer_name && app.customer_phone)
	updateSingleModuleStatus('basic_info', basicComplete)
	
	// 基础资料 - 检查customer_data
	const basicDataComplete = !!(app.customer_data && 
		app.customer_data.name && 
		app.customer_data.id_number &&
		app.customer_data.frontImage && 
		app.customer_data.backImage)
	updateSingleModuleStatus('basic_data', basicDataComplete)
	
	// 单位信息 - 检查工作单位信息 (这个通常是需要补充的)
	const companyComplete = !!(app.customer_data && 
		app.customer_data.company_name && 
		app.customer_data.work_type)
	updateSingleModuleStatus('company_info', companyComplete)
	
	// 联系人信息 - 检查contacts
	const contactComplete = !!(app.contacts && app.contacts.length > 0)
	updateSingleModuleStatus('contact_info', contactComplete)
	
	// 资产信息 - 检查assets
	const assetComplete = !!(app.assets && app.assets.monthly_income)
	updateSingleModuleStatus('asset_info', assetComplete)
	
	// 负债信息 - 检查liabilities
	const liabilityComplete = !!(app.liabilities)
	updateSingleModuleStatus('liability_info', liabilityComplete)
	
	// 附件 - 检查attachments
	const attachmentComplete = !!(app.attachments && app.attachments.length > 0)
	updateSingleModuleStatus('attachments', attachmentComplete)
}

// 更新单个模块状态
const updateSingleModuleStatus = (moduleKey, isComplete) => {
	const module = infoModules.value.find(m => m.key === moduleKey)
	if (module) {
		module.isComplete = isComplete
		module.statusClass = isComplete ? 'status-complete' : 'status-incomplete'
		module.statusText = isComplete ? '已完善' : '信息不全'
	}
}

// 处理模块点击
const handleModuleClick = (module) => {
	if (module.isComplete) {
		uni.showToast({
			title: '该模块信息已完善',
			icon: 'none'
		})
		return
	}
	
	// 根据模块类型跳转到对应页面
	switch (module.key) {
		case 'basic_data':
			uni.navigateTo({
				url: '/pages/business/basic'
			})
			break
		case 'company_info':
			// 跳转到单位信息编辑页面（如果有的话）
			uni.showToast({
				title: '请完善单位信息',
				icon: 'none'
			})
			break
		case 'contact_info':
			uni.navigateTo({
				url: '/pages/business/contact'
			})
			break
		case 'asset_info':
			uni.navigateTo({
				url: '/pages/business/assets'
			})
			break
		case 'liability_info':
			uni.navigateTo({
				url: '/pages/business/liabilities'
			})
			break
		case 'attachments':
			uni.navigateTo({
				url: '/pages/business/attachments'
			})
			break
		default:
			uni.showToast({
				title: '请完善该模块信息',
				icon: 'none'
			})
	}
}

// 提交补件
const submitSupplement = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请先完善所有必需信息',
			icon: 'none'
		})
		return
	}
	
	try {
		submitting.value = true
		uni.showLoading({ title: '提交中...' })
		
		const params = {
			notes: customerNotes.value.trim() || null,
			module_status: infoModules.value.map(module => ({
				key: module.key,
				name: module.name,
				is_complete: module.isComplete,
				status_text: module.statusText
			}))
		}
		
		const response = await request.post(`/supplements/${supplementId.value}/submit`, params)
		
		uni.hideLoading()
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			})
			
			// 返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			uni.showToast({
				title: response.message || '提交失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('提交补件失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	} finally {
		submitting.value = false
	}
}
</script>

<style lang="scss" scoped>
.supplement-submit {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading-container, .error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 20rpx;
	
	.error-icon {
		margin-bottom: 20rpx;
		
		image {
			width: 120rpx;
			height: 120rpx;
		}
	}
	
	.error-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
	}
}

.submit-content {
	padding: 0;
}

.reject-reason-section {
	background: #fff2f0;
	border-left: 6rpx solid #ff4d4f;
	margin: 0;
	padding: 24rpx 30rpx;
	
	.reject-time {
		font-size: 26rpx;
		color: #ff4d4f;
		margin-bottom: 8rpx;
		font-weight: 500;
	}
	
	.reject-reason {
		font-size: 26rpx;
		color: #ff4d4f;
		line-height: 1.5;
	}
}

.info-modules {
	background: #fff;
	margin: 20rpx 24rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.module-item {
	display: flex;
	align-items: center;
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
	transition: background-color 0.2s ease;
	
	&:last-child {
		border-bottom: none;
	}
	
	&:active {
		background: #f8f9fa;
	}
	
	.module-name {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		font-weight: 400;
		line-height: 1.2;
	}
	
	.module-right {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}
	
	.module-status {
		font-size: 26rpx;
		font-weight: 500;
		
		&.status-complete {
			color: #52c41a;
		}
		
		&.status-incomplete {
			color: #ff4d4f;
		}
	}
	
	.module-arrow {
		display: flex;
		align-items: center;
	}
}

.notes-section {
	background: #fff;
	margin: 20rpx 24rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	
	.notes-title {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 20rpx;
		font-weight: 500;
	}
	
	.notes-content {
		.notes-textarea {
			width: 100%;
			min-height: 160rpx;
			padding: 20rpx;
			border: 1rpx solid #e8e8e8;
			border-radius: 12rpx;
			font-size: 28rpx;
			color: #333;
			background: #fafafa;
			resize: none;
			box-sizing: border-box;
			line-height: 1.5;
			
			&:focus {
				border-color: #1890ff;
				background: #fff;
				outline: none;
			}
		}
	}
}

.submit-actions {
	padding: 24rpx;
	background: transparent;
}

.btn {
	/* 重置按钮默认样式 */
	appearance: none;
	-webkit-appearance: none;
	outline: none;
	box-sizing: border-box;
	
	/* 统一尺寸和布局 */
	width: 100%;
	height: 88rpx;
	border-radius: 8rpx;
	border: none;
	
	/* 统一文字样式 */
	font-size: 32rpx;
	font-weight: 500;
	line-height: 1;
	text-align: center;
	white-space: nowrap;
	
	/* 统一交互效果 */
	cursor: pointer;
	transition: all 0.2s ease;
	
	&:active {
		transform: scale(0.98);
		opacity: 0.8;
	}
	
	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
		transform: none;
	}
	
	&.btn-primary {
		background: #1a1a1a;
		color: #fff;
		border-radius: 8rpx;
		
		&:disabled {
			background: #d9d9d9;
			color: #999;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f5f5f5;
}
</style> 