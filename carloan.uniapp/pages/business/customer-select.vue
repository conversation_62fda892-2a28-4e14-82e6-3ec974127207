<template>
	<view class="customer-select">
		
		<z-paging 
			ref="paging" 
			v-model="customerList" 
			@query="queryList"
			:refresher-enabled="true"
			:loading-more-enabled="true"
			:empty-view-text="'暂无客户数据'"
			:empty-view-img="'https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-empty.png'"
			:paging-style="{ background: '#f3f3f3' }"
		>
            <template #top>
                <navbar title="选择客户" />
            </template>

			<view 
				v-for="item in customerList" 
				:key="item.id" 
				class="customer-item"
				@click="handleSelectCustomer(item)"
			>
				<!-- 左侧 checkbox -->
				<view class="checkbox-column">
					<view class="checkbox-box" :class="{ checked: selectedCustomerId === item.id }">
						<view class="checkbox-dot" v-if="selectedCustomerId === item.id"></view>
					</view>
				</view>
				
				<!-- 右侧卡片 -->
				<view class="customer-card" :class="{ selected: selectedCustomerId === item.id }">
					<view class="card-header">
						<text class="title">客户信息</text>
						<view class="status-info">
							<text class="status" :class="getStatusClass(item.risk_status)">
								{{ getStatusText(item.risk_status, item.risk_score) }}
							</text>
						</view>
					</view>

					<view class="customer-info">
						<view class="info-item">
							<text class="label">客户姓名：</text>
							<text class="value">{{ item.name || '未填写' }}</text>
							<view class="phone-icon" v-if="item.phone" @click.stop="handleCallPhone(item.phone)">
								<text class="phone-emoji">📞</text>
							</view>
						</view>
						<view class="info-item">
							<text class="label">身份证号码：</text>
							<text class="value">{{ item.id_card || '未填写' }}</text>
						</view>
						<view class="info-item" v-if="item.latest_query_time">
							<text class="label">查询时间：</text>
							<text class="value">{{ formatDateTime(item.latest_query_time) }}</text>
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onShow } from '@dcloudio/uni-app';
import request from "@/utils/request";
import navbar from "@/components/navbar/index.vue";

const customerList = ref([]);
const paging = ref(null);
const fromPage = ref('');
const selectedCustomerId = ref(null);

onLoad((options) => {
	if (options.from) {
		fromPage.value = options.from;
	}
	// 页面加载时也尝试加载已选择的客户
	loadSelectedCustomer();
});

const queryList = async (pageNo, pageSize) => {
	try {
		const params = {
			page: pageNo,
			page_size: pageSize,
		};
		
		const response = await request.get("/customers", params);
		
		let isSuccess = false;
		let dataList = [];
		
		if (response && response.status_code === 200) {
			isSuccess = true;
			dataList = response.data?.list || [];
		}
		
		if (isSuccess) {
			const list = Array.isArray(dataList) ? dataList : [];
			
			if (paging.value) {
				paging.value.complete(list);
			}
			
			// 设置已选择的客户
			loadSelectedCustomer();
		} else {
			if (paging.value) {
				paging.value.complete(false);
			}
			const errorMessage = response?.message || "获取客户列表失败";
			uni.showToast({
				title: errorMessage,
				icon: "none",
			});
		}
	} catch (error) {
		if (paging.value) {
			paging.value.complete(false);
		}
		uni.showToast({
			title: error.message || "网络错误，请重试",
			icon: "none",
		});
	}
};

const handleSelectCustomer = (customer) => {
	// 设置选中状态
	selectedCustomerId.value = customer.id;
	
	uni.showModal({
		title: '确认选择',
		content: `确定选择客户：${customer.name || '未命名'}？`,
		success: (res) => {
			if (res.confirm) {
				try {
					// 保存选择的客户到业务数据
					const businessData = uni.getStorageSync('business_draft') || {};
					businessData.customer_id = customer.id;
					businessData.customer_name = customer.name;
					businessData.customer_phone = customer.phone;
					businessData.customer_id_card = customer.id_card;
					businessData.customer_completed = true;
					
					// 如果是车辆评估模式，设置评估数据状态
					if (businessData.page_mode === 'vehicle_assessment') {
						if (!businessData.assessment_data) {
							businessData.assessment_data = {};
						}
						businessData.assessment_data.customer_info_completed = true;
					}
					
					uni.setStorageSync('business_draft', businessData);
					
					uni.showToast({
						title: `已选择：${customer.name || '客户'}`,
						icon: 'success'
					});
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1000);
					
				} catch (error) {
					console.error('保存客户选择失败:', error);
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				}
			} else {
				// 取消选择时清除选中状态
				selectedCustomerId.value = null;
			}
		}
	});
};

// 加载已选择的客户
const loadSelectedCustomer = () => {
	try {
		const businessData = uni.getStorageSync('business_draft');
		if (businessData && businessData.customer_id) {
			// 先设置选中的客户ID
			selectedCustomerId.value = businessData.customer_id;
			
			// 如果客户列表已加载，查找对应的客户信息
			if (customerList.value.length > 0) {
				const customer = customerList.value.find(c => c.id === businessData.customer_id);
				if (customer) {
					console.log('已设置默认选中客户:', customer.name);
				}
			}
		}
	} catch (error) {
		console.error('加载已选客户失败:', error);
	}
};

// 获取状态样式类
const getStatusClass = (riskStatus) => {
	switch (riskStatus) {
		case 'querying':
			return 'querying';
		case 'completed':
			return 'completed';
		case 'failed':
			return 'failed';
		default:
			return 'unknown';
	}
};

// 获取状态文本
const getStatusText = (riskStatus, riskScore) => {
	switch (riskStatus) {
		case 'querying':
			return '查询中';
		case 'completed':
			return riskScore ? `${riskScore}分` : '已完成';
		case 'failed':
			return '查询失败';
		default:
			return '未查询';
	}
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
	// 处理空值、空数组、undefined等情况
	if (!dateTime || Array.isArray(dateTime) || dateTime === 'null' || dateTime === 'undefined') {
		return '';
	}
	
	try {
		const date = new Date(dateTime);
		// 检查日期是否有效
		if (isNaN(date.getTime())) {
			return '';
		}
		
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		const seconds = String(date.getSeconds()).padStart(2, '0');
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	} catch (error) {
		console.error('日期格式化错误:', error, '原始数据:', dateTime);
		return '';
	}
};

// 拨打电话
const handleCallPhone = (phone) => {
	if (!phone) return;
	uni.makePhoneCall({
		phoneNumber: phone,
		fail: () => {
			uni.showToast({
				title: '拨打电话失败',
				icon: 'none'
			});
		}
	});
};

onShow(() => {
	if (paging.value) {
		paging.value.reload();
	}
});
</script>

<style lang="scss" scoped>
.customer-select {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);

	.customer-item {
		margin: 48rpx 40rpx 0 40rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;
		
		&:active {
			opacity: 0.8;
		}
	}
	
	.checkbox-column {
		flex-shrink: 0;
		
		.checkbox-box {
			width: 44rpx;
			height: 44rpx;
			border-radius: 50%;
			border: 3rpx solid #ddd;
			background: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			
			&.checked {
				border: 3rpx solid #000000;
				background: #000000;
			}
			
			.checkbox-dot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: #ffffff;
			}
		}
	}

	.customer-card {
		flex: 1;
		background: #ffffff;
		border-radius: 16rpx;
		padding: 32rpx 24rpx;
		position: relative;
		
		&.selected {
			border: 2rpx solid #000000;
		}
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 32rpx;
		margin-bottom: 12rpx;
		border-bottom: 1rpx solid #ededed;

		.title {
			font-weight: 500;
			font-size: 30rpx;
			color: #000000;
		}
	}

	.status-info {
		display: flex;
		align-items: center;

		.status {
			font-weight: 400;
			font-size: 24rpx;

			&.querying {
				color: #4793ea;
			}
			
			&.completed {
				color: #e02626;
			}
			
			&.failed {
				color: #999999;
			}
			
			&.unknown {
				color: #999999;
			}
		}
	}

	.customer-info {
		.info-item {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			position: relative;

			.label {
				font-weight: 400;
				font-size: 28rpx;
				color: #999999;
			}

			.value {
				font-weight: 400;
				font-size: 28rpx;
				color: #000000;
				flex: 1;
			}
			
			.phone-icon {
				margin-left: 16rpx;
				width: 32rpx;
				height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.phone-emoji {
					font-size: 28rpx;
					color: #4793ea;
				}
			}
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 