<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ThirdPartyApiLogService;
use App\Models\ThirdPartyApiLog;

class ThirdPartyApiStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:stats 
                           {service? : 服务名称(esign, jizhengyun)}
                           {--start= : 开始日期(Y-m-d)}
                           {--end= : 结束日期(Y-m-d)}
                           {--failed : 只显示失败的调用}
                           {--limit=10 : 显示记录数}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查看第三方接口调用统计';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $service = $this->argument('service');
        $start = $this->option('start');
        $end = $this->option('end');
        $onlyFailed = $this->option('failed');
        $limit = (int)$this->option('limit');

        if (!$service) {
            $this->showAllServices($start, $end);
        } else {
            $this->showServiceDetails($service, $start, $end, $onlyFailed, $limit);
        }

        return 0;
    }

    /**
     * 显示所有服务的统计
     */
    private function showAllServices($start, $end)
    {
        $this->info('=== 第三方接口调用统计 ===');
        
        if ($start && $end) {
            $this->info("时间范围: {$start} ~ {$end}");
        }
        
        $this->line('');

        // 获取所有服务
        $services = ThirdPartyApiLog::select('service_name')
            ->groupBy('service_name')
            ->pluck('service_name');

        $headers = ['服务名称', '总调用次数', '成功次数', '失败次数', '成功率'];
        $rows = [];

        foreach ($services as $serviceName) {
            $stats = ThirdPartyApiLogService::getServiceStats($serviceName, $start, $end);
            $rows[] = [
                $stats['service_name'],
                $stats['total_calls'],
                $stats['success_calls'],
                $stats['failed_calls'],
                $stats['success_rate']
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * 显示指定服务的详细信息
     */
    private function showServiceDetails($service, $start, $end, $onlyFailed, $limit)
    {
        $this->info("=== {$service} 服务详细统计 ===");
        
        // 显示基本统计
        $stats = ThirdPartyApiLogService::getServiceStats($service, $start, $end);
        $this->info("总调用次数: {$stats['total_calls']}");
        $this->info("成功次数: {$stats['success_calls']}");
        $this->info("失败次数: {$stats['failed_calls']}");
        $this->info("成功率: {$stats['success_rate']}");
        $this->line('');

        // 显示接口调用详情
        $query = ThirdPartyApiLog::byService($service);
        
        if ($start && $end) {
            $query->byTimeRange($start, $end);
        }
        
        if ($onlyFailed) {
            $query->bySuccess(false);
            $this->info('=== 失败的调用记录 ===');
        } else {
            $this->info('=== 最近的调用记录 ===');
        }

        $logs = $query->orderBy('request_time', 'desc')
            ->limit($limit)
            ->get();

        if ($logs->isEmpty()) {
            $this->warn('没有找到相关记录');
            return;
        }

        $headers = ['时间', '接口名称', '耗时(ms)', '状态', '错误信息'];
        $rows = [];

        foreach ($logs as $log) {
            $status = $log->is_success ? '成功' : '失败';
            $errorMsg = $log->error_message ? substr($log->error_message, 0, 50) : '-';
            
            $rows[] = [
                $log->request_time->format('Y-m-d H:i:s'),
                $log->api_name,
                $log->duration_ms ?? '-',
                $status,
                $errorMsg
            ];
        }

        $this->table($headers, $rows);

        // 如果有失败记录，提供更详细的错误信息
        if ($onlyFailed && $logs->isNotEmpty()) {
            $this->line('');
            $this->info('=== 详细错误信息 ===');
            foreach ($logs as $log) {
                if (!$log->is_success) {
                    $this->line("时间: {$log->request_time->format('Y-m-d H:i:s')}");
                    $this->line("接口: {$log->api_name}");
                    $this->line("错误: {$log->error_message}");
                    $this->line("---");
                }
            }
        }
    }
} 