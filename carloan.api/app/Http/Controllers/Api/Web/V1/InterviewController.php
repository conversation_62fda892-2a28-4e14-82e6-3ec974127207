<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\BusinessApplication;
use App\Models\InterviewAppointment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InterviewController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 获取面审列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 10);

            // 查询待面审和已预约面审的业务申请
            $query = BusinessApplication::with(['user', 'currentInterviewAppointment'])
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_INTERVIEW_PENDING,
                    BusinessApplication::STATUS_INTERVIEW_SCHEDULED
                ])
                ->orderBy('submit_time', 'desc');

            // 分页查询
            $applications = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $data = [
                'list' => $applications->items() ? array_map(function ($application) {
                    return $this->formatInterviewData($application);
                }, $applications->items()) : [],
                'pagination' => [
                    'total' => $applications->total(),
                    'per_page' => $applications->perPage(),
                    'current_page' => $applications->currentPage(),
                    'last_page' => $applications->lastPage(),
                    'has_more' => $applications->hasMorePages()
                ]
            ];

            return $this->successHandler('获取面审列表成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取面审列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取面审列表失败', 500);
        }
    }

    /**
     * 获取面审详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::with(['user', 'currentInterviewAppointment'])
                ->where('id', $id)
                ->forUser($user->id)
                ->whereIn('status', [
                    BusinessApplication::STATUS_INTERVIEW_PENDING,
                    BusinessApplication::STATUS_INTERVIEW_SCHEDULED
                ])
                ->first();

            if (!$application) {
                return $this->errorHandler('面审记录不存在', 404);
            }

            $data = $this->formatInterviewData($application, true);

            return $this->successHandler('获取面审详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取面审详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取面审详情失败', 500);
        }
    }

    /**
     * 预约面审
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function schedule(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'appointment_time' => 'required|date|after:now',
                'appointment_location' => 'required|string|max:200',
                'appointment_notes' => 'nullable|string|max:500'
            ], [
                'appointment_time.required' => '请选择预约时间',
                'appointment_time.date' => '预约时间格式错误',
                'appointment_time.after' => '预约时间不能早于当前时间',
                'appointment_location.required' => '请输入面审地点',
                'appointment_location.max' => '面审地点不能超过200个字符',
                'appointment_notes.max' => '预约备注不能超过500个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->where('status', BusinessApplication::STATUS_INTERVIEW_PENDING)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            DB::beginTransaction();

            // 检查是否已存在预约记录
            $existingAppointment = InterviewAppointment::where('application_id', $application->id)
                ->whereIn('status', [InterviewAppointment::STATUS_PENDING, InterviewAppointment::STATUS_SCHEDULED])
                ->first();

            if ($existingAppointment) {
                // 更新现有预约
                $existingAppointment->scheduleInterview(
                    $request->input('appointment_time'),
                    $request->input('appointment_location'),
                    $request->input('appointment_notes')
                );
            } else {
                // 创建新的预约记录
                $appointment = InterviewAppointment::create([
                    'application_id' => $application->id,
                    'user_id' => $user->id,
                    'appointment_no' => InterviewAppointment::generateAppointmentNo(),
                    'status' => InterviewAppointment::STATUS_PENDING,
                    'created_time' => Carbon::now()
                ]);

                $appointment->scheduleInterview(
                    $request->input('appointment_time'),
                    $request->input('appointment_location'),
                    $request->input('appointment_notes')
                );
            }

            DB::commit();

            return $this->successHandler('预约面审成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('预约面审失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('预约面审失败', 500);
        }
    }

    /**
     * 完成面审
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function complete(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'interview_result' => 'required|boolean',
                'interview_notes' => 'nullable|string|max:1000',
                'interview_materials' => 'nullable|array'
            ], [
                'interview_result.required' => '请选择面审结果',
                'interview_result.boolean' => '面审结果格式错误',
                'interview_notes.max' => '面审备注不能超过1000个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->where('status', BusinessApplication::STATUS_INTERVIEW_SCHEDULED)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            $appointment = InterviewAppointment::where('application_id', $application->id)
                ->where('status', InterviewAppointment::STATUS_SCHEDULED)
                ->first();

            if (!$appointment) {
                return $this->errorHandler('预约记录不存在', 404);
            }

            if (!$appointment->canComplete()) {
                return $this->errorHandler('当前状态不允许完成面审', 400);
            }

            DB::beginTransaction();

            // 完成面审
            $appointment->completeInterview(
                $request->input('interview_result'),
                $request->input('interview_notes'),
                $request->input('interview_materials')
            );

            // 根据面审结果决定下一步流程
            $interviewResult = $request->input('interview_result');
            if ($interviewResult) {
                // 面审通过，自动进入终审
                $this->autoAdvanceToFinalReview($application);
            }

            DB::commit();

            $message = $interviewResult ? '面审完成，已进入终审阶段' : '面审已标记为不通过';
            return $this->successHandler($message, 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('完成面审失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('完成面审失败', 500);
        }
    }

    /**
     * 自动进入终审阶段
     */
    private function autoAdvanceToFinalReview(BusinessApplication $application)
    {
        $application->update([
            'status' => BusinessApplication::STATUS_FINAL_REVIEW,
            'status_text' => '终审中'
        ]);

        // 记录审批历史
        $approvalHistory = $application->approval_history ?: [];
        $approvalHistory[] = [
            'stage' => 'interview_completed',
            'status' => 'completed',
            'operator_id' => Auth::id(),
            'operator_name' => Auth::user()->name,
            'notes' => '面审完成，自动进入终审',
            'created_at' => Carbon::now()->toDateTimeString()
        ];
        $application->update(['approval_history' => $approvalHistory]);
    }

    /**
     * 取消预约
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'cancel_reason' => 'required|string|max:200'
            ], [
                'cancel_reason.required' => '请输入取消原因',
                'cancel_reason.max' => '取消原因不能超过200个字符'
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 422);
            }

            $application = BusinessApplication::where('id', $id)
                ->forUser($user->id)
                ->where('status', BusinessApplication::STATUS_INTERVIEW_SCHEDULED)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务申请不存在或状态不正确', 404);
            }

            $appointment = InterviewAppointment::where('application_id', $application->id)
                ->where('status', InterviewAppointment::STATUS_SCHEDULED)
                ->first();

            if (!$appointment) {
                return $this->errorHandler('预约记录不存在', 404);
            }

            if (!$appointment->canCancel()) {
                return $this->errorHandler('当前状态不允许取消预约', 400);
            }

            DB::beginTransaction();

            $appointment->cancelAppointment($request->input('cancel_reason'));

            DB::commit();

            return $this->successHandler('取消预约成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('取消预约失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('取消预约失败', 500);
        }
    }

    /**
     * 格式化面审数据
     * 
     * @param BusinessApplication $application
     * @param bool $detail
     * @return array
     */
    private function formatInterviewData(BusinessApplication $application, $detail = false)
    {
        $appointment = $application->currentInterviewAppointment;
        
        $data = [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'customer_name' => $application->customer_name,
            'customer_phone' => $application->customer_phone,
            'product_name' => $application->product_name,
            'loan_amount' => $application->loan_amount,
            'status' => $application->status,
            'status_text' => $application->status_text,
            'submit_time' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : null,
            'created_time' => $application->created_at->format('Y-m-d H:i:s'),
            
            // 面审预约信息
            'appointment' => $appointment ? [
                'id' => $appointment->id,
                'appointment_no' => $appointment->appointment_no,
                'status' => $appointment->status,
                'status_text' => $appointment->status_text,
                'appointment_time' => $appointment->appointment_time ? $appointment->appointment_time->format('Y-m-d H:i:s') : null,
                'appointment_location' => $appointment->appointment_location,
                'appointment_notes' => $appointment->appointment_notes,
                'scheduled_time' => $appointment->scheduled_time ? $appointment->scheduled_time->format('Y-m-d H:i:s') : null,
                'created_time' => $appointment->created_time ? $appointment->created_time->format('Y-m-d H:i:s') : null,
            ] : null,
            
            // 操作权限
            'can_schedule' => $application->status === BusinessApplication::STATUS_INTERVIEW_PENDING,
            'can_cancel' => $application->status === BusinessApplication::STATUS_INTERVIEW_SCHEDULED && $appointment && $appointment->canCancel()
        ];

        if ($detail) {
            $data = array_merge($data, [
                'customer_id_card' => $application->customer_id_card,
                'vehicle_brand' => $application->vehicle_brand,
                'vehicle_model' => $application->vehicle_model,
                'vehicle_vin' => $application->vehicle_vin,
                'vehicle_year' => $application->vehicle_year,
                'vehicle_price' => $application->vehicle_price,
                'loan_period' => $application->loan_period,
                'interest_rate' => $application->interest_rate,
                'approval_notes' => $application->approval_notes,
                'approval_history' => $application->approval_history,
                'customer_data' => $application->customer_data,
                'risk_assessment' => $application->risk_assessment,
                'attachments' => $application->attachments
            ]);

            if ($appointment) {
                $data['appointment'] = array_merge($data['appointment'], [
                    'operation_log' => $appointment->operation_log
                ]);
            }
        }

        return $data;
    }
} 