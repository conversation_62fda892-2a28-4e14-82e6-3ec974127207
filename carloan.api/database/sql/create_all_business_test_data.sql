-- 全部业务功能测试数据
-- 创建时间: 2024-03-16
-- 说明: 包含各个状态的业务申请测试数据，覆盖完整的业务流程

-- 1. 清理现有测试数据（可选，谨慎使用）
-- DELETE FROM `interview_appointments` WHERE `application_id` IN (SELECT id FROM `business_applications` WHERE `application_no` LIKE 'APP202403%');
-- DELETE FROM `customer_supplements` WHERE `application_id` IN (SELECT id FROM `business_applications` WHERE `application_no` LIKE 'APP202403%');
-- DELETE FROM `business_applications` WHERE `application_no` LIKE 'APP202403%';

-- 2. 确保用户数据存在
INSERT IGNORE INTO `users` (
  `id`, `phone`, `nickname`, `name`, `channel_code`, `status`, 
  `register_time`, `login_time`, `created_at`, `updated_at`
) VALUES 
(1, '***********', '张三业务员', '张三', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW()),
(2, '***********', '李四业务员', '李四', 'DIRECT', 1, NOW(), NOW(), NOW(), NOW()),
(3, '***********', '王五业务员', '王五', 'PARTNER', 1, NOW(), NOW(), NOW(), NOW());

-- 3. 确保渠道数据存在
INSERT IGNORE INTO `channels` (
  `code`, `name`, `status`, `sort`, `created_at`, `updated_at`
) VALUES 
('DIRECT', '直营渠道', 1, 1, NOW(), NOW()),
('PARTNER', '合作伙伴', 1, 2, NOW(), NOW());

-- 4. 插入全部业务测试数据
INSERT INTO `business_applications` (
  `application_no`, `user_id`, `channel_code`, `customer_name`, `customer_phone`, `customer_id_card`,
  `product_name`, `loan_amount`, `loan_period`, `interest_rate`, `vehicle_brand`, `vehicle_model`,
  `vehicle_vin`, `vehicle_year`, `vehicle_price`, `status`, `status_text`, `approval_notes`,
  `approval_history`, `submit_time`, `approval_time`, `need_supplement`, `customer_data`, 
  `risk_assessment`, `created_at`, `updated_at`
) VALUES 

-- 已提交状态
(
  'APP20240316200001', 1, 'DIRECT', '陈一', '***********', '110101199101011001',
  '24年以租代售计划', 120000.00, 24, 0.0850, '比亚迪', '秦PLUS',
  'LGXC16EV8PN123456', 2023, 150000.00, 'submitted', '已提交', '客户已提交申请，等待初审',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 HOUR), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 8 HOUR), NULL, 0,
  JSON_OBJECT('income', 7000, 'work_years', 3, 'education', '本科'),
  JSON_OBJECT('score', 82, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 8 HOUR), DATE_SUB(NOW(), INTERVAL 8 HOUR)
),

-- 初审中状态
(
  'APP20240316200002', 1, 'DIRECT', '陈二', '13800138012', '110101199202021002',
  '36年融资租赁计划', 180000.00, 36, 0.0900, '小鹏', 'P7',
  'LXPD3E2M8KM123456', 2024, 220000.00, 'initial_review', '初审中', '系统自动初审通过，人工复核中',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 HOUR), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'processing', 'operator_id', 2, 'operator_name', '系统', 'notes', '进入初审流程', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 HOUR), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 6 HOUR), NULL, 0,
  JSON_OBJECT('income', 10000, 'work_years', 5, 'education', '硕士'),
  JSON_OBJECT('score', 88, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 5 HOUR)
),

-- 待面审状态
(
  'APP20240316200003', 1, 'DIRECT', '陈三', '13800138013', '110101199303031003',
  '12年短期贷款', 90000.00, 12, 0.0750, '理想', 'ONE',
  'LXYU3A1N5KM123456', 2023, 120000.00, 'interview_pending', '待面审', '初审通过，等待客户预约面审',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 2 DAY), NULL, 0,
  JSON_OBJECT('income', 8500, 'work_years', 4, 'education', '本科'),
  JSON_OBJECT('score', 85, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 已预约面审状态
(
  'APP20240316200004', 1, 'DIRECT', '陈四', '13800138014', '110101199404041004',
  '18年中期贷款', 160000.00, 18, 0.0800, '蔚来', 'ES6',
  'LDXU2B1M9KM123456', 2024, 200000.00, 'interview_scheduled', '已预约面审', '客户已预约面审，等待面审完成',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_scheduled', 'status', 'scheduled', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户已预约面审', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 3 DAY), NULL, 0,
  JSON_OBJECT('income', 9500, 'work_years', 6, 'education', '本科'),
  JSON_OBJECT('score', 87, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 终审中状态
(
  'APP20240316200005', 1, 'DIRECT', '陈五', '13800138015', '110101199505051005',
  '24年以租代售计划', 140000.00, 24, 0.0850, '特斯拉', 'Model 3',
  '5YJ3E1EB8KF123456', 2023, 180000.00, 'final_review', '终审中', '面审已完成，进入终审流程',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'processing', 'operator_id', 3, 'operator_name', '终审员', 'notes', '进入终审', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 5 DAY), NULL, 0,
  JSON_OBJECT('income', 11000, 'work_years', 7, 'education', '硕士'),
  JSON_OBJECT('score', 90, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 复审中状态
(
  'APP20240316200006', 1, 'DIRECT', '陈六', '13800138016', '110101199606061006',
  '30年超长期贷款', 220000.00, 30, 0.0880, '奥迪', 'e-tron',
  'WAUZZZGE1KB123456', 2024, 280000.00, 'secondary_review', '复审中', '终审通过，进入复审流程',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'processing', 'operator_id', 4, 'operator_name', '复审员', 'notes', '进入复审', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 6 DAY), NULL, 0,
  JSON_OBJECT('income', 13000, 'work_years', 8, 'education', '硕士'),
  JSON_OBJECT('score', 92, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 待签约状态
(
  'APP20240316200007', 1, 'DIRECT', '陈七', '13800138017', '110101199707071007',
  '24年以租代售计划', 130000.00, 24, 0.0850, '宝马', 'iX3',
  'WBAXG5C30MD123456', 2023, 170000.00, 'contract_pending', '待签约', '复审通过，可以发起签约',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), 0,
  JSON_OBJECT('income', 9800, 'work_years', 5, 'education', '本科'),
  JSON_OBJECT('score', 86, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)
),

-- 签约中状态
(
  'APP20240316200008', 1, 'DIRECT', '陈八', '13800138018', '110101199808081008',
  '36年融资租赁计划', 250000.00, 36, 0.0900, '奔驰', 'EQC',
  'WDD2473012A123456', 2024, 300000.00, 'contract_processing', '签约中', '已发起电子签约，等待客户完成签约',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'contract_initiated', 'status', 'processing', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '发起电子签约', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 0,
  JSON_OBJECT('income', 14000, 'work_years', 9, 'education', '硕士'),
  JSON_OBJECT('score', 94, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 已完成状态（签约完成）
(
  'APP20240316200009', 1, 'DIRECT', '陈九', '13800138019', '110101199909091009',
  '24年以租代售计划', 135000.00, 24, 0.0850, '凯迪拉克', 'LYRIQ',
  'LSGUE23W2NA123456', 2024, 175000.00, 'contract_completed', '签约完成', '客户已完成签约，等待放款',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 6 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'contract_initiated', 'status', 'processing', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '发起电子签约', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'contract_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户完成签约', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 0,
  JSON_OBJECT('income', 10500, 'work_years', 6, 'education', '本科'),
  JSON_OBJECT('score', 89, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),

-- 已完成状态（审批通过）
(
  'APP20240316200010', 1, 'DIRECT', '陈十', '13800138020', '110101199010101010',
  '18年中期贷款', 150000.00, 18, 0.0800, '沃尔沃', 'XC40 RECHARGE',
  'YV4A22RM0N2123456', 2023, 190000.00, 'approved', '审批通过', '业务审批通过，已放款完成',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 12 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 11 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 9 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'approved', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 8 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'secondary_review', 'status', 'approved', 'operator_id', 4, 'operator_name', '复审员', 'notes', '复审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 7 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'contract_initiated', 'status', 'processing', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '发起电子签约', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 5 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'contract_completed', 'status', 'approved', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '签约完成，业务审批通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 3 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 0,
  JSON_OBJECT('income', 12000, 'work_years', 8, 'education', '硕士'),
  JSON_OBJECT('score', 91, 'level', 'low'),
  DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)
),

-- 已驳回状态
(
  'APP20240316200011', 1, 'DIRECT', '陈十一', '13800138021', '110101199111111011',
  '12年短期贷款', 85000.00, 12, 0.0750, '哪吒', 'U-II',
  'LNBSCKAG5NV123456', 2023, 110000.00, 'rejected', '审批拒绝', '终审未通过，风险评估分数不达标',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 15 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'approved', 'operator_id', 2, 'operator_name', '系统', 'notes', '初审通过', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 14 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'interview_completed', 'status', 'completed', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '面审完成', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 12 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'final_review', 'status', 'rejected', 'operator_id', 3, 'operator_name', '终审员', 'notes', '终审拒绝：风险评估分数不达标', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 10 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY), 0,
  JSON_OBJECT('income', 4500, 'work_years', 1, 'education', '高中'),
  JSON_OBJECT('score', 65, 'level', 'high'),
  DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY)
),

-- 需要补件状态
(
  'APP20240316200012', 1, 'DIRECT', '陈十二', '13800138022', '110101199212121012',
  '24年以租代售计划', 125000.00, 24, 0.0850, '威马', 'EX5',
  'LSYW53E02PN123456', 2023, 160000.00, 'supplement_required', '需要补件', '收入证明不完整，需要补充银行流水和工作证明',
  JSON_ARRAY(
    JSON_OBJECT('stage', 'submitted', 'status', 'submitted', 'operator_id', 1, 'operator_name', '张三业务员', 'notes', '客户提交申请', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 4 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('stage', 'initial_review', 'status', 'supplement_required', 'operator_id', 2, 'operator_name', '初审员', 'notes', '需要补充相关材料', 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 4 DAY), NULL, 1,
  JSON_OBJECT('income', 6800, 'work_years', 2, 'education', '大专'),
  JSON_OBJECT('score', 75, 'level', 'medium'),
  DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)
);

-- 5. 为已预约面审的业务创建面审预约记录
INSERT INTO `interview_appointments` (
  `application_id`, `user_id`, `appointment_no`, `status`, `status_text`,
  `appointment_time`, `appointment_location`, `appointment_notes`,
  `created_time`, `scheduled_time`, `operation_log`, `created_at`, `updated_at`
) VALUES (
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316200004'),
  1, CONCAT('INT', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '0004'),
  'scheduled', '已预约',
  DATE_ADD(NOW(), INTERVAL 1 DAY) + INTERVAL 14 HOUR,
  '上海市浦东新区金融中心大厦12楼面审室',
  '请携带身份证原件及相关收入证明材料',
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 1 DAY),
  JSON_ARRAY(
    JSON_OBJECT('action', 'create', 'description', '创建面审预约', 'user_id', 1, 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')),
    JSON_OBJECT('action', 'schedule', 'description', '预约面审', 'user_id', 1, 'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s'))
  ),
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 1 DAY)
);

-- 6. 为需要补件的业务创建补件记录
INSERT INTO `customer_supplements` (
  `application_id`, `user_id`, `supplement_no`, `reason`, `requirements`, `required_documents`,
  `status`, `status_text`, `created_time`, `deadline`, `created_at`, `updated_at`
) VALUES (
  (SELECT id FROM business_applications WHERE application_no = 'APP20240316200012'),
  1, CONCAT('SUP', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '0012'),
  '收入证明不完整，需要补充银行流水和工作证明',
  '请提供最近6个月的银行流水账单（需加盖银行公章）和单位开具的工作收入证明',
  JSON_ARRAY(
    JSON_OBJECT('name', '银行流水', 'type', 'pdf', 'required', true, 'description', '最近6个月银行流水，需加盖银行公章'),
    JSON_OBJECT('name', '工作收入证明', 'type', 'image', 'required', true, 'description', '单位开具的工作收入证明'),
    JSON_OBJECT('name', '工作合同', 'type', 'pdf', 'required', false, 'description', '劳动合同（如有请提供）')
  ),
  'pending', '待补件',
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_ADD(NOW(), INTERVAL 5 DAY),
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 2 DAY)
);

-- 7. 验证数据插入结果
SELECT 
  ba.application_no,
  ba.customer_name,
  ba.product_name,
  ba.loan_amount,
  ba.status,
  ba.status_text,
  ba.submit_time,
  CASE 
    WHEN ia.id IS NOT NULL THEN CONCAT('面审预约ID: ', ia.id)
    WHEN cs.id IS NOT NULL THEN CONCAT('补件ID: ', cs.id)
    ELSE '无关联记录'
  END as related_record
FROM business_applications ba
LEFT JOIN interview_appointments ia ON ba.id = ia.application_id AND ia.status = 'scheduled'
LEFT JOIN customer_supplements cs ON ba.id = cs.application_id AND cs.status = 'pending'
WHERE ba.application_no LIKE 'APP202403162%'
ORDER BY ba.created_at DESC;

-- 8. 统计各状态数量
SELECT 
  status,
  status_text,
  COUNT(*) as count
FROM business_applications 
WHERE application_no LIKE 'APP202403162%'
GROUP BY status, status_text
ORDER BY count DESC;

-- 9. 清理脚本（备用，慎用）
-- DELETE FROM interview_appointments WHERE application_id IN (SELECT id FROM business_applications WHERE application_no LIKE 'APP202403162%');
-- DELETE FROM customer_supplements WHERE application_id IN (SELECT id FROM business_applications WHERE application_no LIKE 'APP202403162%');
-- DELETE FROM business_applications WHERE application_no LIKE 'APP202403162%'; 