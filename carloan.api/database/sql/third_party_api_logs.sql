-- 第三方接口调用记录表
-- 用于记录所有第三方接口的调用日志，包括e签宝、极证云、车300等
-- 创建时间: 2024-01-01

CREATE TABLE `third_party_api_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务名称(如:esign,jizhengyun,che300)',
  `api_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接口名称',
  `method` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法(GET/POST/PUT等)',
  `request_url` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求地址',
  `request_headers` json DEFAULT NULL COMMENT '请求头(脱敏后)',
  `request_params` json DEFAULT NULL COMMENT '请求参数(脱敏后)',
  `response_status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `response_headers` json DEFAULT NULL COMMENT '响应头',
  `response_body` json DEFAULT NULL COMMENT '响应内容(脱敏后)',
  `duration_ms` int(11) DEFAULT NULL COMMENT '请求耗时(毫秒)',
  `error_message` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误信息',
  `is_success` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否成功',
  `business_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID',
  `business_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务类型(contract,ocr,valuation等)',
  `request_time` timestamp NOT NULL COMMENT '请求时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `third_party_api_logs_service_api_index` (`service_name`,`api_name`),
  KEY `third_party_api_logs_request_time_index` (`request_time`),
  KEY `third_party_api_logs_business_index` (`business_id`,`business_type`),
  KEY `third_party_api_logs_is_success_index` (`is_success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方接口调用记录表';

-- 为提高查询性能，创建复合索引
ALTER TABLE `third_party_api_logs` ADD INDEX `idx_service_time_success` (`service_name`, `request_time`, `is_success`);
ALTER TABLE `third_party_api_logs` ADD INDEX `idx_business_service` (`business_id`, `business_type`, `service_name`);

-- 示例查询语句
/*
-- 查看所有服务的调用统计
SELECT 
    service_name,
    COUNT(*) as total_calls,
    SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls,
    ROUND(SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) as success_rate
FROM third_party_api_logs 
GROUP BY service_name;

-- 查看指定服务的失败记录
SELECT api_name, error_message, request_time, duration_ms
FROM third_party_api_logs 
WHERE service_name = 'esign' AND is_success = 0 
ORDER BY request_time DESC 
LIMIT 10;

-- 查看指定业务的所有第三方调用记录
SELECT service_name, api_name, is_success, duration_ms, request_time
FROM third_party_api_logs 
WHERE business_id = 123 AND business_type = 'contract'
ORDER BY request_time ASC;

-- 查看各服务的平均响应时间
SELECT 
    service_name,
    api_name,
    AVG(duration_ms) as avg_duration,
    MIN(duration_ms) as min_duration,
    MAX(duration_ms) as max_duration,
    COUNT(*) as call_count
FROM third_party_api_logs 
WHERE is_success = 1 
GROUP BY service_name, api_name
ORDER BY avg_duration DESC;
*/ 