<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoanPeriod extends BaseModel
{
    protected $table = 'loan_periods';

    protected $fillable = [
        'period_months',
        'period_label',
        'is_default',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'period_months' => 'integer',
        'is_default' => 'boolean',
        'status' => 'integer',
        'sort_order' => 'integer'
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 获取启用的期数
     *
     * @param $query
     * @return mixed
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 按排序权重排序
     *
     * @param $query
     * @return mixed
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * 获取默认期数
     *
     * @param $query
     * @return mixed
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 获取所有启用的期数选项
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActivePeriods()
    {
        return self::enabled()
            ->ordered()
            ->select([
                'period_months as value',
                'period_label as label',
                'is_default'
            ])
            ->get();
    }

    /**
     * 获取默认期数选项
     *
     * @return LoanPeriod|null
     */
    public static function getDefaultPeriod()
    {
        return self::enabled()->default()->first();
    }
} 