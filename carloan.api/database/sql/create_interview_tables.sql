-- 面审功能相关表结构
-- 创建时间: 2024-03-16
-- 说明: 包含面审预约表及测试数据

-- 1. 创建面审预约表
CREATE TABLE `interview_appointments` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `application_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务申请ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '业务员ID',
  `appointment_no` varchar(32) NOT NULL COMMENT '预约单号',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态:pending待预约,scheduled已预约,completed已完成,cancelled已取消',
  `status_text` varchar(50) NOT NULL DEFAULT '待预约' COMMENT '状态文本',
  `appointment_time` timestamp NULL DEFAULT NULL COMMENT '预约面审时间',
  `appointment_location` varchar(200) DEFAULT NULL COMMENT '面审地点',
  `appointment_notes` text COMMENT '预约备注',
  `interview_result` tinyint(4) DEFAULT NULL COMMENT '面审结果:1通过,0不通过',
  `interview_notes` text COMMENT '面审备注',
  `interview_materials` json DEFAULT NULL COMMENT '面审材料',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `scheduled_time` timestamp NULL DEFAULT NULL COMMENT '预约时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `cancelled_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `interviewer_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '面审官ID',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `operation_log` json DEFAULT NULL COMMENT '操作日志',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `interview_appointments_appointment_no_unique` (`appointment_no`),
  KEY `interview_appointments_application_id_index` (`application_id`),
  KEY `interview_appointments_user_id_index` (`user_id`),
  KEY `interview_appointments_status_index` (`status`),
  KEY `interview_appointments_status_user_id_index` (`status`,`user_id`),
  KEY `interview_appointments_appointment_time_index` (`appointment_time`),
  KEY `interview_appointments_scheduled_time_index` (`scheduled_time`),
  CONSTRAINT `interview_appointments_application_id_foreign` FOREIGN KEY (`application_id`) REFERENCES `business_applications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='面审预约表';

-- 2. 插入测试业务申请数据（面审相关）
INSERT INTO `business_applications` (
  `application_no`, `user_id`, `channel_code`, `customer_name`, `customer_phone`, `customer_id_card`,
  `product_name`, `loan_amount`, `loan_period`, `interest_rate`, `vehicle_brand`, `vehicle_model`,
  `vehicle_vin`, `vehicle_year`, `vehicle_price`, `status`, `status_text`,
  `submit_time`, `created_at`, `updated_at`
) VALUES 
(
  'APP20240316001', 1, 'DEFAULT', '张三', '***********', '110101199001011234',
  '24年以租代售计划', 150000.00, 24, 0.0650, '丰田', '凯美瑞',
  'JTDKN3DU5E0123456', 2023, 200000.00, 'interview_pending', '待面审',
  DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
  'APP20240316002', 1, 'DEFAULT', '李四', '***********', '110101199002021234',
  '36年车贷计划', 200000.00, 36, 0.0720, '本田', '雅阁',
  'JHMCR6F70EC123456', 2024, 250000.00, 'interview_scheduled', '已预约面审',
  DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
  'APP20240316003', 1, 'DEFAULT', '王五', '***********', '110101199003031234',
  '12年短期贷款', 80000.00, 12, 0.0580, '大众', '帕萨特',
  'WVWZZZ3CZ9E123456', 2022, 180000.00, 'interview_pending', '待面审',
  DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR)
);

-- 3. 插入面审预约测试数据
-- 获取刚创建的业务申请ID
SET @app_id_2 = (SELECT id FROM business_applications WHERE application_no = 'APP20240316002');

INSERT INTO `interview_appointments` (
  `application_id`, `user_id`, `appointment_no`, `status`, `status_text`,
  `appointment_time`, `appointment_location`, `appointment_notes`,
  `created_time`, `scheduled_time`, `operation_log`, `created_at`, `updated_at`
) VALUES (
  @app_id_2, 1, CONCAT('INT', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), FLOOR(1000 + RAND() * 9000)),
  'scheduled', '已预约',
  DATE_ADD(NOW(), INTERVAL 1 DAY) + INTERVAL 14 HOUR + INTERVAL 30 MINUTE,
  '上海市浦东新区金融中心大厦12楼面审室',
  '请携带身份证原件及复印件',
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 1 DAY),
  JSON_ARRAY(
    JSON_OBJECT(
      'action', 'create',
      'description', '创建面审预约',
      'user_id', 1,
      'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 DAY), '%Y-%m-%d %H:%i:%s')
    ),
    JSON_OBJECT(
      'action', 'schedule',
      'description', '预约面审',
      'user_id', 1,
      'created_at', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d %H:%i:%s')
    )
  ),
  DATE_SUB(NOW(), INTERVAL 2 DAY),
  DATE_SUB(NOW(), INTERVAL 1 DAY)
);

-- 4. 验证数据
SELECT 
  ba.application_no,
  ba.customer_name,
  ba.status,
  ba.status_text,
  ia.appointment_no,
  ia.status as appointment_status,
  ia.appointment_time,
  ia.appointment_location
FROM business_applications ba
LEFT JOIN interview_appointments ia ON ba.id = ia.application_id
WHERE ba.application_no IN ('APP20240316001', 'APP20240316002', 'APP20240316003')
ORDER BY ba.created_at DESC; 