<template>
	<!-- 已完成详情 -->
	<view class="completed-detail">
		<navbar title="完成详情" />
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<uni-load-more status="loading" :content-text="{ contentdown: '', contentrefresh: '正在加载...', contentnomore: '' }" />
		</view>
		
		<!-- 详情内容 -->
		<view class="content" v-else-if="detail">
			<!-- 完成状态 -->
			<view class="section">
				<view class="section-title">业务状态</view>
				<view class="section-content">
					<view class="status-info">
						<view class="status-badge success">
							{{ detail.status_text || '已完成' }}
						</view>
						<view class="completion-info">
							<view class="completion-time" v-if="detail.completion_time">完成时间：{{ detail.completion_time }}</view>
							<view class="completion-desc">业务已成功完成，合同生效</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="section">
				<view class="section-title">基本信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">业务单号：</view>
						<view class="info-value">{{ detail.application_no }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">客户姓名：</view>
						<view class="info-value">{{ detail.customer_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">联系电话：</view>
						<view class="info-value">{{ detail.customer_phone }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">产品名称：</view>
						<view class="info-value">{{ detail.product_name }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">批准金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.approved_amount || detail.loan_amount) }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">提交时间：</view>
						<view class="info-value">{{ detail.submit_time || detail.created_time }}</view>
					</view>
				</view>
			</view>
			
			<!-- 放款信息 -->
			<view class="section" v-if="detail.loan_info">
				<view class="section-title">放款信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">放款金额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_info.loan_amount) }}</view>
					</view>
					<view class="info-item" v-if="detail.loan_info.loan_rate">
						<view class="info-label">贷款利率：</view>
						<view class="info-value">{{ detail.loan_info.loan_rate }}%</view>
					</view>
					<view class="info-item" v-if="detail.loan_info.loan_term">
						<view class="info-label">贷款期限：</view>
						<view class="info-value">{{ detail.loan_info.loan_term }}个月</view>
					</view>
					<view class="info-item" v-if="detail.loan_info.monthly_payment">
						<view class="info-label">月还款额：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.loan_info.monthly_payment) }}</view>
					</view>
					<view class="info-item" v-if="detail.loan_info.loan_date">
						<view class="info-label">放款日期：</view>
						<view class="info-value">{{ detail.loan_info.loan_date }}</view>
					</view>
					<view class="info-item" v-if="detail.loan_info.first_payment_date">
						<view class="info-label">首次还款日：</view>
						<view class="info-value">{{ detail.loan_info.first_payment_date }}</view>
					</view>
				</view>
			</view>
			
			<!-- 车辆信息 -->
			<view class="section" v-if="detail.vehicle_info">
				<view class="section-title">车辆信息</view>
				<view class="section-content">
					<view class="info-item">
						<view class="info-label">车辆品牌：</view>
						<view class="info-value">{{ detail.vehicle_info.brand }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">车辆型号：</view>
						<view class="info-value">{{ detail.vehicle_info.model }}</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_info.year">
						<view class="info-label">车辆年份：</view>
						<view class="info-value">{{ detail.vehicle_info.year }}年</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_info.vin">
						<view class="info-label">车架号：</view>
						<view class="info-value">{{ detail.vehicle_info.vin }}</view>
					</view>
					<view class="info-item" v-if="detail.vehicle_info.price">
						<view class="info-label">车辆价格：</view>
						<view class="info-value amount">¥{{ formatAmount(detail.vehicle_info.price) }}</view>
					</view>
				</view>
			</view>
			
			<!-- 合同信息 -->
			<view class="section" v-if="detail.contract_info">
				<view class="section-title">合同信息</view>
				<view class="section-content">
					<view class="contract-list">
						<view 
							class="contract-item" 
							v-for="(contract, index) in detail.contract_info.contracts" 
							:key="index"
							@click="onViewContract(contract)"
						>
							<view class="contract-icon">📄</view>
							<view class="contract-info">
								<view class="contract-name">{{ contract.name }}</view>
								<view class="contract-status success">{{ contract.status_text }}</view>
								<view class="contract-time" v-if="contract.sign_time">签约时间：{{ contract.sign_time }}</view>
							</view>
							<view class="contract-action">
								<image 
									class="action-icon" 
									src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-right.png"
								/>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 业务历程 -->
			<view class="section" v-if="detail.business_history && detail.business_history.length > 0">
				<view class="section-title">业务历程</view>
				<view class="section-content">
					<view class="business-timeline">
						<view 
							class="timeline-item completed" 
							v-for="(step, index) in detail.business_history" 
							:key="index"
						>
							<view class="timeline-dot">
								<image 
									class="dot-icon" 
									src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/contract/progress/icon-dot-green.png" 
								/>
							</view>
							<view class="timeline-content">
								<view class="timeline-title">{{ step.title }}</view>
								<view class="timeline-desc" v-if="step.description">{{ step.description }}</view>
								<view class="timeline-time" v-if="step.time">{{ step.time }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view class="error-state" v-else>
			<view class="error-icon">❌</view>
			<view class="error-text">{{ errorMessage || '数据加载失败' }}</view>
			<button class="btn btn-primary" @click="loadDetail">重新加载</button>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="detail">
			<button 
				class="action-btn outline" 
				@click="onContactCustomer"
				v-if="detail.customer_phone"
			>
				联系客户
			</button>
			<button 
				class="action-btn primary" 
				@click="onViewRepaymentPlan"
				v-if="detail.loan_info"
			>
				还款计划
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import navbar from "@/components/navbar/index.vue"
import request from "@/utils/request"

// 响应式数据
const loading = ref(true)
const detail = ref(null)
const errorMessage = ref('')
const applicationId = ref('')

// 页面加载
onLoad((options) => {
	if (options.id) {
		applicationId.value = options.id
		loadDetail()
	} else {
		errorMessage.value = '参数错误'
		loading.value = false
	}
})

// 加载详情数据
const loadDetail = async () => {
	try {
		loading.value = true
		errorMessage.value = ''
		
		const response = await request.get(`/business-applications/${applicationId.value}/completed-detail`)
		
		console.log('完成详情API Response:', response)
		
		if (response && response.status_code === 200) {
			detail.value = response.data
		} else {
			errorMessage.value = response?.message || '获取数据失败'
		}
	} catch (error) {
		console.error('获取完成详情失败:', error)
		errorMessage.value = '网络错误，请重试'
	} finally {
		loading.value = false
	}
}

// 格式化金额
const formatAmount = (amount) => {
	if (!amount) return '0'
	return Number(amount).toLocaleString()
}

// 查看合同
const onViewContract = (contract) => {
	if (contract.view_url) {
		uni.navigateTo({
			url: `/pages/webview/index?url=${encodeURIComponent(contract.view_url)}&title=${contract.name}`
		})
	} else {
		uni.showToast({
			title: '暂无法查看',
			icon: 'none'
		})
	}
}

// 联系客户
const onContactCustomer = () => {
	if (detail.value.customer_phone) {
		uni.makePhoneCall({
			phoneNumber: detail.value.customer_phone
		})
	} else {
		uni.showToast({
			title: '无客户联系方式',
			icon: 'none'
		})
	}
}

// 查看还款计划
const onViewRepaymentPlan = () => {
	uni.navigateTo({
		url: `/pages/loan/repayment-plan?id=${applicationId.value}`
	})
}
</script>

<style lang="scss" scoped>
.completed-detail {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 48rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 48rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 48rpx);
}

.loading {
	padding: 100rpx 0;
	text-align: center;
}

.content {
	padding: 20rpx;
}

.section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		background: #f8f9fa;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.section-content {
		padding: 30rpx;
	}
}

.info-item {
	display: flex;
	margin-bottom: 24rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	.info-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
		flex-shrink: 0;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		
		&.amount {
			color: #e6a23c;
			font-weight: 500;
		}
	}
}

.status-info {
	text-align: center;
	
	.status-badge {
		display: inline-block;
		padding: 12rpx 24rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 20rpx;
		
		&.success {
			background: #f6ffed;
			color: #52c41a;
		}
	}
	
	.completion-info {
		.completion-time {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 8rpx;
		}
		
		.completion-desc {
			font-size: 24rpx;
			color: #999;
		}
	}
}

.contract-list {
	.contract-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		cursor: pointer;
		
		&:last-child {
			border-bottom: none;
		}
		
		.contract-icon {
			font-size: 40rpx;
			margin-right: 20rpx;
		}
		
		.contract-info {
			flex: 1;
			
			.contract-name {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 8rpx;
			}
			
			.contract-status {
				font-size: 24rpx;
				padding: 4rpx 12rpx;
				border-radius: 6rpx;
				display: inline-block;
				margin-bottom: 4rpx;
				
				&.success {
					background: #f6ffed;
					color: #52c41a;
				}
			}
			
			.contract-time {
				font-size: 22rpx;
				color: #999;
			}
		}
		
		.contract-action {
			.action-icon {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}
}

.business-timeline {
	.timeline-item {
		display: flex;
		margin-bottom: 40rpx;
		position: relative;
		
		&:last-child {
			margin-bottom: 0;
			
			&::after {
				display: none;
			}
		}
		
		&::after {
			content: '';
			position: absolute;
			left: 19rpx;
			top: 38rpx;
			width: 2rpx;
			height: calc(100% + 20rpx);
			background: #52c41a;
			z-index: 0;
		}
		
		.timeline-dot {
			width: 38rpx;
			height: 38rpx;
			margin-right: 20rpx;
			position: relative;
			z-index: 1;
			
			.dot-icon {
				width: 100%;
				height: 100%;
			}
		}
		
		.timeline-content {
			flex: 1;
			
			.timeline-title {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.timeline-desc {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 8rpx;
			}
			
			.timeline-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.error-state {
	padding: 100rpx 40rpx;
	text-align: center;
	
	.error-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
	}
	
	.btn {
		height: 76rpx;
		padding: 20rpx 32rpx;
		border-radius: 8rpx;
		border: none;
		font-size: 28rpx;
		
		&.btn-primary {
			background: #333;
			color: #fff;
		}
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 24rpx 40rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
	display: flex;
	gap: 16rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	
	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 8rpx;
		border: none;
		font-size: 32rpx;
		font-weight: 500;
		
		&.primary {
			background: #333;
			color: #fff;
		}
		
		&.outline {
			background: #fff;
			color: #333;
			border: 2rpx solid #d9d9d9;
		}
	}
}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 