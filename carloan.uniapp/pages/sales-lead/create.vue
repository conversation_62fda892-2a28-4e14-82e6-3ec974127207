<template>
	<!-- 新增线索 -->
	<view class="lead-form">
		<navbar title="新增线索" />
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text>加载中...</text>
		</view>
		
		<view class="container" v-else>
			<!-- 客户基本信息 -->
			<view class="form-section">
				<view class="section-title">客户基本信息</view>
				
				<view class="form-item">
					<view class="label">客户姓名 <text class="required">*</text></view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_name"
							placeholder="请输入客户姓名"
							maxlength="20"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">联系电话 <text class="required">*</text></view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_phone"
							placeholder="请输入客户手机号"
							type="number"
							maxlength="11"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">年龄</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_age"
							placeholder="请输入客户年龄"
							type="number"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">性别</view>
					<view class="radio-group">
						<label class="radio-item" v-for="item in genderOptions" :key="item.value">
							<radio 
								:value="item.value" 
								:checked="formData.customer_gender === item.value"
								@change="handleGenderChange"
								color="#007AFF"
							/>
							<text class="radio-text">{{ item.label }}</text>
						</label>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">所在城市</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_city"
							placeholder="请输入客户所在城市"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">职业</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_occupation"
							placeholder="请输入客户职业"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">月收入（元）</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.customer_income"
							placeholder="请输入月收入"
							type="number"
						/>
					</view>
				</view>
			</view>
			
			<!-- 线索来源信息 -->
			<view class="form-section">
				<view class="section-title">线索来源</view>
				
				<view class="form-item">
					<view class="label">来源渠道 <text class="required">*</text></view>
					<wd-picker
						v-model="formData.source_id"
						:columns="sourceColumns"
						label-key="label"
						value-key="value"
						placeholder="请选择线索来源"
						title="选择线索来源"
						:close-on-click-modal="true"
						@confirm="handleSourceConfirm"
						@open="onPickerOpen"
						@cancel="onPickerClose"
					/>
				</view>
				
				<view class="form-item">
					<view class="label">来源详情</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.source_detail"
							placeholder="请输入来源详情（如：朋友介绍、广告等）"
							maxlength="200"
						/>
					</view>
				</view>
			</view>
			
			<!-- 购车意向信息 -->
			<view class="form-section">
				<view class="section-title">购车意向</view>
				
				<view class="form-item">
					<view class="label">意向等级 <text class="required">*</text></view>
					<wd-picker
						v-model="formData.intent_level"
						:columns="intentLevelColumns"
						label-key="label"
						value-key="value"
						placeholder="请选择意向等级"
						title="选择意向等级"
						:close-on-click-modal="true"
						@confirm="handleIntentLevelConfirm"
						@open="onPickerOpen"
						@cancel="onPickerClose"
					/>
				</view>
				
				<view class="form-item">
					<view class="label">意向品牌</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.intent_brand"
							placeholder="请输入意向品牌"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">意向车型</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.intent_model"
							placeholder="请输入意向车型"
							maxlength="50"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">价格范围（万元）</view>
					<view class="price-range">
						<input
							class="price-input"
							v-model="formData.intent_price_min"
							placeholder="最低价"
							type="number"
						/>
						<text class="price-divider">-</text>
						<input
							class="price-input"
							v-model="formData.intent_price_max"
							placeholder="最高价"
							type="number"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">贷款金额（万元）</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.intent_loan_amount"
							placeholder="请输入贷款金额"
							type="number"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">贷款期限（月）</view>
					<view class="input-container">
						<input
							class="form-input"
							v-model="formData.intent_loan_period"
							placeholder="请输入贷款期限"
							type="number"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">购车时间</view>
					<wd-picker
						v-model="formData.purchase_timeline"
						:columns="timelineColumns"
						label-key="label"
						value-key="value"
						placeholder="请选择购车时间"
						title="选择购车时间"
						:close-on-click-modal="true"
						@confirm="handleTimelineConfirm"
						@open="onPickerOpen"
						@cancel="onPickerClose"
					/>
				</view>
			</view>
			
			<!-- 备注信息 -->
			<view class="form-section">
				<view class="section-title">备注信息</view>
				
				<view class="form-item">
					<view class="label">备注</view>
					<view class="textarea-container">
						<textarea
							class="form-textarea"
							v-model="formData.remarks"
							placeholder="请输入备注信息"
							maxlength="500"
						/>
						<view class="char-count">{{ formData.remarks.length }}/500</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="btn-container" v-if="!loading">
			<button 
				class="btn btn-primary" 
				:disabled="!canSubmit || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '提交中...' : '确认新增' }}
			</button>
		</view>
		

	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 页面状态
const loading = ref(true);
const submitLoading = ref(false);

// 表单数据
const formData = ref({
	customer_name: '',
	customer_phone: '',
	customer_age: '',
	customer_gender: 0,
	customer_city: '',
	customer_occupation: '',
	customer_income: '',
	source_id: '',
	source_detail: '',
	intent_level: '',
	intent_brand: '',
	intent_model: '',
	intent_price_min: '',
	intent_price_max: '',
	intent_loan_amount: '',
	intent_loan_period: '',
	purchase_timeline: '',
	remarks: ''
});

// 数据选项
const sourceColumns = ref([]);
const intentLevelColumns = ref([
	{ label: '1级（低意向）', value: 1 },
	{ label: '2级（一般意向）', value: 2 },
	{ label: '3级（中等意向）', value: 3 },
	{ label: '4级（高意向）', value: 4 },
	{ label: '5级（极高意向）', value: 5 }
]);

const timelineColumns = ref([
	{ label: '一周内', value: '一周内' },
	{ label: '一个月内', value: '一个月内' },
	{ label: '三个月内', value: '三个月内' },
	{ label: '半年内', value: '半年内' },
	{ label: '一年内', value: '一年内' },
	{ label: '暂无计划', value: '暂无计划' }
]);

const genderOptions = [
	{ label: '未知', value: 0 },
	{ label: '男', value: 1 },
	{ label: '女', value: 2 }
];

// 计算是否可以提交
const canSubmit = computed(() => {
	return formData.value.customer_name.trim() && 
		   formData.value.customer_phone.trim() && 
		   formData.value.source_id && 
		   formData.value.intent_level;
});

// 页面加载
onMounted(() => {
	loadSources();
});

// 加载线索来源
const loadSources = async () => {
	try {
		loading.value = true;
		const response = await request.get('/sales-leads/sources');
		
		if (response.status_code === 200) {
			sourceColumns.value = response.data.map(item => ({
				label: item.name,
				value: item.id,
				description: item.description
			}));
		}
	} catch (error) {
		console.error('获取线索来源失败:', error);
		uni.showToast({
			title: '获取线索来源失败',
			icon: 'none'
		});
	} finally {
		loading.value = false;
	}
};

// 性别选择处理
const handleGenderChange = (e) => {
	formData.value.customer_gender = parseInt(e.detail.value);
};

// picker确认事件处理
const handleSourceConfirm = ({ value }) => {
	formData.value.source_id = value;
};

const handleIntentLevelConfirm = ({ value }) => {
	formData.value.intent_level = value;
};

const handleTimelineConfirm = ({ value }) => {
	formData.value.purchase_timeline = value;
};

// picker开启/关闭事件（wot-design-uni内置锁定滚动功能）
const onPickerOpen = () => {
	console.log('picker opened');
};

const onPickerClose = () => {
	console.log('picker closed');
};

// 提交表单
const handleSubmit = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请完善必填信息',
			icon: 'none'
		});
		return;
	}
	
	// 验证手机号格式
	const phoneRegex = /^1[3-9]\d{9}$/;
	if (!phoneRegex.test(formData.value.customer_phone)) {
		uni.showToast({
			title: '请输入正确的手机号码',
			icon: 'none'
		});
		return;
	}
	
	// 验证年龄
	if (formData.value.customer_age && (formData.value.customer_age < 18 || formData.value.customer_age > 80)) {
		uni.showToast({
			title: '客户年龄必须在18-80岁之间',
			icon: 'none'
		});
		return;
	}
	
	// 验证价格范围
	if (formData.value.intent_price_min && formData.value.intent_price_max) {
		if (parseFloat(formData.value.intent_price_min) > parseFloat(formData.value.intent_price_max)) {
			uni.showToast({
				title: '最低价格不能高于最高价格',
				icon: 'none'
			});
			return;
		}
	}
	
	submitLoading.value = true;
	
	try {
		const requestData = {
			customer_name: formData.value.customer_name.trim(),
			customer_phone: formData.value.customer_phone.trim(),
			customer_age: formData.value.customer_age ? parseInt(formData.value.customer_age) : undefined,
			customer_gender: formData.value.customer_gender,
			customer_city: formData.value.customer_city.trim(),
			customer_occupation: formData.value.customer_occupation.trim(),
			customer_income: formData.value.customer_income ? parseFloat(formData.value.customer_income) : undefined,
			source_id: formData.value.source_id,
			source_detail: formData.value.source_detail.trim(),
			intent_level: formData.value.intent_level,
			intent_brand: formData.value.intent_brand.trim(),
			intent_model: formData.value.intent_model.trim(),
			intent_price_min: formData.value.intent_price_min ? parseFloat(formData.value.intent_price_min) : undefined,
			intent_price_max: formData.value.intent_price_max ? parseFloat(formData.value.intent_price_max) : undefined,
			intent_loan_amount: formData.value.intent_loan_amount ? parseFloat(formData.value.intent_loan_amount) : undefined,
			intent_loan_period: formData.value.intent_loan_period ? parseInt(formData.value.intent_loan_period) : undefined,
			purchase_timeline: formData.value.purchase_timeline,
			remarks: formData.value.remarks.trim()
		};
		
		// 移除空值
		Object.keys(requestData).forEach(key => {
			if (requestData[key] === undefined || requestData[key] === '') {
				delete requestData[key];
			}
		});
		
		const response = await request.post('/sales-leads', requestData);
		
		if (response.status_code === 200) {
			uni.showToast({
				title: '线索创建成功',
				icon: 'success'
			});
			
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		} else {
			uni.showToast({
				title: response.message || '创建失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('创建线索失败:', error);
		uni.showToast({
			title: error.message || '创建失败',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.lead-form {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 120rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
	
	.loading-container {
		padding: 200rpx 0;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
	}
	
	.container {
		padding: 40rpx;
		
		.form-section {
			background: #ffffff;
			border-radius: 16rpx;
			padding: 40rpx;
			margin-bottom: 20rpx;
			box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0, 0, 0, 0.06);
			
			.section-title {
				font-weight: 600;
				font-size: 32rpx;
				color: #000000;
				margin-bottom: 40rpx;
				padding-bottom: 20rpx;
				border-bottom: 2rpx solid #f0f0f0;
			}
			
			.form-item {
				margin-bottom: 40rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					font-weight: 500;
					font-size: 28rpx;
					color: #000000;
					margin-bottom: 20rpx;
					
					.required {
						color: #ff4757;
						margin-left: 4rpx;
					}
				}
				
				.input-container {
					.form-input {
						width: 100%;
						height: 88rpx;
						background: #f8f8f8;
						border-radius: 12rpx;
						padding: 0 24rpx;
						font-size: 28rpx;
						color: #000000;
						box-sizing: border-box;
						border: 2rpx solid transparent;
						
						&:focus {
							background: #ffffff;
							border-color: #007AFF;
						}
						
						&::placeholder {
							color: #999999;
						}
					}
				}
				
				.textarea-container {
					position: relative;
					
					.form-textarea {
						width: 100%;
						min-height: 120rpx;
						background: #f8f8f8;
						border-radius: 12rpx;
						padding: 24rpx;
						font-size: 28rpx;
						color: #000000;
						box-sizing: border-box;
						border: 2rpx solid transparent;
						
						&:focus {
							background: #ffffff;
							border-color: #007AFF;
						}
						
						&::placeholder {
							color: #999999;
						}
					}
					
					.char-count {
						position: absolute;
						bottom: 12rpx;
						right: 12rpx;
						font-size: 24rpx;
						color: #999999;
					}
				}
				

				
				.radio-group {
					display: flex;
					align-items: center;
					
					.radio-item {
						display: flex;
						align-items: center;
						margin-right: 40rpx;
						
						.radio-text {
							margin-left: 12rpx;
							font-size: 28rpx;
							color: #000000;
						}
					}
				}
				
				.price-range {
					display: flex;
					align-items: center;
					
					.price-input {
						flex: 1;
						height: 88rpx;
						background: #f8f8f8;
						border-radius: 12rpx;
						padding: 0 24rpx;
						font-size: 28rpx;
						color: #000000;
						box-sizing: border-box;
						border: 2rpx solid transparent;
						
						&:focus {
							background: #ffffff;
							border-color: #007AFF;
						}
						
						&::placeholder {
							color: #999999;
						}
					}
					
					.price-divider {
						margin: 0 20rpx;
						font-size: 28rpx;
						color: #999999;
					}
				}
			}
		}
	}
	
	.btn-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 40rpx;
		background: #ffffff;
		box-shadow: 0rpx -8rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
		padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
		
		.btn {
			width: 100%;
			height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 500;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&.btn-primary {
				background: #000000;
				color: #ffffff;
				
				&:disabled {
					background: #cccccc;
					color: #999999;
				}
			}
		}
	}
	

}
</style>

<style lang="scss">
page {
	background: #f3f3f3;
}
</style> 