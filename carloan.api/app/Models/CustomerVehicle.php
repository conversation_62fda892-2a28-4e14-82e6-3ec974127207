<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerVehicle extends Model
{
    protected $table = 'customer_vehicles';

    protected $fillable = [
        'customer_id',
        'business_application_id',
        'vehicle_type',
        'brand',
        'model',
        'series',
        'configuration',
        'year',
        'fuel_type',
        'transmission',
        'seats',
        'guide_price',
        'discount_amount',
        'actual_price',
        'down_payment',
        'loan_amount',
        'dealer_name',
        'dealer_contact',
        'dealer_phone',
        'remarks'
    ];

    protected $casts = [
        'customer_id' => 'integer',
        'business_application_id' => 'integer',
        'year' => 'integer',
        'seats' => 'integer',
        'guide_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'actual_price' => 'decimal:2',
        'down_payment' => 'decimal:2',
        'loan_amount' => 'decimal:2'
    ];

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联业务申请
     */
    public function businessApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'business_application_id');
    }

    /**
     * 计算实际价格
     */
    public function calculateActualPrice()
    {
        $actual = $this->guide_price - ($this->discount_amount ?? 0);
        $this->actual_price = $actual;
        return $actual;
    }

    /**
     * 计算贷款金额
     */
    public function calculateLoanAmount()
    {
        $loan = $this->actual_price - ($this->down_payment ?? 0);
        $this->loan_amount = max(0, $loan);
        return $this->loan_amount;
    }

    /**
     * 获取首付比例
     */
    public function getDownPaymentRatioAttribute()
    {
        if (!$this->actual_price || $this->actual_price <= 0) {
            return 0;
        }
        
        return ($this->down_payment ?? 0) / $this->actual_price;
    }

    /**
     * 获取车辆全名
     */
    public function getFullNameAttribute()
    {
        $parts = array_filter([
            $this->brand,
            $this->model,
            $this->series,
            $this->configuration
        ]);
        
        return implode(' ', $parts);
    }

    /**
     * 获取车辆年龄
     */
    public function getVehicleAgeAttribute()
    {
        if (!$this->year) {
            return null;
        }
        
        return date('Y') - $this->year;
    }

    /**
     * 是否新能源车
     */
    public function getIsNewEnergyAttribute()
    {
        return in_array($this->fuel_type, ['纯电动', '混合动力', '插电混动']);
    }

    /**
     * 作用域：按客户ID筛选
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * 作用域：按业务申请ID筛选
     */
    public function scopeForApplication($query, $applicationId)
    {
        return $query->where('business_application_id', $applicationId);
    }

    /**
     * 作用域：按品牌筛选
     */
    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    /**
     * 作用域：按车辆类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('vehicle_type', $type);
    }

    /**
     * 作用域：按燃料类型筛选
     */
    public function scopeByFuelType($query, $fuelType)
    {
        return $query->where('fuel_type', $fuelType);
    }

    /**
     * 作用域：新能源车
     */
    public function scopeNewEnergy($query)
    {
        return $query->whereIn('fuel_type', ['纯电动', '混合动力', '插电混动']);
    }

    /**
     * 作用域：按年份范围筛选
     */
    public function scopeByYearRange($query, $startYear, $endYear = null)
    {
        $query->where('year', '>=', $startYear);
        
        if ($endYear) {
            $query->where('year', '<=', $endYear);
        }
        
        return $query;
    }
} 