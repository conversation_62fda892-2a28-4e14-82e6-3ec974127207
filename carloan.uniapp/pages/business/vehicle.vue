<template>
	<view class="business-vehicle">
		<navbar title="车辆信息" />
		
		<view class="container">
			<view class="page-title">
				<view>车辆信息</view>
				<view class="desc">请填写您要购买车辆的详细信息</view>
			</view>
			
			<!-- 车辆基本信息 -->
			<view class="basic-card">
				<view class="card-info">车辆基本信息</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label required">车辆品牌</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入车辆品牌"
							v-model="formData.brand"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label required">车辆型号</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入车辆型号"
							v-model="formData.model"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">车系</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入车系"
							v-model="formData.series"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">配置</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入具体配置"
							v-model="formData.configuration"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">车身颜色</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入车身颜色"
							v-model="formData.color"
							@input="saveFormData"
						/>
					</view>
				</view>
			</view>

			<!-- 车辆详情 -->
			<view class="basic-card">
				<view class="card-info">车辆详情</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">车辆类型</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getVehicleTypeLabel(formData.vehicle_type)"
								readonly
								placeholder="请选择车辆类型"
								@click="showVehicleTypePicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
					<view class="card-item">
						<view class="label">燃料类型</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getFuelTypeLabel(formData.fuel_type)"
								readonly
								placeholder="请选择燃料类型"
								@click="showFuelTypePicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
					<view class="card-item">
						<view class="label">发动机排量</view>
						<input 
							class="input"
							type="text"
							placeholder="如：1.5T、2.0L"
							v-model="formData.displacement"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">变速箱</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getTransmissionLabel(formData.transmission)"
								readonly
								placeholder="请选择变速箱类型"
								@click="showTransmissionPicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
					<view class="card-item">
						<view class="label">座位数</view>
						<view class="select-wrapper">
							<input 
								class="input select-input"
								:value="getSeatsLabel(formData.seats)"
								readonly
								placeholder="请选择座位数"
								@click="showSeatsPicker"
							/>
							<image class="arrow-icon" src="https://zccarloan.oss-cn-shanghai.aliyuncs.com/uniapp/common/icon-arrow-down.png" />
						</view>
					</view>
				</view>
			</view>

			<!-- 价格信息 -->
			<view class="basic-card">
				<view class="card-info">价格信息</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label required">指导价</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入官方指导价格(万元)"
							v-model="formData.guide_price"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label required">成交价</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入实际成交价格(万元)"
							v-model="formData.actual_price"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">优惠金额</view>
						<input 
							class="input"
							:value="discountAmount"
							readonly
							placeholder="自动计算"
						/>
					</view>
					<view class="card-item">
						<view class="label">首付金额</view>
						<input 
							class="input"
							type="digit"
							placeholder="请输入首付金额(万元)"
							v-model="formData.down_payment"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">贷款金额</view>
						<input 
							class="input"
							:value="loanAmount"
							readonly
							placeholder="自动计算"
						/>
					</view>
				</view>
			</view>

			<!-- 经销商信息 -->
			<view class="basic-card">
				<view class="card-info">经销商信息</view>
				<view class="card-container">
					<view class="card-item">
						<view class="label">经销商名称</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入经销商名称"
							v-model="formData.dealer_name"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">销售顾问</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入销售顾问姓名"
							v-model="formData.sales_consultant"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">联系电话</view>
						<input 
							class="input"
							type="text"
							placeholder="请输入联系电话"
							v-model="formData.dealer_phone"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">经销商地址</view>
						<textarea 
							class="textarea"
							placeholder="请输入经销商详细地址"
							v-model="formData.dealer_address"
							@input="saveFormData"
						/>
					</view>
					<view class="card-item">
						<view class="label">备注</view>
						<textarea 
							class="textarea"
							placeholder="请输入备注信息"
							v-model="formData.remarks"
							@input="saveFormData"
						/>
					</view>
				</view>
			</view>

			<view style="height: 120rpx;"></view>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-btn">
			<button 
				class="btn btn-primary" 
				:disabled="!isFormValid || submitLoading"
				@click="handleSubmit"
			>
				{{ submitLoading ? '保存中...' : '保存并继续' }}
			</button>
		</view>


	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import navbar from "@/components/navbar/index.vue";
import request from "@/utils/request";

// 数据
const formData = ref({
	brand: '',                    // 车辆品牌
	model: '',                    // 车辆型号
	series: '',                   // 车系
	configuration: '',            // 配置
	color: '',                    // 车身颜色
	vehicle_type: '',             // 车辆类型
	fuel_type: '',                // 燃料类型
	displacement: '',             // 发动机排量
	transmission: '',             // 变速箱
	seats: '',                    // 座位数
	guide_price: '',              // 指导价
	actual_price: '',             // 成交价
	down_payment: '',             // 首付金额
	dealer_name: '',              // 经销商名称
	sales_consultant: '',         // 销售顾问
	dealer_phone: '',             // 联系电话
	dealer_address: '',           // 经销商地址
	remarks: ''                   // 备注
});

// 状态
const submitLoading = ref(false);

// 选项
const vehicleTypeOptions = [
	{ name: '轿车', value: 'sedan' },
	{ name: 'SUV', value: 'suv' },
	{ name: 'MPV', value: 'mpv' },
	{ name: '跑车', value: 'sports' },
	{ name: '皮卡', value: 'pickup' },
	{ name: '微型车', value: 'mini' },
	{ name: '其他', value: 'other' }
];

const fuelTypeOptions = [
	{ name: '汽油', value: 'gasoline' },
	{ name: '柴油', value: 'diesel' },
	{ name: '混合动力', value: 'hybrid' },
	{ name: '纯电动', value: 'electric' },
	{ name: '插电混动', value: 'phev' },
	{ name: '其他', value: 'other' }
];

const transmissionOptions = [
	{ name: '手动变速箱', value: 'manual' },
	{ name: '自动变速箱', value: 'automatic' },
	{ name: 'CVT无级变速', value: 'cvt' },
	{ name: '双离合', value: 'dct' },
	{ name: '其他', value: 'other' }
];

const seatsOptions = [
	{ name: '2座', value: 2 },
	{ name: '4座', value: 4 },
	{ name: '5座', value: 5 },
	{ name: '6座', value: 6 },
	{ name: '7座', value: 7 },
	{ name: '8座', value: 8 },
	{ name: '9座及以上', value: 9 }
];

// 计算属性
const discountAmount = computed(() => {
	const guide = parseFloat(formData.value.guide_price) || 0;
	const actual = parseFloat(formData.value.actual_price) || 0;
	const discount = guide - actual;
	return discount > 0 ? discount.toFixed(2) + '万元' : '0万元';
});

const loanAmount = computed(() => {
	const actual = parseFloat(formData.value.actual_price) || 0;
	const down = parseFloat(formData.value.down_payment) || 0;
	const loan = actual - down;
	return loan > 0 ? loan.toFixed(2) + '万元' : '0万元';
});

const isFormValid = computed(() => {
	return formData.value.brand && 
		   formData.value.model && 
		   formData.value.guide_price && 
		   formData.value.actual_price;
});

// 页面生命周期
onMounted(() => {
	loadFormData();
});

onShow(() => {
	loadFormData();
});

// 方法
const goBack = () => {
	uni.navigateBack();
};

// 加载表单数据
const loadFormData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft_data') || {};
		if (businessData.vehicle) {
			formData.value = { ...formData.value, ...businessData.vehicle };
		}
	} catch (error) {
		console.error('加载车辆信息失败:', error);
	}
};

// 保存表单数据
const saveFormData = () => {
	try {
		const businessData = uni.getStorageSync('business_draft_data') || {};
		businessData.vehicle = formData.value;
		uni.setStorageSync('business_draft_data', businessData);
	} catch (error) {
		console.error('保存车辆信息失败:', error);
	}
};

// 车辆类型选择器
const showVehicleTypePicker = () => {
	const itemList = vehicleTypeOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = vehicleTypeOptions[res.tapIndex];
			formData.value.vehicle_type = selectedOption.value;
			saveFormData();
		}
	});
};

const getVehicleTypeLabel = (value) => {
	const option = vehicleTypeOptions.find(item => item.value === value);
	return option ? option.name : '';
};

// 燃料类型选择器
const showFuelTypePicker = () => {
	const itemList = fuelTypeOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = fuelTypeOptions[res.tapIndex];
			formData.value.fuel_type = selectedOption.value;
			saveFormData();
		}
	});
};

const getFuelTypeLabel = (value) => {
	const option = fuelTypeOptions.find(item => item.value === value);
	return option ? option.name : '';
};

// 变速箱选择器
const showTransmissionPicker = () => {
	const itemList = transmissionOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = transmissionOptions[res.tapIndex];
			formData.value.transmission = selectedOption.value;
			saveFormData();
		}
	});
};

const getTransmissionLabel = (value) => {
	const option = transmissionOptions.find(item => item.value === value);
	return option ? option.name : '';
};

// 座位数选择器
const showSeatsPicker = () => {
	const itemList = seatsOptions.map(item => item.name);
	uni.showActionSheet({
		itemList,
		success: (res) => {
			const selectedOption = seatsOptions[res.tapIndex];
			formData.value.seats = selectedOption.value;
			saveFormData();
		}
	});
};

const getSeatsLabel = (value) => {
	const option = seatsOptions.find(item => item.value === value);
	return option ? option.name : '';
};



// 提交数据
const handleSubmit = async () => {
	try {
		// 验证必填项
		if (!formData.value.brand) {
			return uni.showToast({
				title: '请填写车辆品牌',
				icon: 'none'
			});
		}
		
		if (!formData.value.model) {
			return uni.showToast({
				title: '请填写车辆型号',
				icon: 'none'
			});
		}
		
		if (!formData.value.guide_price) {
			return uni.showToast({
				title: '请填写指导价格',
				icon: 'none'
			});
		}
		
		if (!formData.value.actual_price) {
			return uni.showToast({
				title: '请填写成交价格',
				icon: 'none'
			});
		}

		submitLoading.value = true;
		
		// 获取客户手机号
		const businessData = uni.getStorageSync('business_draft') || {};
		if (!businessData.customer_phone) {
			return uni.showToast({
				title: '请先完成客户验证',
				icon: 'none'
			});
		}
		
		// 调用后端API保存车辆信息
		const response = await request.post('/business-applications/save-vehicle', {
			customer_phone: businessData.customer_phone,
			vehicle_type: formData.value.vehicle_type || '乘用车',
			brand: formData.value.brand,
			model: formData.value.model,
			series: formData.value.series || '',
			configuration: formData.value.config || '',
			year: parseInt(formData.value.year) || new Date().getFullYear(),
			fuel_type: formData.value.fuel_type || '',
			transmission: formData.value.transmission || '',
			seats: parseInt(formData.value.seats) || 5,
			guide_price: parseFloat(formData.value.guide_price),
			discount_amount: parseFloat(formData.value.discount_amount) || 0,
			actual_price: parseFloat(formData.value.actual_price),
			down_payment: parseFloat(formData.value.down_payment) || 0,
			loan_amount: parseFloat(formData.value.loan_amount) || 0,
			dealer_name: formData.value.dealer || '',
			dealer_contact: '',
			dealer_phone: ''
		});

		if (response.status_code === 200) {
			// 保存本地数据
			saveFormData();
			
			// 更新完成状态
			businessData.vehicle_completed = true;
			uni.setStorageSync('business_draft', businessData);

			uni.showToast({
				title: '车辆信息保存成功',
				icon: 'success'
			});

			// 跳转到附件上传页面
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/business/attachments'
				});
			}, 1000);
		} else {
			uni.showToast({
				title: response.message || '保存失败',
				icon: 'none'
			});
		}
		
	} catch (error) {
		console.error('保存车辆信息失败:', error);
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none'
		});
	} finally {
		submitLoading.value = false;
	}
};
</script>

<style lang="scss" scoped>
.business-vehicle {
	min-height: 100vh;
	background: #f3f3f3;
	padding-bottom: 120rpx;
}

.container {
	padding: 20rpx;
}

.page-title {
	padding: 40rpx 20rpx 20rpx;
	text-align: left;
	
	view:first-child {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 12rpx;
	}
	
	.desc {
		font-size: 28rpx;
		color: #666;
	}
}

.basic-card {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 24rpx;
	
	.card-info {
		padding: 32rpx 32rpx 16rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.card-container {
		padding: 32rpx;
		
		.card-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 32rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				width: 160rpx;
				font-size: 30rpx;
				color: #333;
				flex-shrink: 0;
				padding-top: 16rpx;
				
				.required {
					color: #ff4d4f;
				}
			}
			
			.input {
				flex: 1;
				height: 64rpx;
				border: 2rpx solid #d0d0d0;
				border-radius: 8rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				color: #333;
				background: #fff;
				
				&.select-input {
					color: #666;
				}
				
				&:disabled,
				&[readonly] {
					background: #f5f5f5;
					color: #999;
				}
			}
			
			.textarea {
				flex: 1;
				min-height: 120rpx;
				border: 2rpx solid #d0d0d0;
				border-radius: 8rpx;
				padding: 20rpx;
				font-size: 30rpx;
				color: #333;
				background: #fff;
				line-height: 1.4;
			}
			
			.select-wrapper {
				position: relative;
				flex: 1;
				
				.arrow-icon {
					position: absolute;
					right: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
}


</style> 