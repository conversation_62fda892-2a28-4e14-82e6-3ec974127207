# 第三方接口调用记录功能

## 概述

本功能为所有第三方接口调用提供统一的日志记录和监控，包括但不限于：
- e签宝接口（电子签章服务）
- 极证云接口（身份认证、风控查询）
- 车300接口（行驶证识别、车辆估值）
- 其他第三方服务接口

## 功能特性

1. **自动记录**: 所有第三方接口调用都会自动记录到数据库
2. **敏感信息脱敏**: 自动对敏感信息如密码、令牌、身份证号等进行脱敏处理
3. **性能监控**: 记录每次调用的耗时
4. **业务关联**: 支持关联业务ID和业务类型
5. **统计分析**: 提供接口调用成功率、失败原因等统计信息

## 数据库表结构

### third_party_api_logs 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| service_name | varchar(50) | 服务名称(如:esign,jizhengyun) |
| api_name | varchar(100) | 接口名称 |
| method | varchar(10) | 请求方法(GET/POST/PUT等) |
| request_url | text | 请求地址 |
| request_headers | json | 请求头(脱敏后) |
| request_params | json | 请求参数(脱敏后) |
| response_status | int | 响应状态码 |
| response_headers | json | 响应头 |
| response_body | json | 响应内容(脱敏后) |
| duration_ms | int | 请求耗时(毫秒) |
| error_message | text | 错误信息 |
| is_success | boolean | 是否成功 |
| business_id | bigint | 关联业务ID |
| business_type | varchar(50) | 业务类型 |
| request_time | timestamp | 请求时间 |

## 使用说明

### 1. 数据库迁移

首先执行数据库迁移创建表：

```bash
php artisan migrate
```

### 2. 在服务中使用

#### ESignService 示例

```php
use App\Services\ESignService;

// 创建签署流程时会自动记录API调用日志
$eSignService = new ESignService();
$flowId = $eSignService->createSignFlow([
    'application_id' => 123,  // 业务ID会自动记录
    'customer_name' => '张三',
    'customer_id_card' => '123456789012345678',
    // ...其他参数
]);
```

#### JiZhengYunService 示例

```php
use App\Services\JiZhengYunService;

// OCR识别时会自动记录API调用日志
$jzyService = new JiZhengYunService();
$result = $jzyService->idCardFrontOcr($imageBase64, $businessId);
```

#### Che300Service 示例

```php
use App\Services\Che300Service;

// 行驶证识别时会自动记录API调用日志
$che300Service = new Che300Service();
$result = $che300Service->analyzeDrivingLicense($imageUrl, $businessId);

// 车辆估值时会自动记录API调用日志
$valuationResult = $che300Service->getVehicleValuation([
    'vin' => 'LSGGG83K8AA000000',
    'mile' => 50000,
    'reg_date' => '2020-01-01',
    'business_id' => 123  // 业务ID会自动记录
]);
```

#### 手动记录日志

```php
use App\Services\ThirdPartyApiLogService;

ThirdPartyApiLogService::log(
    'custom_service',     // 服务名称
    '自定义接口',          // 接口名称
    'POST',              // 请求方法
    'https://api.example.com/test', // 请求地址
    ['Content-Type' => 'application/json'], // 请求头
    ['param1' => 'value1'], // 请求参数
    200,                 // 响应状态码
    [],                  // 响应头
    ['result' => 'success'], // 响应内容
    150,                 // 耗时(毫秒)
    null,                // 错误信息
    true,                // 是否成功
    456,                 // 业务ID
    'order'              // 业务类型
);
```

### 3. 查看统计信息

#### 查看所有服务统计

```bash
php artisan api:stats
```

#### 查看指定服务统计

```bash
# 查看e签宝服务统计
php artisan api:stats esign

# 查看极证云服务统计
php artisan api:stats jizhengyun

# 查看车300服务统计
php artisan api:stats che300
```

#### 按时间范围查询

```bash
php artisan api:stats esign --start=2024-01-01 --end=2024-01-31
```

#### 查看失败的调用

```bash
php artisan api:stats esign --failed --limit=20
```

### 4. 编程方式查询统计

```php
use App\Services\ThirdPartyApiLogService;

// 获取服务统计
$stats = ThirdPartyApiLogService::getServiceStats('esign', '2024-01-01', '2024-01-31');
/*
返回数据格式:
[
    'service_name' => 'esign',
    'total_calls' => 100,
    'success_calls' => 95,
    'failed_calls' => 5,
    'success_rate' => '95.00%',
    'period' => '2024-01-01 ~ 2024-01-31'
]
*/

// 获取失败的调用记录
$failedCalls = ThirdPartyApiLogService::getFailedCalls('esign', 10);
```

### 5. 使用Model查询

```php
use App\Models\ThirdPartyApiLog;

// 查询指定服务的调用记录
$logs = ThirdPartyApiLog::byService('esign')
    ->byTimeRange('2024-01-01', '2024-01-31')
    ->bySuccess(false)  // 只查询失败的
    ->orderBy('request_time', 'desc')
    ->paginate(20);

// 查询指定业务的调用记录
$businessLogs = ThirdPartyApiLog::byBusiness(123, 'contract')
    ->orderBy('request_time', 'desc')
    ->get();
```

## 脱敏规则

为了保护敏感信息，系统会自动对以下类型的数据进行脱敏：

### 请求头脱敏
- `authorization`
- `x-tsign-open-token`
- `x-api-key`
- `cookie`
- `set-cookie`

### 请求参数脱敏
- `password`
- `secret`
- `token`
- `key`
- `appSecret`
- `app_secret`
- `appCode` (车300应用码)
- `idNumber`
- `id_number`
- `cert_no`
- `mobile`
- `phone`
- `email`
- `vin` (车架号)
- `VIN` (车架号)
- `attachment` (图片base64数据)
- `driver_url` (行驶证图片URL)

### 响应内容脱敏
- `token`
- `accessToken`
- `access_token`
- `idNumber`
- `id_number`
- `cert_no`
- `mobile`
- `phone`
- `email`

### 脱敏规则
- 4位以下：全部用 `*` 替代
- 4位以上：显示前2位和后2位，中间用 `*` 替代
- 例：`***********` -> `13******78`

## 监控和告警

建议通过以下方式监控第三方接口调用：

1. **定期检查成功率**：通过统计命令或API检查各服务的成功率
2. **设置告警阈值**：当某个服务的失败率超过阈值时发送告警
3. **性能监控**：监控接口调用耗时，及时发现性能问题
4. **日志清理**：定期清理过期的日志数据，避免数据表过大

## 注意事项

1. **性能影响**：日志记录会增加少量性能开销，但不会影响业务流程
2. **存储空间**：根据调用频率合理规划存储空间
3. **敏感信息**：即使经过脱敏，仍需谨慎处理日志数据
4. **错误处理**：日志记录失败不会影响业务流程的正常执行

## 扩展开发

如需为其他第三方服务添加日志记录功能：

1. 在服务类中引入 `ThirdPartyApiLogService`
2. 创建类似 `makeHttpRequest` 的通用请求方法
3. 在调用第三方接口时使用该方法
4. 确保传递正确的服务名称和业务信息 