# 管理后台权限系统设计指南

## 概述

本权限系统支持四种角色类型的多层级权限管理：
1. **渠道（车商）** - 通过 `channel_users` 表管理
2. **销售（业务员）** - 通过 `users` 表管理（不能登录后台）
3. **金融公司（资方）** - 通过 `finance_users` 表管理
4. **平台管理员** - 通过 `admins` 表管理

## 表结构说明

### 核心权限表

1. **roles** - 角色表
   - 定义系统中的各种角色
   - 支持角色类型分类（platform、finance、channel、admin）

2. **menus** - 菜单表
   - 定义系统菜单结构
   - 支持多层级菜单

3. **permissions** - 权限表
   - 定义具体的权限项
   - 支持菜单权限和操作权限

4. **role_permissions** - 角色权限关联表
   - 定义角色拥有的权限

### 用户表

1. **admins** - 平台管理员表（原有）
2. **channel_users** - 渠道用户表（新增）
3. **finance_users** - 金融公司用户表（新增）
4. **users** - 销售业务员表（原有，不能登录后台）

### 关联表

1. **admin_roles** - 管理员角色关联表
2. **finance_companies** - 金融公司表
3. **channels** - 渠道表（原有）

## 角色权限设计

### 超级管理员 (super_admin)
- **类型**: platform
- **权限**: 所有菜单和功能的访问权限
- **说明**: 系统最高权限，用于系统维护和管理

### 平台管理员 (platform_admin)
- **类型**: platform
- **权限**: 除系统管理外的所有功能
- **包含菜单**: 
  - 首页、业务管理、客户管理、审批管理
  - 金融管理、渠道管理、报表统计

### 金融公司管理员 (finance_admin)
- **类型**: finance
- **权限**: 业务审批、风控管理相关功能
- **包含菜单**:
  - 首页、全部业务、客户列表、客户风险查询
  - 待我审批、抄送我的、我发起的
  - 金融产品、风控规则、业务报表、客户报表

### 金融公司操作员 (finance_operator)
- **类型**: finance
- **权限**: 基础业务操作功能
- **包含菜单**:
  - 首页、全部业务、客户列表、客户风险查询
  - 待我审批、抄送我的、业务报表

### 渠道管理员 (channel_admin)
- **类型**: channel
- **权限**: 渠道业务管理功能
- **包含菜单**:
  - 首页、全部业务、业务申请、客户列表
  - 销售线索、渠道列表、渠道用户
  - 业绩统计、渠道报表

### 渠道操作员 (channel_operator)
- **类型**: channel
- **权限**: 基础渠道操作功能
- **包含菜单**:
  - 首页、全部业务、业务申请
  - 客户列表、销售线索、渠道报表

## 菜单结构

```
├── 首页 (dashboard)
├── 业务管理 (business)
│   ├── 全部业务 (business_all)
│   ├── 业务申请 (business_application)
│   └── 合同管理 (business_contract)
├── 客户管理 (customer)
│   ├── 客户列表 (customer_list)
│   ├── 风险查询 (customer_risk)
│   └── 销售线索 (customer_leads)
├── 审批管理 (approval)
│   ├── 待我审批 (approval_pending)
│   ├── 抄送我的 (approval_cc)
│   ├── 我发起的 (approval_initiated)
│   └── 审批流程 (approval_workflow)
├── 金融管理 (finance)
│   ├── 金融产品 (finance_product)
│   ├── 风控规则 (finance_risk)
│   └── 公司管理 (finance_company)
├── 渠道管理 (channel)
│   ├── 渠道列表 (channel_list)
│   ├── 渠道用户 (channel_user)
│   └── 业绩统计 (channel_performance)
├── 报表统计 (report)
│   ├── 业务报表 (report_business)
│   ├── 客户报表 (report_customer)
│   └── 渠道报表 (report_channel)
└── 系统管理 (system)
    ├── 用户管理 (system_user)
    ├── 角色管理 (system_role)
    ├── 菜单管理 (system_menu)
    └── 系统设置 (system_setting)
```

## 默认账号

执行 SQL 后会创建以下测试账号（默认密码：password）：

### 平台管理员
- `admin` - 超级管理员
- `platform_admin` - 平台管理员
- `platform_ops` - 平台运营

### 金融公司账号
- `CITIC_BANK_admin` - 中信银行管理员
- `CITIC_BANK_operator` - 中信银行操作员
- `PING_AN_BANK_admin` - 平安银行管理员
- `PING_AN_BANK_operator` - 平安银行操作员
- `CMB_BANK_admin` - 招商银行管理员
- `CMB_BANK_operator` - 招商银行操作员

### 渠道账号
- `DIRECT_admin` - 直营渠道管理员
- `DIRECT_operator` - 直营渠道操作员
- `DEALER_A_admin` - 经销商A管理员
- `DEALER_A_operator` - 经销商A操作员
- 等等...

## 使用说明

### 1. 执行 SQL 文件
```bash
# 1. 先执行权限系统表结构
mysql -u username -p database_name < create_admin_permission_system.sql

# 2. 再执行测试数据
mysql -u username -p database_name < insert_admin_permission_test_data.sql
```

### 2. 后端实现要点

#### 权限验证中间件
```php
// 示例权限验证逻辑
public function checkPermission($user, $permission_code) {
    // 根据用户类型获取角色
    $roles = $this->getUserRoles($user);
    
    // 检查角色是否拥有该权限
    foreach ($roles as $role) {
        if ($this->roleHasPermission($role->id, $permission_code)) {
            return true;
        }
    }
    
    return false;
}
```

#### 菜单生成
```php
// 根据用户权限生成菜单
public function getUserMenus($user) {
    $roles = $this->getUserRoles($user);
    $permissions = $this->getRolePermissions($roles);
    
    return $this->buildMenuTree($permissions);
}
```

### 3. 前端实现要点

#### 路由权限控制
```javascript
// 根据用户权限过滤路由
const filterRoutes = (routes, permissions) => {
  return routes.filter(route => {
    if (route.meta && route.meta.permission) {
      return permissions.includes(route.meta.permission);
    }
    if (route.children) {
      route.children = filterRoutes(route.children, permissions);
    }
    return true;
  });
};
```

#### 菜单渲染
```javascript
// 动态菜单组件
const DynamicMenu = ({ menus, permissions }) => {
  return menus
    .filter(menu => permissions.includes(menu.code))
    .map(menu => <MenuItem key={menu.code} {...menu} />);
};
```

## 扩展说明

### 添加新角色
1. 在 `roles` 表中添加新角色
2. 在 `role_permissions` 表中分配权限
3. 创建对应的用户账号

### 添加新菜单
1. 在 `menus` 表中添加菜单
2. 在 `permissions` 表中添加对应权限
3. 在 `role_permissions` 表中分配给相应角色

### 添加新权限
1. 在 `permissions` 表中添加权限
2. 在 `role_permissions` 表中分配给相应角色
3. 在后端代码中添加权限验证逻辑

## 注意事项

1. **销售人员（users表）不能登录后台**，只能使用前端APP
2. **密码安全**：所有默认密码应在生产环境中修改
3. **权限检查**：每个API接口都应该进行权限验证
4. **数据隔离**：不同角色只能访问其权限范围内的数据
5. **日志记录**：建议记录用户操作日志用于审计

## 数据库关系图

```
admins ──────┐
             ├── admin_roles ──── roles ──── role_permissions ──── permissions ──── menus
             │                                                                        │
channel_users ──── channels                                                          │
             │                                                                        │
finance_users ──── finance_companies                                                 │
                                                                                      │
users (销售，不登录后台)                                                               │
                                                                                      │
business_applications ──────────────────────────────────────────────────────────────┘
``` 