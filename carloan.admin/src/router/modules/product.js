import Layout from '@/layout'
import PlaceholderPage from '@/views/PlaceholderPage'

const productRouter = {
  path: '/product',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Product',
  meta: {
    title: '产品管理',
    icon: 'el-icon-goods'
  },
  children: [
    {
      path: 'list',
      component: PlaceholderPage,
      name: 'ProductList',
      meta: { title: '产品列表' }
    },
    {
      path: 'rates',
      component: PlaceholderPage,
      name: 'ProductRates',
      meta: { title: '利率配置' }
    },
    {
      path: 'limits',
      component: PlaceholderPage,
      name: 'ProductLimits',
      meta: { title: '额度管理' }
    },
    {
      path: 'create',
      component: PlaceholderPage,
      name: 'ProductCreate',
      meta: { title: '新增产品', hidden: true }
    },
    {
      path: 'edit/:id',
      component: PlaceholderPage,
      name: 'ProductEdit',
      meta: { title: '编辑产品', hidden: true }
    },
    {
      path: 'detail/:id',
      component: PlaceholderPage,
      name: 'ProductDetail',
      meta: { title: '产品详情', hidden: true }
    }
  ]
}

export default productRouter
