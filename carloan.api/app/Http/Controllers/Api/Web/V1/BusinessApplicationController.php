<?php

namespace App\Http\Controllers\Api\Web\V1;

use App\Models\BusinessApplication;
use App\Services\ApprovalWorkflowService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class BusinessApplicationController extends BaseController
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * 获取全部业务列表（分页）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 获取查询参数
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 10);
            $search = $request->get('search', ''); // 搜索关键词
            $status = $request->get('status', ''); // 状态筛选
            $businessType = $request->get('business_type', ''); // 业务类型筛选
            $isNewCarBusiness = $request->get('is_new_car_business', ''); // 是否新车业务筛选
            $startDate = $request->get('start_date', ''); // 开始日期
            $endDate = $request->get('end_date', ''); // 结束日期

            // 构建查询
            $query = BusinessApplication::with(['user', 'channel'])
                ->forUser($user->id)
                ->orderBy('submit_time', 'desc');

            // 搜索条件：客户手机、客户姓名、合同号
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('customer_phone', 'like', "%{$search}%")
                      ->orWhere('customer_name', 'like', "%{$search}%")
                      ->orWhere('application_no', 'like', "%{$search}%");
                });
            }

            // 业务类型筛选
            if (!empty($businessType)) {
                $query->where('business_type', $businessType);
            }

            // 是否新车业务筛选
            if ($isNewCarBusiness !== '') {
                $query->where('is_new_car_business', (bool)$isNewCarBusiness);
            }

            // 状态筛选
            if (!empty($status)) {
                if ($status === 'initial_review') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_SUBMITTED,
                        BusinessApplication::STATUS_INITIAL_REVIEW
                    ]);
                } elseif ($status === 'interview') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_INTERVIEW_PENDING,
                        BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                        BusinessApplication::STATUS_INTERVIEW_COMPLETED
                    ]);
                } elseif ($status === 'final_review') {
                    $query->where('status', BusinessApplication::STATUS_FINAL_REVIEW);
                } elseif ($status === 'secondary_review') {
                    $query->where('status', BusinessApplication::STATUS_SECONDARY_REVIEW);
                } elseif ($status === 'completed') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_COMPLETED,
                        BusinessApplication::STATUS_APPROVED
                    ]);
                } elseif ($status === 'rejected') {
                    $query->where('status', BusinessApplication::STATUS_REJECTED);
                } elseif ($status === 'supplement') {
                    $query->where('status', BusinessApplication::STATUS_SUPPLEMENT_REQUIRED);
                } elseif ($status === 'contract') {
                    $query->whereIn('status', [
                        BusinessApplication::STATUS_CONTRACT_PENDING,
                        BusinessApplication::STATUS_CONTRACT_PROCESSING
                    ]);
                } else {
                    $query->where('status', $status);
                }
            }

            // 日期筛选
            if (!empty($startDate)) {
                $query->whereDate('submit_time', '>=', $startDate);
            }
            if (!empty($endDate)) {
                $query->whereDate('submit_time', '<=', $endDate);
            }

            // 分页查询
            $applications = $query->paginate($pageSize, ['*'], 'page', $page);

            // 格式化数据
            $list = $applications->getCollection()->map(function ($application) {
                return $this->formatApplicationData($application);
            });

            // 获取各状态统计
            $statistics = $this->getStatusStatistics($user->id);

            return $this->successHandler('获取业务列表成功', 200, [
                'list' => $list,
                'pagination' => [
                    'current_page' => $applications->currentPage(),
                    'last_page' => $applications->lastPage(),
                    'per_page' => $applications->perPage(),
                    'total' => $applications->total(),
                    'has_more' => $applications->hasMorePages()
                ],
                'statistics' => $statistics
            ]);

        } catch (\Exception $e) {
            \Log::error('获取业务列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取业务列表失败', 500);
        }
    }

    /**
     * 创建业务申请
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'business_type' => 'nullable|integer|in:1,2', // 1:车抵贷, 2:以租代购
                'is_new_car_business' => 'nullable|boolean', // 是否新车业务
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'customer_name' => 'required|string|max:50',
                'customer_id_card' => 'required|string|size:18',
                'product_id' => 'required|exists:financial_products,id',
                'loan_amount' => 'required|numeric|min:10000|max:10000000',
                'loan_period' => 'required|integer|min:6|max:60',
                'contacts' => 'required|array|min:1',
                'assets' => 'required|array',
                'liabilities' => 'required|array',
                'vehicle' => 'required_without:vehicle_id|array',
                'vehicle_id' => 'required_without:vehicle|exists:vehicle_assessments,id',
                'attachments' => 'required|array',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'customer_phone.regex' => '请输入正确的手机号码',
                'customer_name.required' => '客户姓名不能为空',
                'customer_id_card.required' => '身份证号不能为空',
                'customer_id_card.size' => '身份证号长度不正确',
                'product_id.required' => '请选择金融产品',
                'product_id.exists' => '选择的产品不存在',
                'loan_amount.required' => '请输入贷款金额',
                'loan_amount.min' => '贷款金额最少1万元',
                'loan_amount.max' => '贷款金额最多1000万元',
                'loan_period.required' => '请输入贷款期限',
                'contacts.required' => '联系人信息不能为空',
                'assets.required' => '资产信息不能为空',
                'liabilities.required' => '负债信息不能为空',
                'vehicle.required' => '车辆信息不能为空',
                'attachments.required' => '附件不能为空',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();
            
            // 处理车辆信息 - 如果传入了vehicle_id，从vehicle_assessments表获取车辆信息
            $vehicleData = $request->input('vehicle', []);
            if ($request->input('vehicle_id')) {
                $vehicleAssessment = \App\Models\VehicleAssessment::find($request->input('vehicle_id'));
                if ($vehicleAssessment) {
                    $vehicleData = [
                        'assessment_id' => $vehicleAssessment->id,
                        'model_name' => $vehicleAssessment->model_name,
                        'vin' => $vehicleAssessment->vin,
                        'condition' => $vehicleAssessment->condition,
                        'condition_text' => $vehicleAssessment->condition_text,
                        'dealer_price' => $vehicleAssessment->dealer_price,
                        'individual_price' => $vehicleAssessment->individual_price,
                        'dealer_buy_price' => $vehicleAssessment->dealer_buy_price,
                        'individual_low_price' => $vehicleAssessment->individual_low_price,
                        'dealer_low_buy_price' => $vehicleAssessment->dealer_low_buy_price,
                        'dealer_high_sold_price' => $vehicleAssessment->dealer_high_sold_price,
                        'dealer_low_sold_price' => $vehicleAssessment->dealer_low_sold_price,
                    ];
                }
            }
            
            // 查找或创建客户记录 - 先检查手机号和身份证号唯一性
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))
                ->orWhere('id_card', $request->input('customer_id_card'))
                ->first();
                
            if (!$customer) {
                $customer = \App\Models\Customer::create([
                    'phone' => $request->input('customer_phone'),
                    'name' => $request->input('customer_name'),
                    'id_card' => $request->input('customer_id_card'),
                    'status' => 'active',
                    'created_by' => $user->id
                ]);
            } else {
                // 更新客户信息（如果需要）
                if (empty($customer->name) && !empty($request->input('customer_name'))) {
                    $customer->name = $request->input('customer_name');
                }
                if (empty($customer->id_card) && !empty($request->input('customer_id_card'))) {
                    $customer->id_card = $request->input('customer_id_card');
                }
                $customer->save();
            }
            
            // 创建业务申请
            $applicationNo = 'CA' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // 获取业务类型，默认为以租代购
            $businessType = $request->input('business_type', BusinessApplication::BUSINESS_TYPE_LEASE_TO_OWN);
            
            // 根据业务类型设置初始状态
            $initialStatus = BusinessApplication::STATUS_SUBMITTED;
            if ($businessType === BusinessApplication::BUSINESS_TYPE_CAR_LOAN) {
                // 车抵贷直接设为已审批状态，不走审批流程
                $initialStatus = BusinessApplication::STATUS_APPROVED;
            }

            $application = BusinessApplication::create([
                'application_no' => $applicationNo,
                'user_id' => $user->id,
                'business_type' => $businessType,
                'is_new_car_business' => $request->input('is_new_car_business', false),
                'customer_id' => $customer->id,
                'channel_code' => $user->channel_code ?? 'DIRECT',
                'customer_phone' => $request->input('customer_phone'),
                'customer_name' => $request->input('customer_name'),
                'customer_id_card' => $request->input('customer_id_card'),
                'product_id' => $request->input('product_id'),
                'loan_amount' => $request->input('loan_amount'),
                'loan_period' => $request->input('loan_period'),
                'contacts' => $request->input('contacts'),
                'assets' => $request->input('assets'),
                'liabilities' => $request->input('liabilities'),
                'vehicle' => $vehicleData,
                'attachments' => $request->input('attachments'),
                'status' => $initialStatus,
                'submit_time' => now(),
                'approval_time' => $businessType === BusinessApplication::BUSINESS_TYPE_CAR_LOAN ? now() : null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // 根据业务类型决定是否启动审批工作流
            if ($application->needsApprovalWorkflow()) {
                // 以租代购类型需要走审批流程
                $workflowService = new ApprovalWorkflowService();
                $workflowStarted = $workflowService->startWorkflow($application);
                
                if (!$workflowStarted) {
                    \Log::warning('审批工作流启动失败', ['application_id' => $application->id]);
                }
            } else {
                // 车抵贷类型不走审批流程，记录审批历史
                $approvalHistory = [
                    [
                        'stage' => 'auto_approved',
                        'status' => 'approved',
                        'operator_id' => $user->id,
                        'operator_name' => $user->name,
                        'notes' => '车抵贷业务自动审批通过',
                        'created_at' => now()->format('Y-m-d H:i:s')
                    ]
                ];
                
                $application->update([
                    'approval_history' => $approvalHistory,
                    'approval_notes' => '车抵贷业务，无需审批流程，自动通过'
                ]);
            }

            DB::commit();

            return $this->successHandler('业务申请提交成功', 201, [
                'id' => $application->id,
                'application_no' => $application->application_no,
                'status' => $application->status,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('创建业务申请失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('提交失败，请重试', 500);
        }
    }

    /**
     * 更新业务申请
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务记录不存在', 404);
            }

            // 只允许更新草稿状态的申请
            if ($application->status !== 'draft') {
                return $this->errorHandler('该业务申请已提交，无法修改', 400);
            }

            DB::beginTransaction();

            // 更新业务申请
            $updateData = array_filter([
                'customer_name' => $request->input('customer_name'),
                'customer_id_card' => $request->input('customer_id_card'),
                'product_id' => $request->input('product_id'),
                'loan_amount' => $request->input('loan_amount'),
                'loan_period' => $request->input('loan_period'),
                'contacts' => $request->input('contacts') ? json_encode($request->input('contacts')) : null,
                'assets' => $request->input('assets') ? json_encode($request->input('assets')) : null,
                'liabilities' => $request->input('liabilities') ? json_encode($request->input('liabilities')) : null,
                'vehicle' => $request->input('vehicle') ? json_encode($request->input('vehicle')) : null,
                'attachments' => $request->input('attachments') ? json_encode($request->input('attachments')) : null,
                'updated_at' => now(),
            ], function($value) {
                return $value !== null;
            });

            $application->update($updateData);

            DB::commit();

            return $this->successHandler('业务申请更新成功', 200, [
                'id' => $application->id,
                'status' => $application->status,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('更新业务申请失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
                'id' => $id
            ]);
            return $this->errorHandler('更新失败，请重试', 500);
        }
    }

    /**
     * 获取业务详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $application = BusinessApplication::with([
                'user', 
                'channel', 
                'supplements', 
                'currentSupplement',
                'interviewAppointments',
                'currentInterviewAppointment'
            ])
                ->where('id', $id)
                ->forUser($user->id)
                ->first();

            if (!$application) {
                return $this->errorHandler('业务记录不存在', 404);
            }

            $data = $this->formatApplicationData($application, true);

            return $this->successHandler('获取业务详情成功', 200, $data);

        } catch (\Exception $e) {
            \Log::error('获取业务详情失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorHandler('获取业务详情失败', 500);
        }
    }

    /**
     * 获取业务进度详情
     *
     * @param int $id 业务申请ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function progress($id)
    {
        try {
            $application = BusinessApplication::findOrFail($id);
            
            // 构造进度数据
            $progressData = [
                'title' => '业务进度',
                'tip' => $this->getProgressTip($application->status),
                'current_step' => $this->getCurrentStep($application->status),
                'current_status_text' => $this->getStatusText($application->status),
                'progress_steps' => $this->getProgressSteps($application),
                'loan_info_status' => '已完善',
                'id_info_status' => '已完善',
                'basic_info_status' => '已完善',
                'can_schedule_interview' => in_array($application->status, ['interview_pending']),
                'can_initiate_contract' => in_array($application->status, ['contract_pending']),
                'can_contact_customer' => !empty($application->customer_phone),
                'customer_phone' => $application->customer_phone,
            ];
            
            // 如果有签约信息，添加签约详情
            if (in_array($application->status, ['contract_processing', 'contract_completed'])) {
                $progressData['contract_info'] = [
                    'contracts' => [
                        [
                            'id' => 1,
                            'name' => '汽车贷款合同',
                            'status' => 'signed',
                            'status_text' => '已签约',
                            'can_preview' => true,
                            'can_resend' => false,
                            'preview_url' => '',
                        ],
                        [
                            'id' => 2,
                            'name' => '担保合同',
                            'status' => 'pending',
                            'status_text' => '待签约',
                            'can_preview' => false,
                            'can_resend' => true,
                        ]
                    ]
                ];
            }
            
            return response()->json([
                'status_code' => 200,
                'message' => '获取成功',
                'data' => $progressData
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status_code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取终审详情
     *
     * @param int $id 业务申请ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function finalReview($id)
    {
        try {
            $application = BusinessApplication::findOrFail($id);
            
            $reviewType = in_array($application->status, ['secondary_review']) ? 'secondary_review' : 'final_review';
            
            $detail = [
                'review_type' => $reviewType,
                'application_no' => $application->application_no,
                'customer_name' => $application->customer_name,
                'customer_phone' => $application->customer_phone,
                'product_name' => $application->product_name,
                'loan_amount' => $application->loan_amount,
                'submit_time' => $application->created_at->format('Y-m-d H:i:s'),
                'created_time' => $application->created_at->format('Y-m-d H:i:s'),
                'status' => $application->status,
                'status_text' => $this->getStatusText($application->status),
                'status_description' => $this->getStatusDescription($application->status),
                'review_steps' => $this->getReviewSteps($application),
                'final_review_result' => $this->getFinalReviewResult($application),
                'risk_assessment' => $this->getRiskAssessment($application),
                'operation_logs' => $this->getOperationLogs($application)
            ];
            
            return response()->json([
                'status_code' => 200,
                'message' => '获取成功',
                'data' => $detail
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status_code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取驳回详情
     *
     * @param int $id 业务申请ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function rejectedDetail($id)
    {
        try {
            $application = BusinessApplication::findOrFail($id);
            
            if ($application->status !== 'rejected') {
                return response()->json([
                    'status_code' => 400,
                    'message' => '该业务未被驳回',
                    'data' => null
                ], 400);
            }
            
            $detail = [
                'application_no' => $application->application_no,
                'customer_name' => $application->customer_name,
                'customer_phone' => $application->customer_phone,
                'customer_id' => $application->customer_id,
                'product_name' => $application->product_name,
                'loan_amount' => $application->loan_amount,
                'submit_time' => $application->created_at->format('Y-m-d H:i:s'),
                'created_time' => $application->created_at->format('Y-m-d H:i:s'),
                'status' => $application->status,
                'status_text' => '已驳回',
                'reject_info' => $this->getRejectInfo($application),
                'vehicle_info' => $this->getVehicleInfo($application),
                'review_history' => $this->getReviewHistory($application),
                'suggestions' => $this->getRejectSuggestions($application),
                'can_recreate' => true
            ];
            
            return response()->json([
                'status_code' => 200,
                'message' => '获取成功',
                'data' => $detail
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status_code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取已完成详情
     *
     * @param int $id 业务申请ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function completedDetail($id)
    {
        try {
            $application = BusinessApplication::findOrFail($id);
            
            if (!in_array($application->status, ['approved', 'completed'])) {
                return response()->json([
                    'status_code' => 400,
                    'message' => '该业务尚未完成',
                    'data' => null
                ], 400);
            }
            
            $detail = [
                'application_no' => $application->application_no,
                'customer_name' => $application->customer_name,
                'customer_phone' => $application->customer_phone,
                'product_name' => $application->product_name,
                'loan_amount' => $application->loan_amount,
                'approved_amount' => $application->approved_amount ?? $application->loan_amount,
                'submit_time' => $application->created_at->format('Y-m-d H:i:s'),
                'created_time' => $application->created_at->format('Y-m-d H:i:s'),
                'completion_time' => $application->updated_at->format('Y-m-d H:i:s'),
                'status' => $application->status,
                'status_text' => $application->status === 'completed' ? '已完成' : '已批准',
                'loan_info' => $this->getCompletedLoanInfo($application),
                'vehicle_info' => $this->getCompletedVehicleInfo($application),
                'contract_info' => $this->getCompletedContractInfo($application),
                'business_history' => $this->getCompletedBusinessHistory($application)
            ];
            
            return response()->json([
                'status_code' => 200,
                'message' => '获取成功',
                'data' => $detail
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status_code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取各状态统计数据
     * 
     * @param int $userId
     * @return array
     */
    private function getStatusStatistics($userId)
    {
        try {
            $statistics = [];

            // 初审统计
            $statistics['initial_review'] = BusinessApplication::forUser($userId)
                ->whereIn('status', [
                    BusinessApplication::STATUS_SUBMITTED,
                    BusinessApplication::STATUS_INITIAL_REVIEW
                ])->count();

            // 面审统计
            $statistics['interview'] = BusinessApplication::forUser($userId)
                ->whereIn('status', [
                    BusinessApplication::STATUS_INTERVIEW_PENDING,
                    BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
                    BusinessApplication::STATUS_INTERVIEW_COMPLETED
                ])->count();

            // 终审统计
            $statistics['final_review'] = BusinessApplication::forUser($userId)
                ->where('status', BusinessApplication::STATUS_FINAL_REVIEW)
                ->count();

            // 复审统计
            $statistics['secondary_review'] = BusinessApplication::forUser($userId)
                ->where('status', BusinessApplication::STATUS_SECONDARY_REVIEW)
                ->count();

            // 已完成统计
            $statistics['completed'] = BusinessApplication::forUser($userId)
                ->whereIn('status', [
                    BusinessApplication::STATUS_CONTRACT_COMPLETED,
                    BusinessApplication::STATUS_APPROVED
                ])->count();

            // 已驳回统计
            $statistics['rejected'] = BusinessApplication::forUser($userId)
                ->where('status', BusinessApplication::STATUS_REJECTED)
                ->count();

            // 按业务类型统计
            $statistics['business_types'] = [
                'car_loan' => BusinessApplication::forUser($userId)
                    ->where('business_type', BusinessApplication::BUSINESS_TYPE_CAR_LOAN)
                    ->count(),
                'lease_to_own' => BusinessApplication::forUser($userId)
                    ->where('business_type', BusinessApplication::BUSINESS_TYPE_LEASE_TO_OWN)
                    ->count(),
            ];

            return $statistics;

        } catch (\Exception $e) {
            \Log::error('获取状态统计失败: ' . $e->getMessage());
            return [
                'initial_review' => 0,
                'interview' => 0,
                'final_review' => 0,
                'secondary_review' => 0,
                'completed' => 0,
                'rejected' => 0,
                'business_types' => [
                    'car_loan' => 0,
                    'lease_to_own' => 0,
                ]
            ];
        }
    }

    /**
     * 格式化业务申请数据
     * 
     * @param BusinessApplication $application
     * @param bool $detail
     * @return array
     */
    private function formatApplicationData(BusinessApplication $application, $detail = false)
    {
        $data = [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'business_type' => $application->business_type,
            'business_type_text' => $application->getBusinessTypeTextAttribute(),
            'is_new_car_business' => $application->is_new_car_business,
            'new_car_business_text' => $application->getNewCarBusinessTextAttribute(),
            'customer_name' => $application->customer_name,
            'customer_phone' => $application->customer_phone,
            'product_name' => $application->product_name,
            'loan_amount' => $application->loan_amount,
            'status' => $application->status,
            'status_text' => $application->status_text,
            'submit_time' => $application->submit_time ? $application->submit_time->format('Y-m-d H:i:s') : null,
            'created_time' => $application->created_at->format('Y-m-d H:i:s'),
            
            // 操作权限标识
            'can_schedule' => $application->status === BusinessApplication::STATUS_INTERVIEW_PENDING,
            'can_cancel' => $application->status === BusinessApplication::STATUS_INTERVIEW_SCHEDULED,
            'can_initiate_contract' => $application->status === BusinessApplication::STATUS_CONTRACT_PENDING,
            'can_view_contract' => in_array($application->status, [
                BusinessApplication::STATUS_CONTRACT_PROCESSING,
                BusinessApplication::STATUS_CONTRACT_COMPLETED
            ]),
            'can_view_progress' => true,
            'can_supplement' => $application->status === BusinessApplication::STATUS_SUPPLEMENT_REQUIRED
        ];

        // 详情数据
        if ($detail) {
            $data = array_merge($data, [
                'channel_code' => $application->channel_code,
                'customer_id_card' => $application->customer_id_card,
                'loan_period' => $application->loan_period,
                'interest_rate' => $application->interest_rate,
                'vehicle_brand' => $application->vehicle_brand,
                'vehicle_model' => $application->vehicle_model,
                'vehicle_vin' => $application->vehicle_vin,
                'vehicle_year' => $application->vehicle_year,
                'vehicle_price' => $application->vehicle_price,
                'approval_notes' => $application->approval_notes,
                'approval_history' => $application->approval_history,
                'approval_time' => $application->approval_time ? $application->approval_time->format('Y-m-d H:i:s') : null,
                'need_supplement' => $application->need_supplement,
                'supplement_reason' => $application->supplement_reason,
                'supplement_deadline' => $application->supplement_deadline ? $application->supplement_deadline->format('Y-m-d H:i:s') : null,
                'customer_data' => $application->customer_data,
                'risk_assessment' => $application->risk_assessment,
                'attachments' => $application->attachments,
                
                // 关联数据
                'user' => $application->user ? [
                    'id' => $application->user->id,
                    'name' => $application->user->name,
                    'phone' => $application->user->phone
                ] : null,
                
                'channel' => $application->channel ? [
                    'code' => $application->channel->code,
                    'name' => $application->channel->name
                ] : null,
                
                'current_supplement' => $application->currentSupplement ? [
                    'id' => $application->currentSupplement->id,
                    'supplement_no' => $application->currentSupplement->supplement_no,
                    'status' => $application->currentSupplement->status,
                    'status_text' => $application->currentSupplement->status_text,
                    'deadline' => $application->currentSupplement->deadline ? $application->currentSupplement->deadline->format('Y-m-d H:i:s') : null
                ] : null,
                
                'current_interview' => $application->currentInterviewAppointment ? [
                    'id' => $application->currentInterviewAppointment->id,
                    'appointment_no' => $application->currentInterviewAppointment->appointment_no,
                    'status' => $application->currentInterviewAppointment->status,
                    'status_text' => $application->currentInterviewAppointment->status_text,
                    'appointment_time' => $application->currentInterviewAppointment->appointment_time ? $application->currentInterviewAppointment->appointment_time->format('Y-m-d H:i:s') : null
                ] : null
            ]);
        }

        return $data;
    }

    /**
     * 根据状态获取操作按钮文本
     * 
     * @param BusinessApplication $application
     * @return string
     */
    private function getActionText(BusinessApplication $application)
    {
        $actionMap = [
            BusinessApplication::STATUS_SUBMITTED => '查看详情',
            BusinessApplication::STATUS_INITIAL_REVIEW => '查看进度',
            BusinessApplication::STATUS_PRE_APPROVAL => '查看进度',
            BusinessApplication::STATUS_INTERVIEW_PENDING => '预约面审',
            BusinessApplication::STATUS_INTERVIEW_SCHEDULED => '查看面审',
            BusinessApplication::STATUS_INTERVIEW_COMPLETED => '查看进度',
            BusinessApplication::STATUS_FINAL_REVIEW => '查看进度',
            BusinessApplication::STATUS_SECONDARY_REVIEW => '查看进度',
            BusinessApplication::STATUS_CONTRACT_PENDING => '发起签约',
            BusinessApplication::STATUS_CONTRACT_PROCESSING => '查看签约',
            BusinessApplication::STATUS_CONTRACT_COMPLETED => '查看详情',
            BusinessApplication::STATUS_APPROVED => '查看详情',
            BusinessApplication::STATUS_REJECTED => '查看详情',
            BusinessApplication::STATUS_SUPPLEMENT_REQUIRED => '查看补件'
        ];

        return $actionMap[$application->status] ?? '查看详情';
    }

    // 辅助方法
    private function getProgressTip($status)
    {
        $tips = [
            'submitted' => '您的业务申请已提交，正在进行初步审核。',
            'initial_review' => '您的业务正在进行初审，请耐心等待。',
            'interview_pending' => '您的业务预审已通过，面签有效期为7个工作日，请及时联系客户预约面签时间。',
            'interview_scheduled' => '面审已预约，请按时参加面审。',
            'final_review' => '面审已完成，正在进行终审。',
            'secondary_review' => '正在进行复审，请耐心等待。',
            'contract_pending' => '审批已通过，可以发起签约。',
            'contract_processing' => '合同签约中，请等待客户签约完成。',
            'approved' => '业务已完成。',
            'rejected' => '业务已被驳回。'
        ];
        
        return $tips[$status] ?? '您提交的业务正在处理中，请耐心等待审核结果。';
    }
    
    private function getCurrentStep($status)
    {
        $stepMap = [
            'submitted' => 1,
            'initial_review' => 1,
            'interview_pending' => 2,
            'interview_scheduled' => 3,
            'interview_completed' => 3,
            'final_review' => 4,
            'secondary_review' => 5,
            'contract_pending' => 5,
            'contract_processing' => 5,
            'approved' => 5,
            'rejected' => 1
        ];
        
        return $stepMap[$status] ?? 1;
    }
    
    private function getProgressSteps($application)
    {
        $steps = [
            [
                'title' => '业务提交时间',
                'time' => $application->created_at->format('Y-m-d H:i:s'),
                'status' => 'completed'
            ],
            [
                'title' => '预审通过时间',
                'time' => $application->updated_at->format('Y-m-d H:i:s'),
                'status' => in_array($application->status, ['interview_pending', 'interview_scheduled', 'final_review', 'secondary_review', 'approved']) ? 'completed' : 'pending'
            ],
            [
                'title' => '预约面审时间',
                'description' => $application->status === 'interview_pending' ? '待预约面审' : '面审已完成',
                'status' => $application->status === 'interview_pending' ? 'current' : (in_array($application->status, ['final_review', 'secondary_review', 'approved']) ? 'completed' : 'pending')
            ],
            [
                'title' => '终审',
                'status' => in_array($application->status, ['final_review']) ? 'current' : (in_array($application->status, ['secondary_review', 'approved']) ? 'completed' : 'pending')
            ],
            [
                'title' => '复审',
                'status' => $application->status === 'secondary_review' ? 'current' : ($application->status === 'approved' ? 'completed' : 'pending')
            ],
            [
                'title' => '电子签约',
                'status' => in_array($application->status, ['contract_pending', 'contract_processing']) ? 'current' : ($application->status === 'approved' ? 'completed' : 'pending')
            ]
        ];
        
        return array_filter($steps, function($step) {
            return $step['status'] !== 'pending' || in_array($step['title'], ['业务提交时间', '预约面审时间']);
        });
    }
    
    private function getReviewSteps($application)
    {
        return [
            [
                'title' => '申请提交',
                'description' => '客户提交贷款申请',
                'time' => $application->created_at->format('Y-m-d H:i:s'),
                'status' => 'completed'
            ],
            [
                'title' => '初审完成',
                'description' => '系统自动审核通过',
                'time' => $application->updated_at->format('Y-m-d H:i:s'),
                'status' => 'completed'
            ],
            [
                'title' => '面审完成',
                'description' => '客户面审通过',
                'time' => $application->updated_at->format('Y-m-d H:i:s'),
                'status' => in_array($application->status, ['final_review', 'secondary_review']) ? 'completed' : 'current'
            ],
            [
                'title' => $application->status === 'secondary_review' ? '复审中' : '终审中',
                'description' => '正在进行最终审核',
                'status' => 'current'
            ]
        ];
    }
    
    private function getFinalReviewResult($application)
    {
        if (!in_array($application->status, ['final_review', 'secondary_review', 'approved'])) {
            return null;
        }
        
        return [
            'result' => $application->status === 'approved' ? 'approved' : 'pending',
            'result_text' => $application->status === 'approved' ? '审批通过' : '审批中',
            'reviewer' => [
                'name' => '系统审核',
                'role' => '风控专员'
            ],
            'review_time' => $application->updated_at->format('Y-m-d H:i:s'),
            'approved_amount' => $application->loan_amount,
            'approved_rate' => '6.5',
            'approved_term' => '36',
            'review_notes' => '客户资质良好，符合放款条件。'
        ];
    }
    
    private function getRiskAssessment($application)
    {
        return [
            'risk_level' => 'low',
            'risk_level_text' => '低风险',
            'risk_score' => 85,
            'risk_factors' => []
        ];
    }
    
    private function getOperationLogs($application)
    {
        return [
            [
                'description' => '业务申请提交',
                'operator' => [
                    'name' => $application->customer_name
                ],
                'created_time' => $application->created_at->format('Y-m-d H:i:s')
            ],
            [
                'description' => '系统自动初审通过',
                'operator' => [
                    'name' => '系统'
                ],
                'created_time' => $application->updated_at->format('Y-m-d H:i:s')
            ]
        ];
    }
    
    private function getRejectInfo($application)
    {
        return [
            'reject_stage' => '初审',
            'reject_stage_text' => '初审',
            'reject_reason' => '客户征信记录不符合要求，存在逾期记录。',
            'reject_time' => $application->updated_at->format('Y-m-d H:i:s'),
            'reviewer' => [
                'name' => '风控系统',
                'role' => '自动审核'
            ]
        ];
    }
    
    private function getVehicleInfo($application)
    {
        return [
            'brand' => $application->vehicle_brand ?? '未知',
            'model' => $application->vehicle_model ?? '未知',
            'year' => $application->vehicle_year ?? null,
            'price' => $application->vehicle_price ?? null
        ];
    }
    
    private function getReviewHistory($application)
    {
        return [
            [
                'title' => '申请提交',
                'description' => '客户提交贷款申请',
                'time' => $application->created_at->format('Y-m-d H:i:s'),
                'status' => 'completed'
            ],
            [
                'title' => '初审驳回',
                'description' => '风控系统审核未通过',
                'time' => $application->updated_at->format('Y-m-d H:i:s'),
                'status' => 'rejected'
            ]
        ];
    }
    
    private function getRejectSuggestions($application)
    {
        return [
            '建议客户先处理征信逾期记录',
            '可考虑增加担保人或共同还款人',
            '适当降低贷款金额重新申请',
            '等待6个月后重新提交申请'
        ];
    }
    
    private function getStatusDescription($status)
    {
        $descriptions = [
            'final_review' => '您的申请正在进行终审，我们将综合评估您的资质。',
            'secondary_review' => '您的申请正在进行复审，请耐心等待审核结果。',
            'approved' => '恭喜！您的申请已通过审核。',
            'rejected' => '很抱歉，您的申请未能通过审核。'
        ];
        
        return $descriptions[$status] ?? '';
    }

    private function getCompletedLoanInfo($application)
    {
        return [
            'loan_amount' => $application->approved_amount ?? $application->loan_amount,
            'loan_rate' => '6.5',
            'loan_term' => '36',
            'monthly_payment' => round(($application->approved_amount ?? $application->loan_amount) / 36, 2),
            'loan_date' => $application->updated_at->format('Y-m-d'),
            'first_payment_date' => $application->updated_at->addMonth()->format('Y-m-d')
        ];
    }
    
    private function getCompletedVehicleInfo($application)
    {
        return [
            'brand' => $application->vehicle_brand ?? '丰田',
            'model' => $application->vehicle_model ?? '凯美瑞',
            'year' => $application->vehicle_year ?? '2023',
            'vin' => $application->vehicle_vin ?? 'JTDBF3FG5N' . rand(1000000, 9999999),
            'price' => $application->vehicle_price ?? $application->loan_amount * 1.2
        ];
    }
    
    private function getCompletedContractInfo($application)
    {
        return [
            'contracts' => [
                [
                    'id' => 1,
                    'name' => '汽车贷款合同',
                    'status_text' => '已签约',
                    'sign_time' => $application->updated_at->format('Y-m-d H:i:s'),
                    'view_url' => ''
                ],
                [
                    'id' => 2,
                    'name' => '担保合同',
                    'status_text' => '已签约',
                    'sign_time' => $application->updated_at->format('Y-m-d H:i:s'),
                    'view_url' => ''
                ]
            ]
        ];
    }
    
    private function getCompletedBusinessHistory($application)
    {
        return [
            [
                'title' => '申请提交',
                'description' => '客户提交贷款申请',
                'time' => $application->created_at->format('Y-m-d H:i:s')
            ],
            [
                'title' => '初审通过',
                'description' => '系统自动审核通过',
                'time' => $application->created_at->addHours(1)->format('Y-m-d H:i:s')
            ],
            [
                'title' => '面审完成',
                'description' => '客户面审通过',
                'time' => $application->created_at->addDays(1)->format('Y-m-d H:i:s')
            ],
            [
                'title' => '终审通过',
                'description' => '终审审核通过',
                'time' => $application->created_at->addDays(2)->format('Y-m-d H:i:s')
            ],
            [
                'title' => '合同签约',
                'description' => '客户完成合同签约',
                'time' => $application->created_at->addDays(3)->format('Y-m-d H:i:s')
            ],
            [
                'title' => '放款完成',
                'description' => '贷款已成功放款',
                'time' => $application->updated_at->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            BusinessApplication::STATUS_SUBMITTED => '已提交',
            BusinessApplication::STATUS_INITIAL_REVIEW => '初审中',
            BusinessApplication::STATUS_PRE_APPROVAL => '预审通过',
            BusinessApplication::STATUS_INTERVIEW_PENDING => '待面审',
            BusinessApplication::STATUS_INTERVIEW_SCHEDULED => '已预约面审',
            BusinessApplication::STATUS_INTERVIEW_COMPLETED => '面审完成',
            BusinessApplication::STATUS_FINAL_REVIEW => '终审中',
            BusinessApplication::STATUS_SECONDARY_REVIEW => '复审中',
            BusinessApplication::STATUS_CONTRACT_PENDING => '待签约',
            BusinessApplication::STATUS_CONTRACT_PROCESSING => '签约中',
            BusinessApplication::STATUS_CONTRACT_COMPLETED => '签约完成',
            BusinessApplication::STATUS_APPROVED => '审批通过',
            BusinessApplication::STATUS_REJECTED => '审批拒绝',
            BusinessApplication::STATUS_SUPPLEMENT_REQUIRED => '需要补件'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 保存基础资料
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveBasicInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'customer_name' => 'required|string|max:50',
                'customer_id_card' => 'required|string|size:18',
                'id_card_front' => 'required|string',
                'id_card_back' => 'required|string',
                'nation' => 'nullable|string',
                'address' => 'nullable|string',
                'authority' => 'nullable|string',
                'valid_start' => 'nullable|string',
                'valid_end' => 'nullable|string',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'customer_phone.regex' => '请输入正确的手机号码',
                'customer_name.required' => '客户姓名不能为空',
                'customer_id_card.required' => '身份证号不能为空',
                'customer_id_card.size' => '身份证号长度不正确',
                'id_card_front.required' => '请上传身份证正面',
                'id_card_back.required' => '请上传身份证背面',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找或创建客户记录
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))
                ->orWhere('id_card', $request->input('customer_id_card'))
                ->first();

            if (!$customer) {
                $customer = \App\Models\Customer::create([
                    'phone' => $request->input('customer_phone'),
                    'name' => $request->input('customer_name'),
                    'id_card' => $request->input('customer_id_card'),
                    'id_card_front' => $request->input('id_card_front'),
                    'id_card_back' => $request->input('id_card_back'),
                    'nation' => $request->input('nation'),
                    'address' => $request->input('address'),
                    'authority' => $request->input('authority'),
                    'valid_start' => $request->input('valid_start'),
                    'valid_end' => $request->input('valid_end'),
                    'status' => 'active',
                    'created_by' => $user->id
                ]);
            } else {
                // 更新客户信息
                $customer->update([
                    'name' => $request->input('customer_name'),
                    'id_card' => $request->input('customer_id_card'),
                    'id_card_front' => $request->input('id_card_front'),
                    'id_card_back' => $request->input('id_card_back'),
                    'nation' => $request->input('nation'),
                    'address' => $request->input('address'),
                    'authority' => $request->input('authority'),
                    'valid_start' => $request->input('valid_start'),
                    'valid_end' => $request->input('valid_end'),
                ]);
            }

            DB::commit();

            return $this->successHandler('基础资料保存成功', 200, [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'customer_phone' => $customer->phone,
                'customer_id_card' => $customer->id_card
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存基础资料失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 保存联系人信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveContactInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'contacts' => 'required|array|min:1',
                'contacts.*.name' => 'required|string|max:50',
                'contacts.*.relationship' => 'required|string',
                'contacts.*.phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'contacts.*.id_card' => 'nullable|string|size:18',
                'contacts.*.work_unit' => 'nullable|string',
                'contacts.*.address' => 'nullable|string',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'contacts.required' => '联系人信息不能为空',
                'contacts.min' => '至少需要一位联系人',
                'contacts.*.name.required' => '联系人姓名不能为空',
                'contacts.*.relationship.required' => '联系人关系不能为空',
                'contacts.*.phone.required' => '联系人手机号不能为空',
                'contacts.*.phone.regex' => '联系人手机号格式不正确',
                'contacts.*.id_card.size' => '联系人身份证号长度不正确',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找客户
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))->first();
            if (!$customer) {
                return $this->errorHandler('客户信息不存在，请先完成基础资料', 400);
            }

            // 删除现有联系人
            \App\Models\CustomerContact::where('customer_id', $customer->id)->delete();

            // 保存新的联系人信息
            $contacts = $request->input('contacts');
            foreach ($contacts as $contactData) {
                \App\Models\CustomerContact::create([
                    'customer_id' => $customer->id,
                    'name' => $contactData['name'],
                    'relationship' => $contactData['relationship'],
                    'phone' => $contactData['phone'],
                    'id_card' => $contactData['id_card'] ?? null,
                    'work_unit' => $contactData['work_unit'] ?? null,
                    'address' => $contactData['address'] ?? null,
                ]);
            }

            DB::commit();

            return $this->successHandler('联系人信息保存成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存联系人信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 保存资产信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveAssetInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'work_type' => 'nullable|string',
                'company_name' => 'nullable|string',
                'monthly_income' => 'required|numeric|min:0',
                'annual_income' => 'nullable|numeric|min:0',
                'has_property' => 'required|boolean',
                'property_count' => 'nullable|integer|min:0',
                'property_value' => 'nullable|numeric|min:0',
                'property_loan_balance' => 'nullable|numeric|min:0',
                'has_vehicle' => 'required|boolean',
                'vehicle_count' => 'nullable|integer|min:0',
                'vehicle_value' => 'nullable|numeric|min:0',
                'vehicle_loan_balance' => 'nullable|numeric|min:0',
                'other_assets' => 'nullable|numeric|min:0',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'monthly_income.required' => '月收入不能为空',
                'monthly_income.numeric' => '月收入必须为数字',
                'has_property.required' => '请选择是否有房产',
                'has_vehicle.required' => '请选择是否有车辆',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找客户
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))->first();
            if (!$customer) {
                return $this->errorHandler('客户信息不存在，请先完成基础资料', 400);
            }

            // 删除现有资产信息
            \App\Models\CustomerAsset::where('customer_id', $customer->id)->delete();

            // 计算总资产
            $totalAssets = 0;
            if ($request->input('has_property') && $request->input('property_value')) {
                $totalAssets += $request->input('property_value') - ($request->input('property_loan_balance') ?? 0);
            }
            if ($request->input('has_vehicle') && $request->input('vehicle_value')) {
                $totalAssets += $request->input('vehicle_value') - ($request->input('vehicle_loan_balance') ?? 0);
            }
            if ($request->input('other_assets')) {
                $totalAssets += $request->input('other_assets');
            }

            // 保存资产信息
            \App\Models\CustomerAsset::create([
                'customer_id' => $customer->id,
                'work_type' => $request->input('work_type'),
                'company_name' => $request->input('company_name'),
                'monthly_income' => $request->input('monthly_income'),
                'annual_income' => $request->input('annual_income'),
                'has_property' => $request->input('has_property'),
                'property_count' => $request->input('property_count') ?? 0,
                'property_value' => $request->input('property_value') ?? 0,
                'property_loan_balance' => $request->input('property_loan_balance') ?? 0,
                'has_vehicle' => $request->input('has_vehicle'),
                'vehicle_count' => $request->input('vehicle_count') ?? 0,
                'vehicle_value' => $request->input('vehicle_value') ?? 0,
                'vehicle_loan_balance' => $request->input('vehicle_loan_balance') ?? 0,
                'other_assets' => $request->input('other_assets') ?? 0,
                'total_assets' => $totalAssets,
            ]);

            DB::commit();

            return $this->successHandler('资产信息保存成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存资产信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 保存负债信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveLiabilityInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'has_credit_card' => 'required|boolean',
                'credit_card_limit' => 'nullable|numeric|min:0',
                'credit_card_used' => 'nullable|numeric|min:0',
                'has_bank_loan' => 'required|boolean',
                'bank_loan_balance' => 'nullable|numeric|min:0',
                'bank_loan_monthly' => 'nullable|numeric|min:0',
                'has_other_debt' => 'required|boolean',
                'other_debt_balance' => 'nullable|numeric|min:0',
                'other_debt_monthly' => 'nullable|numeric|min:0',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'has_credit_card.required' => '请选择是否有信用卡',
                'has_bank_loan.required' => '请选择是否有银行贷款',
                'has_other_debt.required' => '请选择是否有其他负债',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找客户
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))->first();
            if (!$customer) {
                return $this->errorHandler('客户信息不存在，请先完成基础资料', 400);
            }

            // 删除现有负债信息
            \App\Models\CustomerLiability::where('customer_id', $customer->id)->delete();

            // 计算总负债和月供
            $totalDebt = 0;
            $totalMonthlyPayment = 0;

            if ($request->input('has_credit_card') && $request->input('credit_card_used')) {
                $totalDebt += $request->input('credit_card_used');
            }
            if ($request->input('has_bank_loan') && $request->input('bank_loan_balance')) {
                $totalDebt += $request->input('bank_loan_balance');
                $totalMonthlyPayment += $request->input('bank_loan_monthly') ?? 0;
            }
            if ($request->input('has_other_debt') && $request->input('other_debt_balance')) {
                $totalDebt += $request->input('other_debt_balance');
                $totalMonthlyPayment += $request->input('other_debt_monthly') ?? 0;
            }

            // 保存负债信息
            \App\Models\CustomerLiability::create([
                'customer_id' => $customer->id,
                'has_credit_card' => $request->input('has_credit_card'),
                'credit_card_limit' => $request->input('credit_card_limit') ?? 0,
                'credit_card_used' => $request->input('credit_card_used') ?? 0,
                'has_bank_loan' => $request->input('has_bank_loan'),
                'bank_loan_balance' => $request->input('bank_loan_balance') ?? 0,
                'bank_loan_monthly' => $request->input('bank_loan_monthly') ?? 0,
                'has_other_debt' => $request->input('has_other_debt'),
                'other_debt_balance' => $request->input('other_debt_balance') ?? 0,
                'other_debt_monthly' => $request->input('other_debt_monthly') ?? 0,
                'total_debt' => $totalDebt,
                'total_monthly_payment' => $totalMonthlyPayment,
            ]);

            DB::commit();

            return $this->successHandler('负债信息保存成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存负债信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 保存车辆信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveVehicleInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'vehicle_type' => 'required|string',
                'brand' => 'required|string',
                'model' => 'required|string',
                'series' => 'nullable|string',
                'configuration' => 'nullable|string',
                'year' => 'required|integer|min:2000|max:' . (date('Y') + 1),
                'fuel_type' => 'nullable|string',
                'transmission' => 'nullable|string',
                'seats' => 'nullable|integer|min:2|max:50',
                'guide_price' => 'required|numeric|min:0',
                'discount_amount' => 'nullable|numeric|min:0',
                'actual_price' => 'required|numeric|min:0',
                'down_payment' => 'required|numeric|min:0',
                'loan_amount' => 'required|numeric|min:0',
                'dealer_name' => 'nullable|string',
                'dealer_contact' => 'nullable|string',
                'dealer_phone' => 'nullable|string',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'vehicle_type.required' => '车辆类型不能为空',
                'brand.required' => '品牌不能为空',
                'model.required' => '车型不能为空',
                'year.required' => '年份不能为空',
                'year.min' => '年份不能早于2000年',
                'year.max' => '年份不能超过明年',
                'guide_price.required' => '指导价不能为空',
                'actual_price.required' => '实际价格不能为空',
                'down_payment.required' => '首付金额不能为空',
                'loan_amount.required' => '贷款金额不能为空',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找客户
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))->first();
            if (!$customer) {
                return $this->errorHandler('客户信息不存在，请先完成基础资料', 400);
            }

            // 删除现有车辆信息
            \App\Models\CustomerVehicle::where('customer_id', $customer->id)->delete();

            // 保存车辆信息
            \App\Models\CustomerVehicle::create([
                'customer_id' => $customer->id,
                'vehicle_type' => $request->input('vehicle_type'),
                'brand' => $request->input('brand'),
                'model' => $request->input('model'),
                'series' => $request->input('series'),
                'configuration' => $request->input('configuration'),
                'year' => $request->input('year'),
                'fuel_type' => $request->input('fuel_type'),
                'transmission' => $request->input('transmission'),
                'seats' => $request->input('seats'),
                'guide_price' => $request->input('guide_price'),
                'discount_amount' => $request->input('discount_amount') ?? 0,
                'actual_price' => $request->input('actual_price'),
                'down_payment' => $request->input('down_payment'),
                'loan_amount' => $request->input('loan_amount'),
                'dealer_name' => $request->input('dealer_name'),
                'dealer_contact' => $request->input('dealer_contact'),
                'dealer_phone' => $request->input('dealer_phone'),
            ]);

            DB::commit();

            return $this->successHandler('车辆信息保存成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存车辆信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 保存附件信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveAttachmentInfo(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customer_phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'attachments' => 'required|array|min:1',
                'attachments.*.file_name' => 'required|string',
                'attachments.*.file_url' => 'required|string',
                'attachments.*.file_type' => 'required|string',
                'attachments.*.file_size' => 'required|integer',
                'attachments.*.category' => 'required|string',
                'attachments.*.is_required' => 'required|boolean',
            ], [
                'customer_phone.required' => '客户手机号不能为空',
                'attachments.required' => '附件不能为空',
                'attachments.min' => '至少需要上传一个附件',
                'attachments.*.file_name.required' => '文件名不能为空',
                'attachments.*.file_url.required' => '文件URL不能为空',
                'attachments.*.category.required' => '附件类别不能为空',
            ]);

            if ($validator->fails()) {
                return $this->errorHandler($validator->errors()->first(), 400);
            }

            DB::beginTransaction();

            // 查找客户
            $customer = \App\Models\Customer::where('phone', $request->input('customer_phone'))->first();
            if (!$customer) {
                return $this->errorHandler('客户信息不存在，请先完成基础资料', 400);
            }

            // 删除现有附件
            \App\Models\BusinessAttachment::where('customer_id', $customer->id)->delete();

            // 保存附件信息
            $attachments = $request->input('attachments');
            foreach ($attachments as $attachmentData) {
                \App\Models\BusinessAttachment::create([
                    'customer_id' => $customer->id,
                    'file_name' => $attachmentData['file_name'],
                    'file_url' => $attachmentData['file_url'],
                    'file_type' => $attachmentData['file_type'],
                    'file_size' => $attachmentData['file_size'],
                    'category' => $attachmentData['category'],
                    'is_required' => $attachmentData['is_required'],
                    'upload_time' => now(),
                ]);
            }

            DB::commit();

            return $this->successHandler('附件信息保存成功', 200);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('保存附件信息失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('保存失败，请重试', 500);
        }
    }

    /**
     * 获取业务草稿数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDraftData(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return $this->errorHandler('用户未登录', 401);
            }

            $customerPhone = $request->input('customer_phone');
            if (!$customerPhone) {
                return $this->errorHandler('客户手机号不能为空', 400);
            }

            // 查找客户及相关信息
            $customer = \App\Models\Customer::with([
                'contacts',
                'assets',
                'liabilities',
                'vehicles',
                'businessAttachments'
            ])->where('phone', $customerPhone)->first();

            if (!$customer) {
                return $this->successHandler('暂无草稿数据', 200, [
                    'has_data' => false,
                    'data' => []
                ]);
            }

            // 组装草稿数据
            $draftData = [
                'has_data' => true,
                'basic_completed' => !empty($customer->name) && !empty($customer->id_card),
                'contact_completed' => $customer->contacts->count() > 0,
                'assets_completed' => $customer->assets->count() > 0,
                'liabilities_completed' => $customer->liabilities->count() > 0,
                'vehicle_completed' => $customer->vehicles->count() > 0,
                'attachments_completed' => $customer->businessAttachments->count() > 0,
                'customer' => [
                    'name' => $customer->name,
                    'phone' => $customer->phone,
                    'id_card' => $customer->id_card,
                    'id_card_front' => $customer->id_card_front,
                    'id_card_back' => $customer->id_card_back,
                    'nation' => $customer->nation,
                    'address' => $customer->address,
                    'authority' => $customer->authority,
                    'valid_start' => $customer->valid_start,
                    'valid_end' => $customer->valid_end,
                ],
                'contacts' => $customer->contacts->map(function ($contact) {
                    return [
                        'name' => $contact->name,
                        'relationship' => $contact->relationship,
                        'phone' => $contact->phone,
                        'id_card' => $contact->id_card,
                        'work_unit' => $contact->work_unit,
                        'address' => $contact->address,
                    ];
                }),
                'assets' => $customer->assets->first(),
                'liabilities' => $customer->liabilities->first(),
                'vehicle' => $customer->vehicles->first(),
                'attachments' => $customer->businessAttachments->map(function ($attachment) {
                    return [
                        'file_name' => $attachment->file_name,
                        'file_url' => $attachment->file_url,
                        'file_type' => $attachment->file_type,
                        'file_size' => $attachment->file_size,
                        'category' => $attachment->category,
                        'category_text' => $attachment->category_text,
                        'is_required' => $attachment->is_required,
                        'upload_time' => $attachment->upload_time,
                    ];
                }),
            ];

            return $this->successHandler('获取草稿数据成功', 200, $draftData);

        } catch (\Exception $e) {
            \Log::error('获取草稿数据失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            return $this->errorHandler('获取草稿数据失败', 500);
        }
    }

    /**
     * 获取业务类型选项
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBusinessTypeOptions()
    {
        try {
            $options = BusinessApplication::getBusinessTypeOptions();
            
            return $this->successHandler('获取业务类型选项成功', 200, [
                'options' => $options,
                'list' => collect($options)->map(function ($text, $value) {
                    return [
                        'value' => $value,
                        'text' => $text
                    ];
                })->values()
            ]);

        } catch (\Exception $e) {
            \Log::error('获取业务类型选项失败: ' . $e->getMessage());
            return $this->errorHandler('获取业务类型选项失败', 500);
        }
    }
} 