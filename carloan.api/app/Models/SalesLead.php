<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class SalesLead extends BaseModel
{
    use SoftDeletes;

    protected $table = 'sales_leads';

    protected $fillable = [
        'lead_no',
        'user_id',
        'channel_code',
        'source_id',
        'source_detail',
        'customer_name',
        'customer_phone',
        'customer_id_card',
        'customer_age',
        'customer_gender',
        'customer_city',
        'customer_occupation',
        'customer_income',
        'intent_level',
        'intent_brand',
        'intent_model',
        'intent_price_min',
        'intent_price_max',
        'intent_loan_amount',
        'intent_loan_period',
        'purchase_timeline',
        'status',
        'status_text',
        'priority',
        'is_converted',
        'converted_application_id',
        'converted_time',
        'last_contact_time',
        'next_follow_time',
        'follow_count',
        'notes',
        'tags'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'source_id' => 'integer',
        'customer_age' => 'integer',
        'customer_gender' => 'integer',
        'customer_income' => 'decimal:2',
        'intent_price_min' => 'decimal:2',
        'intent_price_max' => 'decimal:2',
        'intent_loan_amount' => 'decimal:2',
        'intent_loan_period' => 'integer',
        'is_converted' => 'boolean',
        'converted_application_id' => 'integer',
        'follow_count' => 'integer',
        'converted_time' => 'datetime',
        'last_contact_time' => 'datetime',
        'next_follow_time' => 'datetime'
    ];

    protected $dates = [
        'converted_time',
        'last_contact_time',
        'next_follow_time',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 线索状态常量
    const STATUS_NEW = 'new';                    // 新线索
    const STATUS_CONTACTED = 'contacted';        // 已联系
    const STATUS_FOLLOW_UP = 'follow_up';        // 跟进中
    const STATUS_INTENT_STRONG = 'intent_strong'; // 强意向
    const STATUS_CONVERTED = 'converted';        // 已转化
    const STATUS_LOST = 'lost';                  // 已流失
    const STATUS_INVALID = 'invalid';            // 无效

    // 意向等级常量
    const INTENT_HIGH = 'high';                  // 高意向
    const INTENT_MEDIUM = 'medium';              // 中意向
    const INTENT_LOW = 'low';                    // 低意向

    // 优先级常量
    const PRIORITY_HIGH = 'high';                // 高优先级
    const PRIORITY_NORMAL = 'normal';            // 普通优先级
    const PRIORITY_LOW = 'low';                  // 低优先级

    // 购车时间常量
    const TIMELINE_IMMEDIATE = 'immediate';      // 立即
    const TIMELINE_WITHIN_MONTH = 'within_month'; // 一个月内
    const TIMELINE_WITHIN_QUARTER = 'within_quarter'; // 三个月内
    const TIMELINE_LATER = 'later';              // 三个月后

    /**
     * 关联业务员
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联渠道
     */
    public function channel()
    {
        return $this->belongsTo(Channel::class, 'channel_code', 'code');
    }

    /**
     * 关联线索来源
     */
    public function source()
    {
        return $this->belongsTo(LeadSource::class, 'source_id');
    }

    /**
     * 关联转化的业务申请
     */
    public function convertedApplication()
    {
        return $this->belongsTo(BusinessApplication::class, 'converted_application_id');
    }

    /**
     * 关联跟进记录
     */
    public function followUps()
    {
        return $this->hasMany(SalesLeadFollowUp::class, 'lead_id');
    }

    /**
     * 获取最新跟进记录
     */
    public function latestFollowUp()
    {
        return $this->hasOne(SalesLeadFollowUp::class, 'lead_id')->latest();
    }

    /**
     * 生成线索编号
     */
    public static function generateLeadNo()
    {
        do {
            $leadNo = 'LD' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('lead_no', $leadNo)->exists());

        return $leadNo;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute($value)
    {
        if ($value) {
            return $value;
        }

        $statusTexts = [
            self::STATUS_NEW => '新线索',
            self::STATUS_CONTACTED => '已联系',
            self::STATUS_FOLLOW_UP => '跟进中',
            self::STATUS_INTENT_STRONG => '强意向',
            self::STATUS_CONVERTED => '已转化',
            self::STATUS_LOST => '已流失',
            self::STATUS_INVALID => '无效',
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 获取意向等级文本
     */
    public function getIntentLevelTextAttribute()
    {
        $levels = [
            self::INTENT_HIGH => '高意向',
            self::INTENT_MEDIUM => '中意向',
            self::INTENT_LOW => '低意向',
        ];

        return $levels[$this->intent_level] ?? '未知';
    }

    /**
     * 获取优先级文本
     */
    public function getPriorityTextAttribute()
    {
        $priorities = [
            self::PRIORITY_HIGH => '高优先级',
            self::PRIORITY_NORMAL => '普通',
            self::PRIORITY_LOW => '低优先级',
        ];

        return $priorities[$this->priority] ?? '普通';
    }

    /**
     * 获取购车时间文本
     */
    public function getPurchaseTimelineTextAttribute()
    {
        $timelines = [
            self::TIMELINE_IMMEDIATE => '立即',
            self::TIMELINE_WITHIN_MONTH => '一个月内',
            self::TIMELINE_WITHIN_QUARTER => '三个月内',
            self::TIMELINE_LATER => '三个月后',
        ];

        return $timelines[$this->purchase_timeline] ?? '未明确';
    }

    /**
     * 转化为业务申请
     */
    public function convertToApplication($applicationData)
    {
        try {
            \DB::beginTransaction();

            // 创建业务申请
            $application = BusinessApplication::create(array_merge($applicationData, [
                'application_no' => BusinessApplication::generateApplicationNo(),
                'user_id' => $this->user_id,
                'channel_code' => $this->channel_code,
                'customer_name' => $this->customer_name,
                'customer_phone' => $this->customer_phone,
                'customer_id_card' => $this->customer_id_card,
                'status' => BusinessApplication::STATUS_SUBMITTED,
                'submit_time' => now(),
            ]));

            // 更新线索状态
            $this->update([
                'status' => self::STATUS_CONVERTED,
                'status_text' => '已转化',
                'is_converted' => true,
                'converted_application_id' => $application->id,
                'converted_time' => now(),
            ]);

            // 记录跟进
            $this->followUps()->create([
                'user_id' => $this->user_id,
                'follow_type' => 'other',
                'follow_result' => 'success',
                'content' => '线索转化为业务申请，申请单号：' . $application->application_no,
            ]);

            \DB::commit();

            return $application;

        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * 范围查询：指定用户
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：指定状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 范围查询：新线索
     */
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    /**
     * 范围查询：跟进中
     */
    public function scopeFollowUp($query)
    {
        return $query->whereIn('status', [self::STATUS_CONTACTED, self::STATUS_FOLLOW_UP]);
    }

    /**
     * 范围查询：强意向
     */
    public function scopeStrongIntent($query)
    {
        return $query->where('status', self::STATUS_INTENT_STRONG);
    }

    /**
     * 范围查询：已转化
     */
    public function scopeConverted($query)
    {
        return $query->where('is_converted', true);
    }

    /**
     * 范围查询：待跟进
     */
    public function scopePendingFollow($query)
    {
        return $query->whereNotIn('status', [self::STATUS_CONVERTED, self::STATUS_LOST, self::STATUS_INVALID])
            ->where(function ($q) {
                $q->whereNull('next_follow_time')
                  ->orWhere('next_follow_time', '<=', now());
            });
    }

    /**
     * 范围查询：高优先级
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', self::PRIORITY_HIGH);
    }

    /**
     * 范围查询：最近创建
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 范围查询：搜索客户信息
     */
    public function scopeSearchCustomer($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('customer_name', 'like', "%{$keyword}%")
              ->orWhere('customer_phone', 'like', "%{$keyword}%")
              ->orWhere('customer_id_card', 'like', "%{$keyword}%");
        });
    }
} 