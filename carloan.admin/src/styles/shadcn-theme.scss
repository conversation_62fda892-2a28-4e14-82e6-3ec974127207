/* ===== Shadcn/ui Theme Configuration ===== */
/* Modern design system inspired by shadcn/ui with black primary color */

:root {
  /* Primary colors */
  --color-primary: #000000;
  --color-primary-foreground: #ffffff;

  /* Secondary colors */
  --color-secondary: #f8f8f8;
  --color-secondary-foreground: #171717;

  /* Muted colors */
  --color-muted: #f5f5f5;
  --color-muted-foreground: #525252;

  /* Accent colors */
  --color-accent: #f8f8f8;
  --color-accent-foreground: #171717;

  /* Destructive colors */
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;

  /* Border colors */
  --color-border: #e5e5e5;
  --color-input: #e5e5e5;
  --color-ring: #000000;

  /* Background colors */
  --color-background: #ffffff;
  --color-foreground: #0a0a0a;

  /* Card colors */
  --color-card: #ffffff;
  --color-card-foreground: #0a0a0a;

  /* Popover colors */
  --color-popover: #ffffff;
  --color-popover-foreground: #0a0a0a;

  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Global typography */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--color-foreground);
  background-color: var(--color-background);
  line-height: 1.5;
}

/* Utility classes for shadcn/ui style */
.shadcn-card {
  background-color: var(--color-card);
  color: var(--color-card-foreground);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.shadcn-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;

  &.shadcn-button--primary {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);

    &:hover {
      background-color: rgba(0, 0, 0, 0.9);
    }
  }

  &.shadcn-button--secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
    border-color: var(--color-border);

    &:hover {
      background-color: var(--color-accent);
    }
  }

  &.shadcn-button--destructive {
    background-color: var(--color-destructive);
    color: var(--color-destructive-foreground);

    &:hover {
      background-color: rgba(239, 68, 68, 0.9);
    }
  }

  &.shadcn-button--outline {
    background-color: transparent;
    border-color: var(--color-border);
    color: var(--color-foreground);

    &:hover {
      background-color: var(--color-accent);
    }
  }

  &.shadcn-button--ghost {
    background-color: transparent;
    color: var(--color-foreground);

    &:hover {
      background-color: var(--color-accent);
    }
  }
}

.shadcn-input {
  display: flex;
  height: 40px;
  width: 100%;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-input);
  background-color: var(--color-background);
  padding: 8px 12px;
  font-size: 14px;
  transition: all 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: var(--color-ring);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  }

  &::placeholder {
    color: var(--color-muted-foreground);
  }
}

.shadcn-badge {
  display: inline-flex;
  align-items: center;
  border-radius: var(--radius-sm);
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;

  &.shadcn-badge--default {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  &.shadcn-badge--secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
  }

  &.shadcn-badge--destructive {
    background-color: var(--color-destructive);
    color: var(--color-destructive-foreground);
  }

  &.shadcn-badge--outline {
    background-color: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-foreground);
  }
}

/* Enhanced focus styles */
.shadcn-focus-visible {
  &:focus-visible {
    outline: 2px solid var(--color-ring);
    outline-offset: 2px;
  }
}

/* Modern scrollbar */
.shadcn-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--color-muted);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--color-muted-foreground);
    border-radius: 3px;

    &:hover {
      background-color: var(--color-foreground);
    }
  }
}
