import request from '@/utils/request'

// 权限码管理
export function fetchPermissionCodeList(query) {
  return request({
    url: '/permission-codes',
    method: 'get',
    params: query
  })
}

export function fetchPermissionCode(id) {
  return request({
    url: `/permission-codes/${id}`,
    method: 'get'
  })
}

export function createPermissionCode(data) {
  return request({
    url: '/permission-codes',
    method: 'post',
    data
  })
}

export function updatePermissionCode(id, data) {
  return request({
    url: `/permission-codes/${id}`,
    method: 'put',
    data
  })
}

export function deletePermissionCode(id) {
  return request({
    url: `/permission-codes/${id}`,
    method: 'delete'
  })
}

export function fetchPermissionGroups() {
  return request({
    url: '/permission-codes/groups',
    method: 'get'
  })
}

export function togglePermissionCodeStatus(id, status) {
  return request({
    url: `/permission-codes/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function batchImportPermissionCodes(data) {
  return request({
    url: '/permission-codes/batch-import',
    method: 'post',
    data
  })
}

// 角色管理
export function fetchRoleList(query) {
  return request({
    url: '/roles',
    method: 'get',
    params: query
  })
}

export function fetchRole(id) {
  return request({
    url: `/roles/${id}`,
    method: 'get'
  })
}

export function createRole(data) {
  return request({
    url: '/roles',
    method: 'post',
    data
  })
}

export function updateRole(id, data) {
  return request({
    url: `/roles/${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/roles/${id}`,
    method: 'delete'
  })
}

export function fetchRolePermissions() {
  return request({
    url: '/roles/permissions',
    method: 'get'
  })
}

export function fetchRoleTypes() {
  return request({
    url: '/roles/types',
    method: 'get'
  })
}

export function toggleRoleStatus(id, status) {
  return request({
    url: `/roles/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function copyRole(id, data) {
  return request({
    url: `/roles/${id}/copy`,
    method: 'post',
    data
  })
}

// 管理员管理
export function fetchAdminList(query) {
  return request({
    url: '/admins',
    method: 'get',
    params: query
  })
}

export function fetchAdmin(id) {
  return request({
    url: `/admins/${id}`,
    method: 'get'
  })
}

export function createAdmin(data) {
  return request({
    url: '/admins',
    method: 'post',
    data
  })
}

export function updateAdmin(id, data) {
  return request({
    url: `/admins/${id}`,
    method: 'put',
    data
  })
}

export function deleteAdmin(id) {
  return request({
    url: `/admins/${id}`,
    method: 'delete'
  })
}

export function changeAdminPassword(id, data) {
  return request({
    url: `/admins/${id}/password`,
    method: 'put',
    data
  })
}

export function fetchAdminRoles() {
  return request({
    url: '/admins/roles',
    method: 'get'
  })
}

export function toggleAdminStatus(id, status) {
  return request({
    url: `/admins/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 业务员管理
export function fetchSystemUserList(query) {
  return request({
    url: '/system-users',
    method: 'get',
    params: query
  })
}

export function fetchSystemUser(id) {
  return request({
    url: `/system-users/${id}`,
    method: 'get'
  })
}

export function createSystemUser(data) {
  return request({
    url: '/system-users',
    method: 'post',
    data
  })
}

export function updateSystemUser(id, data) {
  return request({
    url: `/system-users/${id}`,
    method: 'put',
    data
  })
}

export function deleteSystemUser(id) {
  return request({
    url: `/system-users/${id}`,
    method: 'delete'
  })
}

export function changeSystemUserPassword(id, data) {
  return request({
    url: `/system-users/${id}/password`,
    method: 'put',
    data
  })
}

export function fetchSystemUserRoles() {
  return request({
    url: '/system-users/roles',
    method: 'get'
  })
}

export function fetchSystemUserChannels() {
  return request({
    url: '/system-users/channels',
    method: 'get'
  })
}

export function toggleSystemUserStatus(id, status) {
  return request({
    url: `/system-users/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function setSystemUserAdminPermission(id, data) {
  return request({
    url: `/system-users/${id}/admin-permission`,
    method: 'put',
    data
  })
}

export function fetchAdminUsers() {
  return request({
    url: '/system-users/admin-users',
    method: 'get'
  })
}
